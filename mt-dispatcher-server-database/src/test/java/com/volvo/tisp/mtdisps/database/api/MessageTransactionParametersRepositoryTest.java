package com.volvo.tisp.mtdisps.database.api;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.data.mongo.DataMongoTest;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;

@DataMongoTest
@Testcontainers
class MessageTransactionParametersRepositoryTest {
  @Container
  static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:6.0");
  private static final String ASSET_HARDWARE_ID = "0123456-789101112";
  private static final String CORRELATION_ID = "f739a254-0560-11ee-be56-0242ac120002";
  private static final String CORRELATION_ID_2 = "a254f739-be56-11ee-2xd4-7642ac123456";
  private static final Instant EXPIRE_AT = Instant.now(Clock.systemUTC()).plus(5, TimeUnit.MINUTES.toChronoUnit());
  private static final String SERVICE_FUNCTION = "TEST_SERVICE";
  private static final int SERVICE_ID = 42;
  private static final int SERVICE_VERSION = 43;
  private static final int TTL = 60;
  private static final String VPI = "1FAC35BF1EDB1DFF91AAC0BEBC56F22B";

  @Autowired
  private CacheManager cacheManager;
  @Autowired
  private MessageTransactionParametersRepository messageTransactionParametersRepository;

  @DynamicPropertySource
  static void setProperties(DynamicPropertyRegistry registry) {
    registry.add("spring.data.mongodb.uri", mongoDBContainer::getReplicaSetUrl);
    registry.add("mongock.migration-scan-package", () -> "com.volvo.tisp.mtdisps.database.migration");
  }

  private static void verifyMessageTransactionParameters(MessageTransactionParametersEntity expected, MessageTransactionParametersEntity actual) {
    Assertions.assertEquals(expected.getCorrelationId(), actual.getCorrelationId());
    Assertions.assertEquals(expected.getExpireAt(), actual.getExpireAt());
    Assertions.assertEquals(expected.getTtl(), actual.getTtl());
  }

  private static void verifyMessageTransactionParameters(MessageTransactionParametersEntity messageTransactionParametersEntity) {
    Assertions.assertEquals(CORRELATION_ID, messageTransactionParametersEntity.getCorrelationId());
    Assertions.assertEquals(EXPIRE_AT, messageTransactionParametersEntity.getExpireAt());
    Assertions.assertEquals(TTL, messageTransactionParametersEntity.getTtl());
    Assertions.assertEquals(SERVICE_FUNCTION, messageTransactionParametersEntity.getServiceFunction());
    Assertions.assertEquals(SERVICE_ID, messageTransactionParametersEntity.getServiceId());
    Assertions.assertEquals(SERVICE_VERSION, messageTransactionParametersEntity.getServiceVersion());
    Assertions.assertEquals(VPI, messageTransactionParametersEntity.getVpi());
  }

  private static void verifyUpdatedMessageTransactionParameters(MessageTransactionParametersEntity messageTransactionParametersEntity) {
    Assertions.assertEquals(CORRELATION_ID, messageTransactionParametersEntity.getCorrelationId());
    Assertions.assertTrue(messageTransactionParametersEntity.getExpireAt().compareTo(Instant.now(Clock.systemUTC())) <= 0);
    Assertions.assertEquals(TTL, messageTransactionParametersEntity.getTtl());
    Assertions.assertEquals(SERVICE_FUNCTION, messageTransactionParametersEntity.getServiceFunction());
    Assertions.assertEquals(SERVICE_ID, messageTransactionParametersEntity.getServiceId());
    Assertions.assertEquals(SERVICE_VERSION, messageTransactionParametersEntity.getServiceVersion());
    Assertions.assertEquals(VPI, messageTransactionParametersEntity.getVpi());
  }

  @AfterEach
  void afterEachTest() {
    messageTransactionParametersRepository.deleteById(CORRELATION_ID);
    evictCacheByCorrelationId(CORRELATION_ID);
  }

  @BeforeEach
  void beforeEachTest() {
    insertMessageTransactionParameters(CORRELATION_ID);
  }

  @Test
  void cacheTtlTest() {
    messageTransactionParametersRepository.findById(CORRELATION_ID);
    Assertions.assertTrue(getCachedMessageTransactionParameters(CORRELATION_ID).isPresent());

    Awaitility.await()
        .atMost(Duration.ofSeconds(2))
        .until(() -> getCachedMessageTransactionParameters(CORRELATION_ID).isEmpty()); // cache ttl set to 1 sec, verify ttl eviction
  }

  @Test
  void countRecordsBeforeCurrentTime() {
    for (int i = 0; i < 10; i++) {
      insertMessageTransactionParameters(CORRELATION_ID + i);
    }
    long countRecordsBeforeCurrentTime = messageTransactionParametersRepository.countRecordsBeforeCurrentTime(
        Instant.now(Clock.systemUTC()).plus(6, TimeUnit.MINUTES.toChronoUnit()));
    Assertions.assertEquals(11, countRecordsBeforeCurrentTime);
  }

  @Test
  void findByIdTest() {
    Assertions.assertTrue(getCachedMessageTransactionParameters(CORRELATION_ID).isPresent());
    verifyMessageTransactionParameters(getCachedMessageTransactionParameters(CORRELATION_ID).get());//verify cache present
    Optional<MessageTransactionParametersEntity> mightBeCachedResponse = messageTransactionParametersRepository.findById(CORRELATION_ID);
    verifyMessageTransactionParameters(mightBeCachedResponse.get(), messageTransactionParametersRepository.findById(CORRELATION_ID).get()); //cached vs db
  }

  @Test
  void insertTest() {
    Assertions.assertTrue(getCachedMessageTransactionParameters(CORRELATION_ID).isPresent());
    verifyMessageTransactionParameters(getCachedMessageTransactionParameters(CORRELATION_ID).get()); //verify cache present
    Optional<MessageTransactionParametersEntity> messageTransactionParametersEntity = messageTransactionParametersRepository.findById(
        CORRELATION_ID); //cache evicted
    verifyMessageTransactionParameters(messageTransactionParametersEntity.get(),
        messageTransactionParametersRepository.findById(CORRELATION_ID).get()); //cached vs db
    //cleanup
    messageTransactionParametersRepository.deleteById(CORRELATION_ID_2);
    evictCacheByCorrelationId(CORRELATION_ID_2);
  }

  @Test
  void saveTest() {
    Assertions.assertTrue(getCachedMessageTransactionParameters(CORRELATION_ID).isPresent());
    verifyMessageTransactionParameters(getCachedMessageTransactionParameters(CORRELATION_ID).get()); //verify cache present
    Optional<MessageTransactionParametersEntity> messageTransactionParametersEntity = messageTransactionParametersRepository.findById(
        CORRELATION_ID);
    updateMessageTransactionParameters(messageTransactionParametersEntity.get()); //cache evicted
    verifyNoCacheForCorrelationId(CORRELATION_ID); //verify cache evicted
    verifyUpdatedMessageTransactionParameters(messageTransactionParametersRepository.findById(CORRELATION_ID).get()
    );//findById will be db hit, verify updated
  }

  private void evictCacheByCorrelationId(String correlationId) {
    cacheManager.getCache(MessageTransactionParametersRepository.MESSAGE_TRANSACTION_PARAMETERS_CACHE).evictIfPresent(correlationId);
  }

  private Optional<MessageTransactionParametersEntity> getCachedMessageTransactionParameters(String correlationId) {
    return Optional.ofNullable(cacheManager.getCache(MessageTransactionParametersRepository.MESSAGE_TRANSACTION_PARAMETERS_CACHE))
        .map(cache -> cache.get(correlationId, MessageTransactionParametersEntity.class));
  }

  private void insertMessageTransactionParameters(String correlationId) {
    MessageTransactionParametersEntity messageTransactionParametersEntity = new MessageTransactionParametersEntity();
    messageTransactionParametersEntity.setCorrelationId(correlationId);
    messageTransactionParametersEntity.setExpireAt(EXPIRE_AT);
    messageTransactionParametersEntity.setTtl(TTL);
    messageTransactionParametersEntity.setAssetHardwareId(ASSET_HARDWARE_ID);
    messageTransactionParametersEntity.setServiceFunction(SERVICE_FUNCTION);
    messageTransactionParametersEntity.setServiceId(SERVICE_ID);
    messageTransactionParametersEntity.setServiceVersion(SERVICE_VERSION);
    messageTransactionParametersEntity.setVpi(VPI);
    messageTransactionParametersEntity.setMtMessageType("INTERNAL_MT_MESSAGE");

    messageTransactionParametersRepository.insert(messageTransactionParametersEntity);
  }

  private void updateMessageTransactionParameters(MessageTransactionParametersEntity messageTransactionParametersEntity) {
    messageTransactionParametersEntity.setExpireAt(Instant.now(Clock.systemUTC()));

    messageTransactionParametersRepository.save(messageTransactionParametersEntity);
  }

  private void verifyNoCacheForCorrelationId(String correlationId) {
    Assertions.assertEquals(Optional.empty(), getCachedMessageTransactionParameters(correlationId));
  }
}
