package com.volvo.tisp.mtdisps.database.api;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.data.mongo.DataMongoTest;
import org.springframework.cache.CacheManager;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;

@DataMongoTest
@Testcontainers
class VehicleInformationRepositoryTest {
  @Container
  static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:6.0");
  private static final String CHASSIS_ID = "SCAR202450";
  private static final boolean NON_OPERATIONAL = false;
  private static final boolean OPERATIONAL = true;
  private static final String PART_NUMBER = "testPartNumber";
  private static final String SERIAL_NUMBER = "testSerialNumber";
  private static final boolean SOFTCAR = false;
  private static final String TELEMATIC_UNIT_SUB_TYPE = "TGW3";
  private static final TelematicUnitType TELEMATIC_UNIT_TYPE = TelematicUnitType.TGW;
  private static final String VPI = "1FAC35BF1EDB1DFF91AAC0BEBC56F22B";
  private static final String VPI_2 = "56F22B1FAC35BF1EDB1DFF91AAC0BEBC";
  private static final String VPI_3 = "BF1EDB1DFF56F22B1FAC3591AAC0BEBC";

  @Autowired
  private CacheManager cacheManager;
  @Autowired
  private VehicleInformationRepository vehicleInformationRepository;

  @DynamicPropertySource
  static void setProperties(DynamicPropertyRegistry registry) {
    registry.add("spring.data.mongodb.uri", mongoDBContainer::getReplicaSetUrl);
    registry.add("mongock.migration-scan-package", () -> "com.volvo.tisp.mtdisps.database.migration");
  }

  private static VehicleInformationEntity createVehicleInformationEntity(String vpi, boolean operational) {
    VehicleInformationEntity vehicleInformationEntity = new VehicleInformationEntity();

    vehicleInformationEntity.setChassisId(CHASSIS_ID);
    vehicleInformationEntity.setOperational(operational);
    vehicleInformationEntity.setPartNumber(PART_NUMBER);
    vehicleInformationEntity.setSerialNumber(SERIAL_NUMBER);
    vehicleInformationEntity.setTelematicUnitType(TELEMATIC_UNIT_TYPE);
    vehicleInformationEntity.setTelematicUnitSubType(TELEMATIC_UNIT_SUB_TYPE);
    vehicleInformationEntity.setVersion(1);
    vehicleInformationEntity.setVpi(vpi);
    return vehicleInformationEntity;
  }

  private static void verifyVehicleInformation(VehicleInformationEntity vehicleInformationEntity, boolean operational) {
    Assertions.assertNotNull(vehicleInformationEntity.getCreatedAt());
    Assertions.assertEquals(CHASSIS_ID, vehicleInformationEntity.getChassisId());
    Assertions.assertEquals(operational, vehicleInformationEntity.isOperational());
    Assertions.assertEquals(SOFTCAR, vehicleInformationEntity.isSoftCar());
    Assertions.assertEquals(PART_NUMBER, vehicleInformationEntity.getPartNumber());
    Assertions.assertEquals(SERIAL_NUMBER, vehicleInformationEntity.getSerialNumber());
    Assertions.assertEquals(TELEMATIC_UNIT_TYPE, vehicleInformationEntity.getTelematicUnitType());
    Assertions.assertEquals(TELEMATIC_UNIT_SUB_TYPE, vehicleInformationEntity.getTelematicUnitSubType());
    Assertions.assertEquals(VPI, vehicleInformationEntity.getVpi());
    Assertions.assertNotNull(vehicleInformationEntity.getUpdatedAt());
  }

  private static void verifyVehicleInformation(VehicleInformationEntity expected, VehicleInformationEntity actual) {
    Assertions.assertEquals(expected.getChassisId(), actual.getChassisId());
    Assertions.assertEquals(expected.getCreatedAt(), actual.getCreatedAt());
    Assertions.assertEquals(expected.isOperational(), actual.isOperational());
    Assertions.assertEquals(expected.isSoftCar(), actual.isSoftCar());
    Assertions.assertEquals(expected.getPartNumber(), actual.getPartNumber());
    Assertions.assertEquals(expected.getSerialNumber(), actual.getSerialNumber());
    Assertions.assertEquals(expected.getTelematicUnitType(), actual.getTelematicUnitType());
    Assertions.assertEquals(expected.getTelematicUnitSubType(), actual.getTelematicUnitSubType());
    Assertions.assertEquals(expected.getVpi(), actual.getVpi());
    Assertions.assertEquals(expected.getUpdatedAt(), actual.getUpdatedAt());
  }

  @AfterEach
  void afterEachTest() {
    vehicleInformationRepository.deleteAll();
  }

  @BeforeEach
  void beforeEachTest() {
    insertVehicleInformation(VPI, OPERATIONAL);
  }

  @Test
  void cacheTtlTest() {
    vehicleInformationRepository.findByVpiAndOperationalIsTrue(VPI);
    getCachedVehicleInformationByVpi(VPI).orElseThrow();
    Awaitility.await()
        .atMost(Duration.ofSeconds(2))
        .until(() -> getCachedVehicleInformationByVpi(VPI).isEmpty()); // cache ttl set to 1 sec, verify ttl eviction
  }

  @Test
  void deleteAllTest() {
    List<VehicleInformationEntity> vehicleInformationEntity1 = List.of(createVehicleInformationEntity(VPI_2, true),
        createVehicleInformationEntity(VPI_3, true));
    vehicleInformationRepository.saveAll(vehicleInformationEntity1);

    vehicleInformationRepository.deleteAll(vehicleInformationEntity1);
    Assertions.assertTrue(vehicleInformationRepository.findByVpi(VPI_2).isEmpty());
    Assertions.assertTrue(vehicleInformationRepository.findByVpi(VPI_3).isEmpty());
  }

  @Test
  void deleteByVpiTest() {
    vehicleInformationRepository.findByVpiAndOperationalIsTrue(VPI); //result cached

    vehicleInformationRepository.deleteByVpi(VPI);
    Assertions.assertEquals(Optional.empty(), vehicleInformationRepository.findByVpiAndOperationalIsTrue(VPI));//no result to cache
    verifyNoCacheForVPI(VPI);//confirms cache evicted
  }

  @Test
  void findByChassisIdAndOperationalStatusTest() {
    Optional<VehicleInformationEntity> byChassisId = vehicleInformationRepository.findByChassisIdAndOperationalIsTrue(CHASSIS_ID); // cached
    verifyVehicleInformation(byChassisId.orElseThrow(), OPERATIONAL);
  }

  @Test
  void findByChassisIdTest() {
    Optional<VehicleInformationEntity> optional1 = vehicleInformationRepository.findByChassisId(CHASSIS_ID);
    verifyVehicleInformation(optional1.orElseThrow(), OPERATIONAL);

    updateVehicleInformation(optional1.orElseThrow(), false);

    Optional<VehicleInformationEntity> optional2 = vehicleInformationRepository.findByChassisId(CHASSIS_ID);
    verifyVehicleInformation(optional2.orElseThrow(), NON_OPERATIONAL);
  }

  @Test
  void findByVpiAndOperationalStatusTest() {
    verifyNoCacheForVPI(VPI);
    Optional<VehicleInformationEntity> byVpiAndOperationalStatus = vehicleInformationRepository.findByVpiAndOperationalIsTrue(VPI);
    verifyVehicleInformation(byVpiAndOperationalStatus.orElseThrow(), OPERATIONAL);
    verifyVehicleInformation(getCachedVehicleInformationByVpi(VPI).orElseThrow(), OPERATIONAL);//verify cache present

    evictCacheByVpi(VPI);
    verifyNoCacheForVPI(VPI);
    Optional<VehicleInformationEntity> byVpiAndOperationalStatus2 = vehicleInformationRepository.findByVpiAndOperationalIsTrue(VPI);
    verifyVehicleInformation(byVpiAndOperationalStatus2.orElseThrow(), OPERATIONAL);
    verifyVehicleInformation(getCachedVehicleInformationByVpi(VPI).orElseThrow(), OPERATIONAL);

    Optional<VehicleInformationEntity> byVpiAndOperationalStatus3 = vehicleInformationRepository.findByVpiAndOperationalIsTrue(VPI); //Should be from cache
    verifyVehicleInformation(getCachedVehicleInformationByVpi(VPI).orElseThrow(), byVpiAndOperationalStatus3.orElseThrow());
  }

  @Test
  void findByVpiTest() {
    verifyNoCacheForVPI(VPI);
    Optional<VehicleInformationEntity> byVpiAndOperationalStatus = vehicleInformationRepository.findByVpi(VPI); // should not be cached
    verifyVehicleInformation(byVpiAndOperationalStatus.orElseThrow(), OPERATIONAL);
    verifyNoCacheForVPI(VPI);//verify No cache present
  }

  @Test
  void multipleAssetsWithSameChassisIdOrAssetHardwareIdTest() {
    insertVehicleInformation(VPI_2, true);
    Assertions.assertThrows(IncorrectResultSizeDataAccessException.class, () -> vehicleInformationRepository.findByChassisId(CHASSIS_ID));
    Assertions.assertThrows(IncorrectResultSizeDataAccessException.class,
        () -> vehicleInformationRepository.findByPartNumberAndSerialNumber(PART_NUMBER, SERIAL_NUMBER));
  }

  @Test
  void saveAllTest() {
    List<VehicleInformationEntity> vehicleInformationEntity1 = List.of(createVehicleInformationEntity(VPI_2, true),
        createVehicleInformationEntity(VPI_3, true));
    vehicleInformationRepository.saveAll(vehicleInformationEntity1);

    Assertions.assertTrue(vehicleInformationRepository.findByVpi(VPI_2).isPresent());
    Assertions.assertTrue(vehicleInformationRepository.findByVpi(VPI_3).isPresent());

    //cleanup
    vehicleInformationRepository.deleteByVpi(VPI_2);
    vehicleInformationRepository.deleteByVpi(VPI_3);
  }

  @Test
  void saveTest() {
    Optional<VehicleInformationEntity> vehicleInformationEntityByVpi = vehicleInformationRepository.findByVpiAndOperationalIsTrue(VPI);//result cached by vpi
    vehicleInformationRepository.findByChassisIdAndOperationalIsTrue(CHASSIS_ID);//result cached by chassisId

    verifyVehicleInformation(getCachedVehicleInformationByVpi(VPI).orElseThrow(), true);  //confirms cache exist for vpi

    updateVehicleInformation(vehicleInformationEntityByVpi.orElseThrow(), false); //update record should evict both cache
    Assertions.assertEquals(Optional.empty(), getCachedVehicleInformationByVpi(VPI));//confirms vpi cache evicted
    verifyVehicleInformation(vehicleInformationRepository.findByVpi(VPI).orElseThrow(), false);
  }

  @Test
  void verifyOperationalStatusQueryTest() {
    VehicleInformationEntity vehicleInformationEntity = vehicleInformationRepository.findByVpi(VPI).orElseThrow();
    updateVehicleInformation(vehicleInformationEntity, false);
    Assertions.assertEquals(Optional.empty(), getCachedVehicleInformationByVpi(VPI));
    Assertions.assertTrue(vehicleInformationRepository.findByChassisIdAndOperationalIsTrue(CHASSIS_ID).isEmpty()); // operational is false so it should be empty
    Assertions.assertTrue(vehicleInformationRepository.findByVpiAndOperationalIsTrue(VPI).isEmpty()); // operational is false so it should be empty
    verifyVehicleInformation(vehicleInformationRepository.findByVpi(VPI).orElseThrow(), false);
  }

  private void evictCacheByVpi(String vpi) {
    cacheManager.getCache(VehicleInformationRepository.OPERATIONAL_VEHICLE_INFORMATION_BY_VPI_CACHE).evictIfPresent(vpi);
  }

  private Optional<VehicleInformationEntity> getCachedVehicleInformationByVpi(String vpi) {
    return Optional.ofNullable(cacheManager.getCache(VehicleInformationRepository.OPERATIONAL_VEHICLE_INFORMATION_BY_VPI_CACHE))
        .map(cache -> cache.get(vpi, VehicleInformationEntity.class));
  }

  private void insertVehicleInformation(String vpi, boolean operational) {
    vehicleInformationRepository.save(createVehicleInformationEntity(vpi, operational));
  }

  private void updateVehicleInformation(VehicleInformationEntity vehicleInformationEntity, boolean operational) {
    vehicleInformationEntity.setOperational(operational);

    vehicleInformationRepository.save(vehicleInformationEntity);
  }

  private void verifyNoCacheForVPI(String vpi) {
    Assertions.assertEquals(Optional.empty(), getCachedVehicleInformationByVpi(vpi));
  }
}
