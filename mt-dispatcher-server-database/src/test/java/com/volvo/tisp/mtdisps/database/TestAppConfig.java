package com.volvo.tisp.mtdisps.database;

import java.util.concurrent.TimeUnit;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.github.benmanes.caffeine.cache.Caffeine;

import io.mongock.driver.api.driver.ConnectionDriver;
import io.mongock.driver.mongodb.springdata.v4.SpringDataMongoV4Driver;
import io.mongock.runner.springboot.EnableMongock;

@SpringBootApplication
@EnableMongoRepositories("com.volvo.tisp.mtdisps.database")
@EnableMongoAuditing
@EnableMongock
@EnableCaching
@EnableTransactionManagement
public class TestAppConfig {

  @Bean
  public CacheManager cacheManager(Caffeine caffeine) {
    CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
    caffeineCacheManager.setCaffeine(caffeine);
    return caffeineCacheManager;
  }

  @Bean
  public Caffeine caffeineConfig() {
    return Caffeine.newBuilder().expireAfterWrite(1, TimeUnit.SECONDS);
  }

  @Bean
  public ConnectionDriver mongockConnection(MongoTemplate mongoTemplate) {
    return SpringDataMongoV4Driver.withDefaultLock(mongoTemplate);
  }
}
