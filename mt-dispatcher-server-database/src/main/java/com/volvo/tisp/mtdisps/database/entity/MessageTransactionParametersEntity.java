package com.volvo.tisp.mtdisps.database.entity;

import java.io.Serializable;
import java.time.Instant;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.TypeAlias;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "messageTransactionParameters")
@TypeAlias("messageTransactionParameters")
public class MessageTransactionParametersEntity implements Serializable {
  @Field("assetHardwareId")
  private String assetHardwareId;
  @Id
  private String correlationId;
  @Field(name = "expireAt")
  private Instant expireAt;
  @Field(name = "serviceFunction")
  private String serviceFunction;
  @Field(name = "serviceId")
  private int serviceId;
  @Field(name = "serviceVersion")
  private int serviceVersion;
  @Field(name = "ttl")
  private int ttl;
  @Field(name = "vpi")
  private String vpi;
  @Field("mtMessageType")
  private String mtMessageType;

  public String getAssetHardwareId() {
    return assetHardwareId;
  }

  public String getCorrelationId() {
    return correlationId;
  }

  public Instant getExpireAt() {
    return expireAt;
  }

  public String getServiceFunction() {
    return serviceFunction;
  }

  public int getServiceId() {
    return serviceId;
  }

  public int getServiceVersion() {
    return serviceVersion;
  }

  public int getTtl() {
    return ttl;
  }

  public String getVpi() {
    return vpi;
  }

  public void setAssetHardwareId(String assetHardwareId) {
    this.assetHardwareId = assetHardwareId;
  }

  public void setCorrelationId(String correlationId) {
    this.correlationId = correlationId;
  }

  public void setExpireAt(Instant expireAt) {
    this.expireAt = expireAt;
  }

  public void setServiceFunction(String serviceFunction) {
    this.serviceFunction = serviceFunction;
  }

  public void setServiceId(int serviceId) {
    this.serviceId = serviceId;
  }

  public void setServiceVersion(int serviceVersion) {
    this.serviceVersion = serviceVersion;
  }

  public void setTtl(int ttl) {
    this.ttl = ttl;
  }

  public void setVpi(String vpi) {
    this.vpi = vpi;
  }

  public String getMtMessageType() {
    return mtMessageType;
  }

  public void setMtMessageType(String mtMessageType) {
    this.mtMessageType = mtMessageType;
  }
}
