package com.volvo.tisp.mtdisps.database.entity;

import java.util.Locale;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public enum TelematicUnitType {
  TGW, VCM;

  public static TelematicUnitType fromString(String telematicUnitType) {
    Validate.notEmpty(telematicUnitType, "telematicUnitType");

    return switch (telematicUnitType.toUpperCase(Locale.ROOT)) {
      case "TGW" -> TGW;
      case "VCM" -> VCM;
      default -> throw new IllegalStateException("Unexpected value: " + telematicUnitType.toUpperCase(Locale.ROOT));
    };
  }
}
