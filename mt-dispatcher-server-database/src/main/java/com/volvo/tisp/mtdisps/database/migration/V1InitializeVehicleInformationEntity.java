package com.volvo.tisp.mtdisps.database.migration;

import java.util.Arrays;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.mongodb.client.model.Indexes;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;

@ChangeUnit(order = "01", id = "V1InitializeVehicleInformationEntity")
public class V1InitializeVehicleInformationEntity {
  private static final String VEHICLE_INFORMATION = "vehicleInformation";
  private final MongoTemplate mongoTemplate;

  public V1InitializeVehicleInformationEntity(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @Execution
  public void execution() {
    Document validationSchema = new Document("$jsonSchema", new Document("type", "object")
        .append("required",
            Arrays.asList("_id", "vpi", "createdAt", "operational", "softCar", "telematicUnitSubType", "telematicUnitType", "updatedAt", "version"))
        .append("properties",
            new Document()
                .append("vpi", new Document("type", "string").append("maxLength", 32))
                .append("chassisId", new Document("type", "string"))
                .append("createdAt", new Document("bsonType", "date"))
                .append("operational", new Document("type", "boolean"))
                .append("operationalVersion", new Document("type", "number"))
                .append("partNumber", new Document("type", "string"))
                .append("serialNumber", new Document("type", "string"))
                .append("softCar", new Document("type", "boolean"))
                .append("telematicUnitSubType", new Document("type", "string").append("maxLength", 32))
                .append("telematicUnitType", new Document("type", "string").append("maxLength", 32))
                .append("updatedAt", new Document("bsonType", "date"))
                .append("version", new Document("type", "number"))
        )
    );

    Document command = new Document("collMod", VEHICLE_INFORMATION)
        .append("validator", validationSchema);
    if (!mongoTemplate.collectionExists(VEHICLE_INFORMATION)) {
      mongoTemplate.createCollection(VEHICLE_INFORMATION);
    }
    mongoTemplate.getDb().runCommand(command);

    mongoTemplate.getCollection(VEHICLE_INFORMATION).createIndex(Indexes.ascending("vpi"));
    mongoTemplate.getCollection(VEHICLE_INFORMATION).createIndex(Indexes.ascending("partNumber", "serialNumber"));
    mongoTemplate.getCollection(VEHICLE_INFORMATION).createIndex(Indexes.ascending("chassisId"));
  }

  @RollbackExecution
  public void rollbackExecution() {
  }
}
