package com.volvo.tisp.mtdisps.database.migration;

import java.util.Arrays;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;

@ChangeUnit(order = "02", id = "V2InitializeMessageTransactionParametersEntity")
public class V2InitializeMessageTransactionParametersEntity {
  public static final String MESSAGE_TRANSACTION_PARAMETERS = "messageTransactionParameters";
  private final MongoTemplate mongoTemplate;

  public V2InitializeMessageTransactionParametersEntity(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @Execution
  public void execution() {
    Document validationSchema = new Document("$jsonSchema", new Document("type", "object")
        .append("required", Arrays.asList("_id", "expireAt", "ttl", "assetHardwareId", "mtMessageType"))
        .append("properties",
            new Document()
                .append("_id", new Document("type", "string").append("minLength", 1).append("maxLength", 128))
                .append("expireAt", new Document("bsonType", "date"))
                .append("serviceFunction", new Document("type", "string").append("minLength", 1).append("maxLength", 32))
                .append("serviceId", new Document("type", "number"))
                .append("serviceVersion", new Document("type", "number"))
                .append("ttl", new Document("type", "number"))
                .append("assetHardwareId", new Document("type", "string").append("minLength", 1).append("maxLength", 128))
                .append("mtMessageType", new Document("type", "string").append("minLength", 1).append("maxLength", 50))
        )
    );
    Document command = new Document("collMod", MESSAGE_TRANSACTION_PARAMETERS).append("validator", validationSchema);
    if (!mongoTemplate.collectionExists(MESSAGE_TRANSACTION_PARAMETERS)) {
      mongoTemplate.createCollection(MESSAGE_TRANSACTION_PARAMETERS);
    }
    mongoTemplate.getDb().runCommand(command);
  }

  @RollbackExecution
  public void rollbackExecution() {
    mongoTemplate.dropCollection(MessageTransactionParametersEntity.class);
  }
}
