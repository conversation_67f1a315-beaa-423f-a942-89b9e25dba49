package com.volvo.tisp.mtdisps.database.migration;

import org.springframework.data.mongodb.core.MongoTemplate;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;

@ChangeUnit(order = "018", id = "ResetMongockChangeLogCollection")
public class ResetMongockChangeLogCollection {
  private final MongoTemplate mongoTemplate;

  public ResetMongockChangeLogCollection(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @Execution
  public void execution() {
    mongoTemplate.getCollection("mongockChangeLog").drop();
  }

  @RollbackExecution
  public void rollbackExecution() {
  }
}
