package com.volvo.tisp.mtdisps.database.entity;

import java.io.Serializable;
import java.time.Instant;

import jakarta.annotation.Nullable;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.annotation.TypeAlias;
import org.springframework.data.domain.Persistable;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Document(collection = "vehicleInformation")
@TypeAlias("vehicleInformation")
public class VehicleInformationEntity implements Persistable<String>, Serializable {
  @Field("chassisId")
  private String chassisId;
  @Field("createdAt")
  @CreatedDate
  private Instant createdAt;
  @Id
  private String id;
  @Field("operational")
  private boolean operational;
  @Field("operationalVersion ")
  private long operationalVersion;
  @Field("partNumber")
  private String partNumber;
  @Field("serialNumber")
  private String serialNumber;
  @Field("softCar")
  private boolean softCar;
  @Field("telematicUnitSubType")
  private String telematicUnitSubType;
  @Field("telematicUnitType")
  private TelematicUnitType telematicUnitType;
  @Field("updatedAt")
  @LastModifiedDate
  private Instant updatedAt;
  @Field("version")
  private long version;
  @Field("vpi")
  private String vpi;

  public @Nullable String getChassisId() {
    return chassisId;
  }

  public Instant getCreatedAt() {
    return createdAt;
  }

  @Override
  public String getId() {
    return id;
  }

  public long getOperationalVersion() {
    return operationalVersion;
  }

  public String getPartNumber() {
    return partNumber;
  }

  public String getSerialNumber() {
    return serialNumber;
  }

  public String getTelematicUnitSubType() {
    return telematicUnitSubType;
  }

  public TelematicUnitType getTelematicUnitType() {
    return telematicUnitType;
  }

  public Instant getUpdatedAt() {
    return updatedAt;
  }

  public long getVersion() {
    return version;
  }

  public @Nullable String getVpi() {
    return vpi;
  }

  @Override
  public boolean isNew() {
    return createdAt == null;
  }

  public boolean isOperational() {
    return operational;
  }

  public boolean isSoftCar() {
    return softCar;
  }

  public void setChassisId(String chassisId) {
    Validate.notNull(chassisId, "chassisId");

    this.chassisId = chassisId;
  }

  public void setCreatedAt(Instant createdAt) {
    this.createdAt = createdAt;
  }

  public void setOperational(boolean operational) {
    this.operational = operational;
  }

  public void setOperationalVersion(long operationalVersion) {
    this.operationalVersion = operationalVersion;
  }

  public void setPartNumber(String partNumber) {
    this.partNumber = partNumber;
  }

  public void setSerialNumber(String serialNumber) {
    this.serialNumber = serialNumber;
  }

  public void setSoftCar(boolean softCar) {
    this.softCar = softCar;
  }

  public void setTelematicUnitSubType(String telematicUnitSubType) {
    this.telematicUnitSubType = telematicUnitSubType;
  }

  public void setTelematicUnitType(TelematicUnitType telematicUnitType) {
    Validate.notNull(telematicUnitType, "telematicUnitType");

    this.telematicUnitType = telematicUnitType;
  }

  public void setUpdatedAt(Instant updatedAt) {
    this.updatedAt = updatedAt;
  }

  public void setVersion(long version) {
    this.version = version;
  }

  public void setVpi(String vpi) {
    Validate.notNull(vpi, "vpi");

    this.vpi = vpi;
  }

  public String toCsvString() {
    return String.join(",", vpi, telematicUnitType.name());
  }

  @Override
  public String toString() {
    return new StringBuilder(100).append("createdAt={")
        .append(createdAt)
        .append("}, chassisId={")
        .append(chassisId)
        .append("}, id={")
        .append(id)
        .append("}, operational={")
        .append(operational)
        .append("}, operationalVersion={")
        .append(operationalVersion)
        .append("}, serialNumber={")
        .append(serialNumber)
        .append("}, softCar={")
        .append(softCar)
        .append("}, partNumber={")
        .append(partNumber)
        .append("}, telematicUnitSubType={")
        .append(telematicUnitSubType)
        .append("}, telematicUnitType={")
        .append(telematicUnitType)
        .append("}, updatedAt={")
        .append(updatedAt)
        .append("}, version={")
        .append(version)
        .append("}, vpi={")
        .append(vpi)
        .append("}")
        .toString();
  }
}
