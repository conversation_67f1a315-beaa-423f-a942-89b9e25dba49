package com.volvo.tisp.mtdisps.database.api;

import java.util.Collection;
import java.util.Optional;

import javax.annotation.Nonnull;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.dao.DataAccessException;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;

@Repository
public interface VehicleInformationRepository extends CrudRepository<VehicleInformationEntity, String> {
  String OPERATIONAL_VEHICLE_INFORMATION_BY_ASSET_HARDWARE_ID_CACHE = "operationalVehicleInformationByAssetHardwareIdCache";
  String OPERATIONAL_VEHICLE_INFORMATION_BY_VPI_CACHE = "operationalVehicleInformationByVpiCache";

  @Override
  @CacheEvict(cacheNames = {OPERATIONAL_VEHICLE_INFORMATION_BY_VPI_CACHE,
                            OPERATIONAL_VEHICLE_INFORMATION_BY_ASSET_HARDWARE_ID_CACHE})
  void deleteAll(@Nonnull Iterable<? extends VehicleInformationEntity> entities);

  @CacheEvict(cacheNames = {OPERATIONAL_VEHICLE_INFORMATION_BY_VPI_CACHE})
  void deleteByVpi(@Nonnull String vpi) throws DataAccessException;

  @Nonnull
  Collection<VehicleInformationEntity> findAllByChassisId(@Nonnull String chassisId);

  @Nonnull
  Collection<VehicleInformationEntity> findAllByPartNumberAndSerialNumber(@Nonnull String partNumber, @Nonnull String serialNumber);

  @Nonnull
  Optional<VehicleInformationEntity> findByChassisId(@Nonnull String chassisId);

  @Nonnull
  Optional<VehicleInformationEntity> findByChassisIdAndOperationalIsTrue(@Nonnull String chassisId);

  @Nonnull
  Optional<VehicleInformationEntity> findByPartNumberAndSerialNumber(@Nonnull String partNumber, @Nonnull String serialNumber);

  @Nonnull
  @Cacheable(value = OPERATIONAL_VEHICLE_INFORMATION_BY_ASSET_HARDWARE_ID_CACHE, key = "#p0.concat('-').concat(#p1)")
  Optional<VehicleInformationEntity> findByPartNumberAndSerialNumberAndOperationalIsTrue(@Nonnull String partNumber, @Nonnull String serialNumber);

  @Nonnull
  Optional<VehicleInformationEntity> findByVpi(@Nonnull String vpi);

  @Cacheable(OPERATIONAL_VEHICLE_INFORMATION_BY_VPI_CACHE)
  Optional<VehicleInformationEntity> findByVpiAndOperationalIsTrue(String vpi);

  @Override
  @Caching(evict = {
      @CacheEvict(cacheNames = OPERATIONAL_VEHICLE_INFORMATION_BY_VPI_CACHE, key = "#p0.vpi"),
      @CacheEvict(cacheNames = OPERATIONAL_VEHICLE_INFORMATION_BY_ASSET_HARDWARE_ID_CACHE, key = "#p0.partNumber?.concat('-')?.concat(#p0.serialNumber)")})
  @Nonnull
  <S extends VehicleInformationEntity> S save(@Nonnull S vehicleInformation) throws DataAccessException;

  @Override
  @CacheEvict(cacheNames = {OPERATIONAL_VEHICLE_INFORMATION_BY_VPI_CACHE,
                            OPERATIONAL_VEHICLE_INFORMATION_BY_ASSET_HARDWARE_ID_CACHE})
  @Nonnull
  <S extends VehicleInformationEntity> Iterable<S> saveAll(@Nonnull Iterable<S> entities);
}
