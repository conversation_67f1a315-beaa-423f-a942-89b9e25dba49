package com.volvo.tisp.mtdisps.database.api;

import java.time.Instant;
import java.util.Optional;

import javax.annotation.Nonnull;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataAccessException;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;

@Repository
public interface MessageTransactionParametersRepository extends MongoRepository<MessageTransactionParametersEntity, String> {
  String MESSAGE_TRANSACTION_PARAMETERS_CACHE = "messageTransactionParametersCache";

  @Query(value = "{'expireAt' : { $lt: ?0 } }", count = true)
  long countRecordsBeforeCurrentTime(Instant currentTime);

  @Override
  @Nonnull
  @Transactional(readOnly = true)
  Optional<MessageTransactionParametersEntity> findById(@Nonnull String correlationId);

  @Override
  @Nonnull
  @Cacheable(value = MESSAGE_TRANSACTION_PARAMETERS_CACHE, key = "#p0.correlationId")
  <S extends MessageTransactionParametersEntity> S insert(@Nonnull S messageTransactionParameters) throws DataAccessException;

  @Override
  @Nonnull
  @CacheEvict(cacheNames = MESSAGE_TRANSACTION_PARAMETERS_CACHE, key = "#p0.correlationId")
  <S extends MessageTransactionParametersEntity> S save(@Nonnull S messageTransactionParameters) throws DataAccessException;
}
