import * as React from "react";
import * as ReactDOM from "react-dom";
import './index.css';
import App from './App';
import 'semantic-ui-less/semantic.less';


declare const Oasis: any;
declare const oasis: any;


const BroadcastConsumer = Oasis.Consumer.extend({
    initialize: function () {
        window["BroadcastSender"] = this;
    },
    onMessage: function(message: string) {
    },
    events: {
        hostbroadcast: function(message: any) {

            this.onMessage(message);
        }
    }
});

oasis.connect({
    consumers: {
        broadcast: BroadcastConsumer
    }
});
window["BroadcastConsumer"] = BroadcastConsumer;

ReactDOM.render(
    <App />,
    document.getElementById('app')
);