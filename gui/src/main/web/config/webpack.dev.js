var webpackMerge = require('webpack-merge');
var commonConfig = require('./webpack.common.js');
var helpers = require('./helpers');

module.exports = webpackMerge(commonConfig, {
  devtool: "inline-source-map",
  output: {
    path: helpers.root('dist'),
    publicPath: 'http://localhost:8082/',
    filename: '[name].js',
    chunkFilename: '[id].chunk.js'
  },
  devServer: {
    historyApiFallback: true,
    stats: 'minimal',
    port: 8082,
    proxy: {
      "/vehicleping":"http://localhost:12590",
      "/auth":"http://localhost:12590",
      "/beefy":"http://localhost:12590",
      "/custom":"http://localhost:12590"
    }
  }
});
