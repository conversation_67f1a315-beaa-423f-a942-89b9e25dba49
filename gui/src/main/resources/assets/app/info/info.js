"use strict";

angular.module("app.info", ["ui.router"])

  .config(function($stateProvider) {
    $stateProvider.state("info", { title: "Info", url: "/componentinfo", templateUrl: "../assets/app/info/info.html" });

  })
  .controller('InfoController', function ($scope, $http) {
          var req = {
              method: 'GET',
              url: '/runtimeinfo'
          };
          $http(req).
              then(function(response) {
                  $scope.runtimeInfos = response.data;
              });
    });
