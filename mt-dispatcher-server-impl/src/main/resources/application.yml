# Global properties
mongock:
  migration-scan-package: com.volvo.tisp.mtdisps.database.migration
  transaction-enabled: false

server.port: 48000

spring:
  profiles.group:
    de_component-test_eu-west-1: sqs
    local_local_local: sqs,vcm-legacy
    shared_test_eu-west-1: sqs,vcm,tgw,load-test
    shared_iot1_eu-west-1: sqs,vcm,tgw
    shared_qa_eu-west-1: sqs,vcm,tgw
    shared_iot1_us-east-1: sqs,vcm,tgw
    shared_qa_us-east-1: sqs,vcm,tgw
    shared_prod_eu-west-1: sqs,vcm
    shared_prod_us-east-1: sqs,vcm
    iz_qa_ap-ne-1: sqs,vcm
  flyway.enabled: false
  jms.listener.max-concurrency: 10
  liquibase.enabled: false

service-function-send-schema-mapping:
  map:
    ping-sat-only:
      sendSchemaHint: sat-only
    ping-sms-only:
      sendSchemaHint: sms-only
    legacy-darf-config:
      sendSchemaHint: darf-config
---
#LOCAL
spring:
  config.activate.on-profile: local_local_local
  artemis:
    broker-url: tcp://localhost:61616
    user: admin
    password: admin
  cloud.aws.region.static: us-east-1
  data.mongodb.uri: **********************************************************************

jms:
  tgwmts.mt.message.queue.name: LOCAL.LOCAL.LOCAL.TGWMTS.MT-MESSAGES
sqs:
  region: us-east-1
  endpoint-override: http://localhost:4566
  incoming-mt-message.queue-url: http://localhost:4566/000000000000/mtdisps-in-mt-message-queue
  incoming-mt-message-with-asset-hardware-id.queue-url: http://localhost:4566/000000000000/mtdisps-in-mt-message-with-asset-hardware-id-queue

integration.logging.enabled: false
token-issuer.public-key.path: classpath:tokenIssuerPublicKey.pem
kinesis.data.stream.arn: arn:aws:kinesis:default000000000000:stream/integration-log-stream
management.influx.metrics.export.enabled: false

