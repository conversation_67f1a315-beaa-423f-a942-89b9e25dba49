package com.volvo.tisp.mtdisps.impl.service;

import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;

public final class ValidationException extends Exception {
  private final ValidationFailureReason validationFailureReason;

  public ValidationException(ValidationFailureReason validationFailureReason) {
    super(validationFailureReason.name());
    this.validationFailureReason = validationFailureReason;
  }

  public ValidationFailureReason getValidationFailureReason() {
    return validationFailureReason;
  }
}
