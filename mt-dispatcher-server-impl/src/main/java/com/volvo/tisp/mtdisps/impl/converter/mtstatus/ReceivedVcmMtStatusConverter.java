package com.volvo.tisp.mtdisps.impl.converter.mtstatus;

import java.util.Optional;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.Status;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtStatusProtobuf;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class ReceivedVcmMtStatusConverter implements Function<AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus, Optional<ReceivedMtStatus>> {
  private static Status mapStatus(AssetMtSchedulerMtStatusProtobuf.Status status) {
    return switch (status) {
      case DELIVERED -> Status.DELIVERED;
      case DISCARDED -> Status.CANCELED;
      case UNSUPPORTEDSERVICE -> Status.UNSUPPORTED_SERVICE;
      case UNSUPPORTEDVERSION -> Status.UNSUPPORTED_VERSION;
      case FAILED, UNRECOGNIZED -> Status.FAILED;
      case OVERRIDDEN -> Status.OVERRIDDEN;
      case TIMEOUT -> Status.TIMEOUT;
    };
  }

  @Override
  public Optional<ReceivedMtStatus> apply(AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus mtStatus) {
    Validate.notNull(mtStatus, "mtStatus");
    try {
      return Optional.of(new ReceivedMtStatus.Builder()
          .setCorrelationId(CorrelationId.ofString(mtStatus.getCorrelationId()))
          .setDescription(mtStatus.getDescription())
          .setStatus(mapStatus(mtStatus.getStatus()))
          .setTrackingIdentifier(TrackingIdentifier.fromString(mtStatus.getTrackingId()))
          .build());
    } catch (Exception e) {
      return Optional.empty();
    }
  }
}
