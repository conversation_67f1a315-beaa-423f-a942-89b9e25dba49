package com.volvo.tisp.mtdisps.impl.converter.mtstatus;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.reporter.InternalMtStatusConverterMetricReporter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus;
import com.volvo.tisp.vc.mt.message.client.json.v1.Status;

@Component
public final class MtStatusConverter {
  private static final Logger logger = LoggerFactory.getLogger(MtStatusConverter.class);

  private final InternalMtStatusConverterMetricReporter mtStatusConverterMetricReporter;

  public MtStatusConverter(InternalMtStatusConverterMetricReporter mtStatusConverterMetricReporter) {
    this.mtStatusConverterMetricReporter = mtStatusConverterMetricReporter;
  }

  public MtStatus convert(ProcessableMtStatus processableMtStatus) {
    Validate.notNull(processableMtStatus, "processableMtStatus");

    return new MtStatus()
        .withStatus(toStatus(processableMtStatus.status()))
        .withVehiclePlatformId(getVehiclePlatformId(processableMtStatus))
        .withCorrelationId(processableMtStatus.correlationId().toString())
        .withDescription(Optional.ofNullable(processableMtStatus.description()).orElse(processableMtStatus.status().toString()))
        .withTrackingId(processableMtStatus.trackingIdentifier().toString());
  }

  private String getVehiclePlatformId(ProcessableMtStatus processableMtStatus) {
    return processableMtStatus.vpiOption().map(Vpi::toString).orElseThrow(() -> {
      logger.error("VPI missing in processableMtStatus {}", processableMtStatus);
      mtStatusConverterMetricReporter.onMissingVpi();
      return new IllegalStateException("VPI missing in processableMtStatus");
    });
  }

  private Status toStatus(com.volvo.tisp.mtdisps.impl.dto.Status status) {
    return switch (status) {
      case ASSET_NOT_FOUND -> Status.ASSET_NOT_FOUND;
      case CANCELED -> Status.CANCELED;
      case DELIVERED -> Status.DELIVERED;
      case FAILED -> Status.FAILED;
      case INVALID_TOKEN -> Status.INVALID_TOKEN;
      case MULTIPLE_ASSETS_FOUND -> Status.MULTIPLE_ASSETS_CONFLICT;
      case NO_PERMISSION -> Status.NO_PERMISSION;
      case OVERRIDDEN -> Status.OVERRIDDEN;
      case TIMEOUT -> Status.TIMEOUT;
      case UNSUPPORTED_SERVICE -> Status.UNSUPPORTED_SERVICE;
      case UNSUPPORTED_VERSION -> Status.UNSUPPORTED_VERSION;
    };
  }
}
