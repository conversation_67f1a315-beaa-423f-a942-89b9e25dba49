package com.volvo.tisp.mtdisps.impl.dto;

import java.util.Optional;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ServiceVersion;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record ProcessableMtStatus(
    CorrelationId correlationId,
    String description,
    Status status,
    TrackingIdentifier trackingIdentifier,
    ServiceFunction serviceFunction,
    ServiceId serviceId,
    ServiceVersion serviceVersion,
    Optional<String> assetHardwareId,
    Optional<Vpi> vpiOption,
    MtMessageType mtMessageType) {
  public ProcessableMtStatus {
    Validate.notNull(correlationId, "correlationId");
    Validate.notNull(status, "status");
    Validate.notNull(trackingIdentifier, "trackingIdentifier");
    Validate.notNull(serviceFunction, "serviceFunction");
    Validate.notNull(serviceId, "serviceId");
    Validate.notNull(serviceVersion, "serviceVersion");
    Validate.notNull(assetHardwareId, "assetHardwareId");
    Validate.notNull(vpiOption, "vpiOption");
    Validate.notNull(mtMessageType, "mtMessageType");
  }
}
