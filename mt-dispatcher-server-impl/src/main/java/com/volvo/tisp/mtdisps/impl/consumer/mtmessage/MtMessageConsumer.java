package com.volvo.tisp.mtdisps.impl.consumer.mtmessage;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import com.volvo.tisp.mtdisps.impl.converter.mtmessage.ProcessableMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.ReceivedMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtMessage;
import com.volvo.tisp.mtdisps.impl.dto.Status;
import com.volvo.tisp.mtdisps.impl.model.InterfaceType;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.MtMessageMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.IntegrationLogger;
import com.volvo.tisp.mtdisps.impl.service.MtMessageDomainManager;
import com.volvo.tisp.mtdisps.impl.service.ServiceAccessTokenValidator;
import com.volvo.tisp.mtdisps.impl.service.ServiceWhitelistValidator;
import com.volvo.tisp.mtdisps.impl.service.ValidationException;
import com.volvo.tisp.mtdisps.impl.service.processor.MtStatusProcessor;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;

public abstract class MtMessageConsumer<T> {
  private final IntegrationLogger integrationLogger;
  private final MtMessageDomainManager mtMessageDomainManager;
  private final MtMessageMetricReporter mtMessageMetricReporter;
  private final MtStatusProcessor mtStatusProcessor;
  private final ProcessableMtMessageConverter processableMtMessageConverter;
  private final ReceivedMtMessageConverter receivedMtMessageConverter;
  private final ServiceAccessTokenValidator serviceAccessTokenValidator;
  private final ServiceWhitelistValidator serviceWhitelistValidator;

  protected MtMessageConsumer(
      MtMessageDomainManager mtMessageDomainManager,
      MtMessageMetricReporter mtMessageMetricReporter,
      ServiceAccessTokenValidator serviceAccessTokenValidator,
      ProcessableMtMessageConverter processableMtMessageConverter,
      MtStatusProcessor mtStatusProcessor,
      IntegrationLogger integrationLogger,
      ReceivedMtMessageConverter receivedMtMessageConverter,
      ServiceWhitelistValidator serviceWhitelistValidator) {
    this.mtMessageDomainManager = mtMessageDomainManager;
    this.mtMessageMetricReporter = mtMessageMetricReporter;
    this.serviceAccessTokenValidator = serviceAccessTokenValidator;
    this.processableMtMessageConverter = processableMtMessageConverter;
    this.mtStatusProcessor = mtStatusProcessor;
    this.integrationLogger = integrationLogger;
    this.receivedMtMessageConverter = receivedMtMessageConverter;
    this.serviceWhitelistValidator = serviceWhitelistValidator;
  }

  private static Optional<Vpi> getVpiOption(ReceivedMtMessage receivedMtMessage) {
    return receivedMtMessage.assetIdentifierType() == AssetIdentifierType.VPI ?
        Optional.of(Vpi.ofString(receivedMtMessage.assetIdentifier())) :
        Optional.empty();
  }

  protected CompletableFuture<Void> consumeMessage(T mtMessage) {
    mtMessageMetricReporter.onMtMessageReceived(getInterfaceType());
    return receivedMtMessageConverter.convert(mtMessage).map(this::authenticateAndProcessRequest).orElseGet(() -> {
      mtMessageMetricReporter.onMtMessageFailure(ValidationFailureReason.CONVERSION_FAILURE);
      return CompletableFuture.failedFuture(new ValidationException(ValidationFailureReason.CONVERSION_FAILURE));
    });
  }

  protected abstract InterfaceType getInterfaceType();

  String createDescription(CorrelationId correlationId) {
    return "MT Status for CorrelationId:" + correlationId;
  }

  Status createStatus(ValidationFailureReason validationFailureReason) {
    return switch (validationFailureReason) {
      case AUTHENTICATION_FAILURE -> Status.INVALID_TOKEN;
      case ASSET_NOT_FOUND -> Status.ASSET_NOT_FOUND;
      case MULTIPLE_ASSETS_FOUND -> Status.MULTIPLE_ASSETS_FOUND;
      case SERVICE_NOT_WHITELISTED -> Status.NO_PERMISSION;
      default -> Status.FAILED;
    };
  }

  private CompletableFuture<Void> authenticateAndProcessRequest(ReceivedMtMessage receivedMtMessage) {
    try {
      ProcessableMtMessage processableMtMessage = processableMtMessageConverter.convert(receivedMtMessage);
      integrationLogger.log(processableMtMessage, "MtMessageReceived");

      serviceWhitelistValidator.validateIsWhitelisted(processableMtMessage);
      serviceAccessTokenValidator.validateServiceAccessToken(processableMtMessage);
      return mtMessageDomainManager.processMessage(processableMtMessage).whenComplete((unused, throwable) -> {
        if (throwable != null) {
          integrationLogger.log(processableMtMessage, "MtMessageProcessingFailed_" + throwable.getClass().getSimpleName());
        }
      });
    } catch (ValidationException e) {
      ValidationFailureReason validationFailureReason = e.getValidationFailureReason();
      return publishFailureStatus(receivedMtMessage, validationFailureReason);
    }
  }

  private Optional<ProcessableMtStatus> createFailureMtStatus(ReceivedMtMessage receivedMtMessage, ValidationFailureReason validationFailureReason) {
    return receivedMtMessage.correlationId()
        .map(correlationId -> new ProcessableMtStatus(correlationId, createDescription(correlationId),
            createStatus(validationFailureReason), receivedMtMessage.trackingIdentifier(), receivedMtMessage.serviceFunction(), receivedMtMessage.serviceId(),
            receivedMtMessage.serviceVersion(), Optional.empty(), getVpiOption(receivedMtMessage), receivedMtMessage.mtMessageType()));
  }

  private CompletableFuture<Void> publishFailureStatus(ReceivedMtMessage receivedMtMessage, ValidationFailureReason validationFailureReason) {
    mtMessageMetricReporter.onMtMessageFailure(validationFailureReason);
    return createFailureMtStatus(receivedMtMessage, validationFailureReason).map(processableMtStatus -> {
      integrationLogger.log(processableMtStatus, "MtMessageProcessingFailed_" + validationFailureReason);
      return mtStatusProcessor.process(processableMtStatus);
    }).orElse(CompletableFuture.completedFuture(null));
  }
}
