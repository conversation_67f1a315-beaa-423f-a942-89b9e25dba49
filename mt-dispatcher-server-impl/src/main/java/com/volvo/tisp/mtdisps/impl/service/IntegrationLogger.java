package com.volvo.tisp.mtdisps.impl.service;

import java.time.Clock;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.integration.log.IntegrationLogEventPublisher;
import com.volvo.tisp.integration.log.v2.AssetIdentifier;
import com.volvo.tisp.integration.log.v2.IntegrationLogEvent;
import com.volvo.tisp.mtdisps.impl.conf.AppProperties;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.model.AssetHardwareId;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;

@Component
public class IntegrationLogger {
  private static final String UNKNOWN_ASSET_HARDWARE_ID = "00000000-00000000";
  private static final Logger logger = LoggerFactory.getLogger(IntegrationLogger.class);

  private final AppProperties appProperties;
  private final Clock clock;
  private final IntegrationLogEventPublisher integrationLogEventPublisher;

  public IntegrationLogger(AppProperties appProperties, Clock clock, IntegrationLogEventPublisher integrationLogEventPublisher) {
    this.clock = clock;
    this.appProperties = appProperties;
    this.integrationLogEventPublisher = integrationLogEventPublisher;
  }

  private static AssetIdentifier createAssetIdentifier(String assetIdentifier, AssetIdentifier.Type type) {
    return new AssetIdentifier()
        .withAssetIdentifier(assetIdentifier)
        .withType(type);
  }

  private static Set<AssetIdentifier> getAssetIdentifiers(AssetHardwareId assetHardwareId) {
    HashSet<AssetIdentifier> hashSet = new HashSet<>();
    hashSet.add(createAssetIdentifier(assetHardwareId.toString(), AssetIdentifier.Type.ASSET_ID));
    return hashSet;
  }

  private static String getUnknownAssetHardwareId(ProcessableMtStatus processableMtStatus) {
    logger.warn("received mt-status without asset-hardware-id: {}", processableMtStatus);
    return UNKNOWN_ASSET_HARDWARE_ID;
  }

  public void log(ProcessableMtStatus processableMtStatus, String detailType) {
    if (appProperties.isIntegrationLoggingEnabled()) {
      IntegrationLogEvent integrationLogEvent = createIntegrationLogEvent(
          AssetHardwareId.fromString(processableMtStatus.assetHardwareId().orElseGet(() -> getUnknownAssetHardwareId(processableMtStatus))),
          processableMtStatus.trackingIdentifier().toString(),
          new Detail(processableMtStatus),
          detailType);

      processableMtStatus.vpiOption()
          .ifPresent(vpi -> integrationLogEvent.getAssetIdentifiers().add(createAssetIdentifier(vpi.toString(), AssetIdentifier.Type.VPI)));
      log(integrationLogEvent);
    }
  }

  public void log(ProcessableMtMessage processableMtMessage, String detailType) {
    if (appProperties.isIntegrationLoggingEnabled()) {
      VehicleInformation vehicleInformation = processableMtMessage.vehicleInformation();
      IntegrationLogEvent integrationLogEvent = createIntegrationLogEvent(
          vehicleInformation.assetHardwareId(),
          processableMtMessage.trackingIdentifier().toString(),
          new Detail(processableMtMessage), detailType);

      vehicleInformation.vpi().ifPresent(vpi -> integrationLogEvent.getAssetIdentifiers().add(createAssetIdentifier(vpi.toString(), AssetIdentifier.Type.VPI)));
      log(integrationLogEvent);
    }
  }

  private IntegrationLogEvent createIntegrationLogEvent(AssetHardwareId assetHardwareId, String trackingId, Detail detail, String detailType) {
    return new IntegrationLogEvent().withTrackingId(trackingId)
        .withAssetIdentifiers(getAssetIdentifiers(assetHardwareId))
        .withId(UUID.randomUUID())
        .withSource("MTDISPS")
        .withTimestamp(clock.instant().toEpochMilli())
        .withDetail(detail.toMap())
        .withDetailType(detailType)
        .withVersion(IntegrationLogEvent.Version._2);
  }

  @SuppressWarnings("FutureReturnValueIgnored")
  private void log(IntegrationLogEvent integrationLogEvent) {
    integrationLogEventPublisher.publish(integrationLogEvent);
  }

  record Detail(String correlationId, int serviceId, Map<String, Object> additionalProperties) {

    Detail(ProcessableMtStatus processableMtStatus) {
      this(
          processableMtStatus.correlationId().toString(),
          processableMtStatus.serviceId().serviceId(),
          Map.of("Status", processableMtStatus.status().name()));
    }

    Detail(ProcessableMtMessage processableMtMessage) {
      this(
          processableMtMessage.correlationId().map(CorrelationId::toString).orElse("MISSING_CORRELATION_ID"),
          processableMtMessage.serviceId().serviceId(),
          Map.of());
    }

    Map<String, Object> toMap() {
      HashMap<String, Object> map = new HashMap<>(Map.of("CorrelationId", correlationId, "ServiceId", serviceId));
      map.putAll(additionalProperties);
      return map;
    }
  }
}
