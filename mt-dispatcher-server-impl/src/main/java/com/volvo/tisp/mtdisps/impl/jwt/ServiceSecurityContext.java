package com.volvo.tisp.mtdisps.impl.jwt;

import java.util.Arrays;
import java.util.List;

import com.nimbusds.jose.proc.SecurityContext;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record ServiceSecurityContext(ServiceId serviceId) implements SecurityContext {
  private static final String MH_WRITE_SCOPE = "mh.w";
  private static final String SERVICE_SCOPE_BASE = "svc.%";

  public ServiceSecurityContext {
    Validate.notNull(serviceId, "serviceId");
  }

  public static ServiceSecurityContext of(ServiceId serviceId) {
    return new ServiceSecurityContext(serviceId);
  }

  public boolean isValidClaim(String claimString) {
    List<String> claims = Arrays.stream(claimString.split(" ")).map(String::trim).toList();

    if (!claims.contains(MH_WRITE_SCOPE)) {
      return false;
    }

    return claims.contains(SERVICE_SCOPE_BASE.replace("%", serviceId.toString()));
  }
}
