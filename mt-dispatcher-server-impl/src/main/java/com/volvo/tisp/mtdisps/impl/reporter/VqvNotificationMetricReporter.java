package com.volvo.tisp.mtdisps.impl.reporter;

import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

@Component
public class VqvNotificationMetricReporter {
  private static final String NAME = "vqv";
  private static final String DUPLICATE_ASSET_COUNTER = NAME + ".asset.duplicate";

  private final Counter conversionFailureCounter;
  private final MeterRegistry meterRegistry;

  public VqvNotificationMetricReporter(MeterRegistry meterRegistry) {
    this.conversionFailureCounter = meterRegistry.counter(NAME + ".conversion.failure");
    this.meterRegistry = meterRegistry;
  }

  public void onConversionFailure() {
    conversionFailureCounter.increment();
  }

  public void onDuplicateAssetHardwareId(String assetHardwareId) {
    Validate.notEmpty(assetHardwareId, "assetHardwareId");

    this.meterRegistry.counter(DUPLICATE_ASSET_COUNTER,
        Tags.of("asset.identifier", assetHardwareId, "asset.identifier.type", AssetIdentifierType.ASSET_HARDWARE_ID.name())).increment();
  }

  public void onDuplicateChassisId(String chassisId) {
    Validate.notEmpty(chassisId, "chassisId");

    this.meterRegistry.counter(DUPLICATE_ASSET_COUNTER,
        Tags.of("asset.identifier", chassisId, "asset.identifier.type", AssetIdentifierType.CHASSIS_ID.name())).increment();
  }
}
