package com.volvo.tisp.mtdisps.impl.model;

import java.util.Optional;

import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record VehicleInformation(
    Optional<ChassisId> chassisId,
    OperationalStatus operationalStatus,
    AssetHardwareId assetHardwareId,
    boolean softCar,
    Optional<TelematicUnitSubType> telematicUnitSubType,
    TelematicUnitType telematicUnitType,
    Version version,
    Optional<Vpi> vpi
) {

  public VehicleInformation {
    Validate.notNull(chassisId, "chassisId");
    Validate.notNull(operationalStatus, "operationalStatus");
    Validate.notNull(assetHardwareId, "assetHardwareId");
    Validate.notNull(telematicUnitType, "telematicUnitType");
    Validate.notNull(version, "version");
    Validate.notNull(vpi, "vpi");
  }

  public static VehicleInformation fromEntity(VehicleInformationEntity vehicleInformationEntity) {
    return new VehicleInformation(Optional.ofNullable(vehicleInformationEntity.getChassisId()).map(ChassisId::new),
        OperationalStatus.fromValue(vehicleInformationEntity.isOperational()), AssetHardwareId.of(vehicleInformationEntity.getPartNumber(),
        vehicleInformationEntity.getSerialNumber()), vehicleInformationEntity.isSoftCar(),
        Optional.ofNullable(vehicleInformationEntity.getTelematicUnitSubType()).map(TelematicUnitSubType::new), vehicleInformationEntity.getTelematicUnitType(),
        new Version(vehicleInformationEntity.getVersion()), Optional.ofNullable(vehicleInformationEntity.getVpi()).map(Vpi::ofString));
  }

  @Override
  public String toString() {
    return new StringBuilder(200)
        .append("chassisId={")
        .append(chassisId)
        .append("}, operationalStatus={")
        .append(operationalStatus)
        .append("}, assetHardwareId={")
        .append(assetHardwareId)
        .append("}, softCar={")
        .append(softCar)
        .append("}, telematicUnitSubType={")
        .append(telematicUnitSubType)
        .append("}, telematicUnitType={")
        .append(telematicUnitType)
        .append("}, version={")
        .append(version)
        .append("}, vpi={")
        .append(vpi)
        .append("}")
        .toString();
  }

  public static final class Builder {
    private Optional<ChassisId> chassisId = Optional.empty();
    private OperationalStatus operationalStatus;
    private AssetHardwareId assetHardwareId;
    private boolean softCar;
    private Optional<TelematicUnitSubType> telematicUnitSubType = Optional.empty();
    private TelematicUnitType telematicUnitType;
    private Version version;
    private Optional<Vpi> vpi = Optional.empty();

    public VehicleInformation build() {
      return new VehicleInformation(chassisId, operationalStatus, assetHardwareId, softCar, telematicUnitSubType, telematicUnitType, version, vpi);
    }

    public Builder setChassisId(ChassisId chassisId) {
      Validate.notNull(chassisId, "chassisId");

      this.chassisId = Optional.of(chassisId);
      return this;
    }

    public Builder setOperationalStatus(OperationalStatus operationalStatus) {
      Validate.notNull(operationalStatus, "operationalStatus");

      this.operationalStatus = operationalStatus;
      return this;
    }

    public Builder setAssetHardwareId(AssetHardwareId assetHardwareId) {
      Validate.notNull(assetHardwareId, "assetHardwareId");

      this.assetHardwareId = assetHardwareId;
      return this;
    }

    public Builder setSoftCar(boolean softCar) {
      this.softCar = softCar;
      return this;
    }

    public Builder setTelematicUnitSubType(TelematicUnitSubType telematicUnitSubType) {
      this.telematicUnitSubType = Optional.ofNullable(telematicUnitSubType);
      return this;
    }

    public Builder setTelematicUnitType(TelematicUnitType telematicUnitType) {
      Validate.notNull(telematicUnitType, "telematicUnitType");

      this.telematicUnitType = telematicUnitType;
      return this;
    }

    public Builder setVersion(Version version) {
      Validate.notNull(version, "version");

      this.version = version;
      return this;
    }

    public Builder setVpi(Vpi vpi) {
      Validate.notNull(vpi, "vpi");

      this.vpi = Optional.of(vpi);
      return this;
    }
  }
}
