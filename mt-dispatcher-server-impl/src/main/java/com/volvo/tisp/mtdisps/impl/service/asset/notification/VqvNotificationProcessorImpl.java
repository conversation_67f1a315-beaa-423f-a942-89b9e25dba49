package com.volvo.tisp.mtdisps.impl.service.asset.notification;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.transaction.annotation.Transactional;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.mtdisps.impl.model.AssetHardwareId;
import com.volvo.tisp.mtdisps.impl.model.ChassisId;
import com.volvo.tisp.mtdisps.impl.model.TelematicUnitSubType;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.reporter.Crud;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.mtdisps.impl.reporter.VqvNotificationMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.vqv.v1.api.NotificationType;

public class VqvNotificationProcessorImpl implements VqvNotificationProcessor {
  private static final Logger logger = LoggerFactory.getLogger(VqvNotificationProcessorImpl.class);

  private final Clock clock;
  private final DbOperationsMetricReporter dbOperationsMetricReporter;
  private final VehicleInformationRepository vehicleInformationRepository;
  private final VqvNotificationMetricReporter vqvNotificationMetricReporter;

  public VqvNotificationProcessorImpl(Clock clock, DbOperationsMetricReporter dbOperationsMetricReporter,
      VehicleInformationRepository vehicleInformationRepository, VqvNotificationMetricReporter vqvNotificationMetricReporter) {
    this.clock = clock;
    this.dbOperationsMetricReporter = dbOperationsMetricReporter;
    this.vehicleInformationRepository = vehicleInformationRepository;
    this.vqvNotificationMetricReporter = vqvNotificationMetricReporter;
  }

  private static void updateVehicleInformationEntity(VehicleInformation vehicleInformation, VehicleInformationEntity vehicleInformationEntity) {
    vehicleInformationEntity.setChassisId(vehicleInformation.chassisId().map(ChassisId::value).orElse(null));
    vehicleInformationEntity.setPartNumber(vehicleInformation.assetHardwareId().partNumber().value());
    vehicleInformationEntity.setSerialNumber(vehicleInformation.assetHardwareId().serialNumber().value());
    vehicleInformationEntity.setSoftCar(vehicleInformation.softCar());
    vehicleInformationEntity.setTelematicUnitType(vehicleInformation.telematicUnitType());
    vehicleInformationEntity.setTelematicUnitSubType(vehicleInformation.telematicUnitSubType().map(TelematicUnitSubType::telematicUnitSubType).orElse(null));
    vehicleInformationEntity.setVersion(vehicleInformation.version().version());
  }

  @Transactional
  @Override
  public void process(VqvNotification vqvNotification) {
    logger.debug("begin processing of vqvNotification: {}", vqvNotification);

    // markedForDeletion means vehicle was deleted from VDA, NotificationType.REMOVED means the vehicle is no longer part of our filter
    if (vqvNotification.isMarkedForDeletion() || vqvNotification.notificationType() == NotificationType.REMOVED) {
      logger.debug("deleting vehicleInformation entry: {}", vqvNotification.vehicleInformation());
      try {
        Instant start = clock.instant();
        vehicleInformationRepository.deleteByVpi(vqvNotification.vpi().toString());
        dbOperationsMetricReporter.onVehicleInfoCrudDuration(Duration.between(start, clock.instant()), Crud.DELETE);
      } catch (DataAccessException e) {
        dbOperationsMetricReporter.onVehicleInfoCrudFailure(Crud.DELETE);
        throw e;
      }
      return;
    }

    Instant start = clock.instant();
    Optional<VehicleInformationEntity> vehicleInformationEntityOptional = vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString());
    dbOperationsMetricReporter.onVehicleInfoCrudDuration(Duration.between(start, clock.instant()), Crud.READ);

    vehicleInformationEntityOptional.ifPresentOrElse(vehicleInformationEntity -> update(vehicleInformationEntity, vqvNotification.vehicleInformation()),
        () -> insert(vqvNotification.vehicleInformation()));
    logDuplicateAssetMetrics(vqvNotification);
  }

  private void insert(VehicleInformation vehicleInformation) {
    Validate.notNull(vehicleInformation.vpi().orElse(null), "vpi");

    VehicleInformationEntity vehicleInformationEntity = new VehicleInformationEntity();
    vehicleInformationEntity.setVpi(vehicleInformation.vpi().get().toString());
    vehicleInformationEntity.setOperationalVersion(0);
    vehicleInformationEntity.setOperational(vehicleInformation.operationalStatus().getOperationalStatus());
    updateVehicleInformationEntity(vehicleInformation, vehicleInformationEntity);
    try {
      Instant start = clock.instant();
      vehicleInformationRepository.save(vehicleInformationEntity);
      dbOperationsMetricReporter.onVehicleInfoCrudDuration(Duration.between(start, clock.instant()), Crud.CREATE);
    } catch (RuntimeException e) {
      dbOperationsMetricReporter.onVehicleInfoCrudFailure(Crud.CREATE);
      throw e;
    }
  }

  private void logDuplicateAssetMetrics(VqvNotification vqvNotification) {
    vqvNotification.vehicleInformation()
        .chassisId()
        .stream()
        .flatMap(chassisId -> vehicleInformationRepository.findAllByChassisId(chassisId.value()).stream())
        .filter(vehicleInformationEntity -> !vehicleInformationEntity.getVpi().equals(vqvNotification.vpi().toString()))
        .findAny()
        .ifPresent(vehicleInformationEntity -> vqvNotificationMetricReporter.onDuplicateChassisId(vehicleInformationEntity.getChassisId()));

    AssetHardwareId assetHardwareId = vqvNotification.vehicleInformation().assetHardwareId();
    vehicleInformationRepository.findAllByPartNumberAndSerialNumber(assetHardwareId.partNumber().value(), assetHardwareId.serialNumber().value())
        .stream()
        .filter(vehicleInformationEntity -> !vehicleInformationEntity.getVpi().equals(vqvNotification.vpi().toString()))
        .findAny()
        .ifPresent(vehicleInformationEntity -> vqvNotificationMetricReporter.onDuplicateAssetHardwareId(assetHardwareId.toString()));
  }

  private void update(VehicleInformationEntity vehicleInformationEntity, VehicleInformation vehicleInformation) {
    if (vehicleInformation.version().version() < vehicleInformationEntity.getVersion()) {
      logger.warn("Received a stale message for vpi {}. Not processing it. Current version {}. Received version {}", vehicleInformation.vpi(),
          vehicleInformationEntity.getVersion(), vehicleInformation.version());
      return;
    }
    updateVehicleInformationEntity(vehicleInformation, vehicleInformationEntity);
    try {
      Instant start = clock.instant();
      vehicleInformationRepository.save(vehicleInformationEntity);
      dbOperationsMetricReporter.onVehicleInfoCrudDuration(Duration.between(start, clock.instant()), Crud.UPDATE);
      logger.debug("updated vehicleInformationEntity: {}", vehicleInformationEntity);
    } catch (RuntimeException e) {
      dbOperationsMetricReporter.onVehicleInfoCrudFailure(Crud.UPDATE);
      throw e;
    }
  }
}
