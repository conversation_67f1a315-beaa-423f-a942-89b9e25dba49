package com.volvo.tisp.mtdisps.impl.conf;

import java.time.Clock;
import java.time.Duration;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoClientSettingsBuilderCustomizer;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.TaskDecorator;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.reactive.function.client.WebClient;

import com.amazon.corretto.crypto.provider.AmazonCorrettoCryptoProvider;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.blackbird.BlackbirdModule;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.volvo.tisp.external.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.framework.jms.DestinationNamingConvention;
import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.TgwMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.VcmMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.converter.mtstatus.ExternalMtStatusConverter;
import com.volvo.tisp.mtdisps.impl.converter.mtstatus.MtStatusConverter;
import com.volvo.tisp.mtdisps.impl.converter.mtstatus.MtStatusWithAssetHardwareIdConverter;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.publisher.TgwMtMessagePublisher;
import com.volvo.tisp.mtdisps.impl.publisher.VcmMtMessagePublisher;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.mtdisps.impl.reporter.VqvNotificationMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.asset.notification.NoOpVqvNotificationProcessor;
import com.volvo.tisp.mtdisps.impl.service.asset.notification.VqvNotificationProcessor;
import com.volvo.tisp.mtdisps.impl.service.asset.notification.VqvNotificationProcessorImpl;
import com.volvo.tisp.mtdisps.impl.service.processor.MtMessageProcessor;
import com.volvo.tisp.mtdisps.impl.service.processor.NoOpMtMessageProcessor;
import com.volvo.tisp.mtdisps.impl.service.processor.TgwMtMessageProcessor;
import com.volvo.tisp.mtdisps.impl.service.processor.VcmMtMessageProcessor;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.wirelesscar.vqv.v1.client.subscription.ViewRegistry;
import com.wirelesscar.vqv.v1.client.subscription.ViewRegistryImpl;

import io.mongock.driver.api.driver.ConnectionDriver;
import io.mongock.driver.mongodb.springdata.v4.SpringDataMongoV4Driver;
import io.mongock.runner.springboot.EnableMongock;

@SpringBootApplication
@ComponentScan(basePackages = {"com.volvo.tisp.mtdisps", "com.volvo.tisp.vc.uncaught.exception.handler"})
@EnableMongoRepositories("com.volvo.tisp.mtdisps.database")
@ConfigurationPropertiesScan
@EnableMongoAuditing
@EnableMongock
@EnableCaching
@EnableScheduling
public class AppConfig {
  public static final String SQS_PROFILE = "sqs";
  private static final Logger logger = LoggerFactory.getLogger(AppConfig.class);

  public static void main(String[] args) {
    installCorrettoCryptoProvider();
    SpringApplication.run(AppConfig.class);
  }

  private static MessagePublisher<ProcessableMtStatus> createExternalMtStatusPublisher(MessagePublisher.Builder builder,
      ExternalMtStatusConverter externalMtStatusConverter) {
    return builder.messageType(MessageTypes.EXTERNAL_MT_STATUS, ProcessableMtStatus.class)
        .version(MessageTypes.VERSION_1_0, externalMtStatusConverter::convert)
        .build();
  }

  private static MessagePublisher<ProcessableMtStatus> createInternalMtStatusWithAssetHardwareIdPublisher(MessagePublisher.Builder builder,
      MtStatusWithAssetHardwareIdConverter mtStatusWithAssetHardwareIdConverter) {
    return builder.messageType(com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.MT_STATUS_WITH_ASSET_HARDWARE_ID, ProcessableMtStatus.class)
        .version(com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.VERSION_1_0, mtStatusWithAssetHardwareIdConverter::convert)
        .build();
  }

  private static MessagePublisher<ProcessableMtStatus> createInternalMtStatusWithVPIPublisher(MessagePublisher.Builder builder,
      MtStatusConverter mtStatusConverter) {
    return builder.messageType(com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.MT_STATUS, ProcessableMtStatus.class)
        .version(com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.VERSION_1_0, mtStatusConverter::convert)
        .build();
  }

  private static void installCorrettoCryptoProvider() {
    if (System.getProperty("os.name", "unknown").toLowerCase(Locale.ROOT).contains("linux") && (
        System.getProperty("os.arch", "unknown").toLowerCase(Locale.ROOT).contains("aarch64") || System.getProperty("os.arch", "unknown")
            .toLowerCase(Locale.ROOT)
            .contains("arm64"))) {
      logger.info("installing AmazonCorrettoCryptoProvider");
      AmazonCorrettoCryptoProvider.install();
      AmazonCorrettoCryptoProvider.INSTANCE.assertHealthy();
      logger.info("installed AmazonCorrettoCryptoProvider");
    }
  }

  @Bean
  public CacheManager cacheManager(@Value("${cache.ttl:PT5S}") Duration cacheTtl, @Value("${cache.initial-size:1000}") int initialSize,
      @Value("${cache.max-size:100000}") int maxSize) {
    CaffeineCacheManager cacheManager = new CaffeineCacheManager();

    cacheManager.setCaffeine(Caffeine.newBuilder().expireAfterWrite(cacheTtl).initialCapacity(initialSize).maximumSize(maxSize));

    return cacheManager;
  }

  @Bean
  public MongoClientSettingsBuilderCustomizer createMongoClient(ConnectionPoolConfigProperties connectionPoolConfigProperties) {
    return builder -> builder.applyToConnectionPoolSettings(connectionPool -> {
      connectionPool.maxSize(connectionPoolConfigProperties.getMaxPoolSize());
      connectionPool.minSize(connectionPoolConfigProperties.getMinPoolSize());
      connectionPool.maxConnectionIdleTime(connectionPoolConfigProperties.getMaxConnectionIdleTime(), TimeUnit.MINUTES);
      connectionPool.maxWaitTime(connectionPoolConfigProperties.getMaxWaitTime(), TimeUnit.MINUTES);
      connectionPool.maxConnectionLifeTime(connectionPoolConfigProperties.getMaxConnectionLifeTime(), TimeUnit.MINUTES);
    });
  }

  @Bean
  @Primary
  public ConnectionDriver createMongockConnection(MongoTemplate mongoTemplate) {
    return SpringDataMongoV4Driver.withDefaultLock(mongoTemplate);
  }

  @Bean
  public ViewRegistry createViewRegistry(WebClient.Builder builder) {
    return new ViewRegistryImpl(builder);
  }

  @Bean
  Clock createClock() {
    return Clock.systemUTC();
  }

  @Bean
  Map<MtMessageType, MessagePublisher<ProcessableMtStatus>> createMtStatusPublishers(MessagePublisher.Builder builder,
      MtStatusWithAssetHardwareIdConverter mtStatusWithAssetHardwareIdConverter, MtStatusConverter mtStatusConverter,
      ExternalMtStatusConverter externalMtStatusConverter) {
    return Map.of(MtMessageType.EXTERNAL_MT_MESSAGE, createExternalMtStatusPublisher(builder, externalMtStatusConverter), MtMessageType.INTERNAL_MT_MESSAGE,
        createInternalMtStatusWithVPIPublisher(builder, mtStatusConverter), MtMessageType.INTERNAL_MT_MESSAGE_WITH_ASSET_HARDWARE_ID,
        createInternalMtStatusWithAssetHardwareIdPublisher(builder, mtStatusWithAssetHardwareIdConverter));
  }

  @Bean("tgwMtMessageProcessor")
  @Conditional(TgwFlowNotEnabledCondition.class)
  MtMessageProcessor createNoOpTgwMtMessageProcessor() {
    return new NoOpMtMessageProcessor();
  }

  @Bean("tgwVqvNotificationProcessor")
  @Conditional(TgwFlowNotEnabledCondition.class)
  VqvNotificationProcessor createNoOpTgwVqvNotificationProcessor() {
    return new NoOpVqvNotificationProcessor();
  }

  @Bean("vcmMtMessageProcessor")
  @Conditional(VcmFlowNotEnabledCondition.class)
  MtMessageProcessor createNoOpVcmMtMessageProcessor() {
    return new NoOpMtMessageProcessor();
  }

  @Bean("vcmVqvNotificationProcessor")
  @Conditional(VcmFlowNotEnabledCondition.class)
  VqvNotificationProcessor createNoOpVcmVqvNotificationProcessor() {
    return new NoOpVqvNotificationProcessor();
  }

  @Bean
  @Primary
  ObjectMapper createObjectMapper() {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    objectMapper.registerModule(new Jdk8Module());
    objectMapper.registerModule(new BlackbirdModule());
    return objectMapper;
  }

  @Bean
  ObjectWriter createObjectWriter(ObjectMapper objectMapper) {
    return objectMapper.writer();
  }

  @Bean
  TaskScheduler createTaskScheduler(TaskDecorator taskDecorator) {
    ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
    threadPoolTaskScheduler.setPoolSize(2);
    threadPoolTaskScheduler.setTaskDecorator(taskDecorator);
    return threadPoolTaskScheduler;
  }

  @Bean("tgwMtMessageProcessor")
  @Conditional({TgwFlowEnabledCondition.class})
  MtMessageProcessor createTgwMtMessageProcessor(DestinationNamingConvention destinationNamingConvention, TgwMtMessageConverter tgwMtMessageConverter,
      TgwMtMessagePublisher tgwMtMessagePublisher) {
    return new TgwMtMessageProcessor(destinationNamingConvention, tgwMtMessageConverter, tgwMtMessagePublisher);
  }

  @Bean("tgwVqvNotificationProcessor")
  @Conditional(TgwFlowEnabledCondition.class)
  VqvNotificationProcessor createTgwVqvNotificationProcessor(Clock clock, DbOperationsMetricReporter dbOperationsMetricReporter,
      VehicleInformationRepository vehicleInformationRepository, VqvNotificationMetricReporter vqvNotificationMetricReporter) {
    return new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter, vehicleInformationRepository, vqvNotificationMetricReporter);
  }

  @Bean("vcmMtMessageProcessor")
  @Conditional({VcmFlowEnabledCondition.class})
  MtMessageProcessor createVcmMtMessageProcessor(VcmMtMessageConverter vcmMtMessageConverter, VcmMtMessagePublisher vcmMtMessagePublisher) {
    return new VcmMtMessageProcessor(vcmMtMessageConverter, vcmMtMessagePublisher);
  }

  @Bean("vcmVqvNotificationProcessor")
  @Conditional(VcmFlowEnabledCondition.class)
  VqvNotificationProcessor createVcmVqvNotificationProcessor(Clock clock, DbOperationsMetricReporter dbOperationsMetricReporter,
      VehicleInformationRepository vehicleInformationRepository, VqvNotificationMetricReporter vqvNotificationMetricReporter) {
    return new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter, vehicleInformationRepository, vqvNotificationMetricReporter);
  }
}
