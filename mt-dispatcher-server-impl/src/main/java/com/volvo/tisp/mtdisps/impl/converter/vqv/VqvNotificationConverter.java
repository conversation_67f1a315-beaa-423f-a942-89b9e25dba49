package com.volvo.tisp.mtdisps.impl.converter.vqv;

import java.io.IOException;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.mtdisps.impl.model.AssetHardwareId;
import com.volvo.tisp.mtdisps.impl.model.ChassisId;
import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.model.TelematicUnitSubType;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.model.Version;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.vqv.v1.api.UpdateNotification;

@Component
public class VqvNotificationConverter implements Function<String, Optional<VqvNotification>> {
  private static final Logger logger = LoggerFactory.getLogger(VqvNotificationConverter.class);

  private static VehicleInformation createVehicleInformation(UpdateNotification<VqvVehicle> updateNotification) {
    VqvVehicle vqvVehicle = updateNotification.getVehicle();
    TelematicUnitType telematicUnitType = TelematicUnitType.fromString(vqvVehicle.telematicUnit().telematicUnitType());

    VehicleInformation.Builder vehicleInformationBuilder = new VehicleInformation.Builder()
        .setChassisId(ChassisId.fromComponents(vqvVehicle.chassisSeries(), vqvVehicle.chassisNumber()))
        .setOperationalStatus(getOperationalStatus(vqvVehicle.operationalStatus()))
        .setSoftCar(findIfSoftCar(vqvVehicle.scarprovInformation()))
        .setTelematicUnitSubType(new TelematicUnitSubType(vqvVehicle.telematicUnit().telematicUnitSubType()))
        .setTelematicUnitType(telematicUnitType)
        .setVersion(new Version(updateNotification.getVehicleVersion()))
        .setVpi(Vpi.ofString(vqvVehicle.vehiclePlatformId()));

    validateForVcmTelematicType(vqvVehicle, telematicUnitType);
    if (!StringUtils.isBlank(vqvVehicle.telematicUnit().partNumber()) && !StringUtils.isBlank(vqvVehicle.telematicUnit().serialNumber())) {
      vehicleInformationBuilder.setAssetHardwareId(AssetHardwareId.of(vqvVehicle.telematicUnit().partNumber(), vqvVehicle.telematicUnit().serialNumber()));
    }
    return vehicleInformationBuilder.build();
  }

  private static VqvNotification createVqvNotification(UpdateNotification<VqvVehicle> updateNotification) {
    return new VqvNotification.Builder()
        .setMasterDataDeletedTimestamp(updateNotification.getMasterDataDeletedTimestamp())
        .setNotificationType(updateNotification.getNotificationType())
        .setVehicleInformation(createVehicleInformation(updateNotification))
        .setVpi(Vpi.ofString(updateNotification.getVehiclePlatformId()))
        .build();
  }

  private static boolean findIfSoftCar(ScarprovInformation scarprovInformation) {
    if (scarprovInformation == null || scarprovInformation.dataItems() == null || scarprovInformation.dataItems().isEmpty()) {
      return false;
    }

    return scarprovInformation.dataItems()
        .stream()
        .filter(dataItems -> Objects.equals(dataItems.key(), "isSoftcar"))
        .map(DataItems::value)
        .anyMatch(s -> Objects.equals(s, "true"));
  }

  private static OperationalStatus getOperationalStatus(String operationalStatusString) {
    return Optional.ofNullable(operationalStatusString)
        .map(s -> s.toUpperCase(Locale.ROOT))
        .map(OperationalStatus::valueOf)
        .orElse(OperationalStatus.NON_OPERATIONAL);
  }

  private static void validateForVcmTelematicType(VqvVehicle vqvVehicle, TelematicUnitType telematicUnitType) {
    if (telematicUnitType == TelematicUnitType.VCM && (StringUtils.isBlank(vqvVehicle.telematicUnit().partNumber()) || StringUtils.isBlank(
        vqvVehicle.telematicUnit().serialNumber()))) {
      throw new IllegalArgumentException("partNumber or serialNumber cannot be null for VCM telematicUnitType");
    }
  }

  @Override
  public Optional<VqvNotification> apply(String string) {
    try {
      UpdateNotification<VqvVehicle> updateNotification = UpdateNotification.fromMessage(VqvVehicle.class, string);
      return Optional.of(createVqvNotification(updateNotification));
    } catch (IOException e) {
      logger.warn("unable to fetch string: {}", string, e);
      return Optional.empty();
    } catch (RuntimeException e) {
      logger.warn("input validation failed: {}", string, e);
      return Optional.empty();
    }
  }
}
