package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.model.AssetHardwareId;
import com.volvo.tisp.mtdisps.impl.model.MultipleAssetsFoundException;
import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.model.Version;

/**
 When unIdentifiedAssetsAllowed is true and vehicleInformation is not found, fetch method returns a made up VCM Vehicle Information with the provided assetHardwareId
 */
@Component
class AssetHardwareIdVehicleInformationFetcher implements VehicleInformationFetcher {
  private final VehicleInformationRepository vehicleInformationRepository;
  private final boolean unIdentifiedAssetsAllowed;

  public AssetHardwareIdVehicleInformationFetcher(VehicleInformationRepository vehicleInformationRepository,
      @Value("${unidentified-assets.allowed:false}") boolean unIdentifiedAssetsAllowed) {
    this.vehicleInformationRepository = vehicleInformationRepository;
    this.unIdentifiedAssetsAllowed = unIdentifiedAssetsAllowed;
  }

  @Override
  public Optional<VehicleInformation> fetch(String assetIdentifier, boolean isActivation) throws MultipleAssetsFoundException {
    if (StringUtils.isAllBlank(assetIdentifier)) {
      return Optional.empty();
    }

    AssetHardwareId assetHardwareId = AssetHardwareId.fromString(assetIdentifier);
    Optional<VehicleInformation> vehicleInformationOptional = doFetch(assetHardwareId, isActivation).map(VehicleInformation::fromEntity);

    if (vehicleInformationOptional.isEmpty() && unIdentifiedAssetsAllowed) {
      VehicleInformation unIdentifiedVCMVehicleInformation = buildUnIdentifiedVCMVehicleInformation(assetHardwareId);
      return Optional.of(unIdentifiedVCMVehicleInformation);
    } else {
      return vehicleInformationOptional;
    }
  }

  private static VehicleInformation buildUnIdentifiedVCMVehicleInformation(AssetHardwareId assetHardwareId) {
    return new VehicleInformation(Optional.empty(), OperationalStatus.OPERATIONAL, assetHardwareId, false, Optional.empty(), TelematicUnitType.VCM,
        new Version(1L), Optional.empty());
  }

  private Optional<VehicleInformationEntity> doFetch(AssetHardwareId assetHardwareId, boolean isActivation) throws MultipleAssetsFoundException {
    try {
      if (isActivation) {
        return vehicleInformationRepository.findByPartNumberAndSerialNumber(assetHardwareId.partNumber().value(), assetHardwareId.serialNumber().value());
      }

      return vehicleInformationRepository.findByPartNumberAndSerialNumberAndOperationalIsTrue(assetHardwareId.partNumber().value(),
          assetHardwareId.serialNumber().value());
    } catch (IncorrectResultSizeDataAccessException e) {
      throw new MultipleAssetsFoundException(assetHardwareId.toString());
    }
  }

  @Override
  public AssetIdentifierType getSupportedAssetIdentifierType() {
    return AssetIdentifierType.ASSET_HARDWARE_ID;
  }
}
