package com.volvo.tisp.mtdisps.impl.jwt;

import java.time.Clock;
import java.time.Instant;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.proc.BadJWTException;
import com.nimbusds.jwt.proc.JWTClaimsSetVerifier;
import com.nimbusds.jwt.util.DateUtils;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class Idpm2mScopeVerifier implements JWTClaimsSetVerifier<ServiceSecurityContext> {
  private static final Logger logger = LoggerFactory.getLogger(Idpm2mScopeVerifier.class);

  private final Clock clock;

  public Idpm2mScopeVerifier(Clock clock) {
    Validate.notNull(clock, "clock");

    this.clock = clock;
  }

  private static Date getMessageHandlerExpiration(JWTClaimsSet claimsSet) throws BadJWTException {
    Object expMh = claimsSet.getClaim("exp_mh");

    if (expMh instanceof Date date) {
      return date;
    } else if (expMh instanceof Number number) {
      return DateUtils.fromSecondsSinceEpoch((number).longValue());
    } else {
      throw new BadJWTException("exp_mh missing from claims");
    }
  }

  private static String getScope(JWTClaimsSet claimsSet) throws BadJWTException {
    Object scope = claimsSet.getClaim("scope");

    if (!(scope instanceof String)) {
      throw new BadJWTException("scope missing from token");
    }

    return (String) scope;
  }

  private static void verifyExpiration(Instant now, Date expires) throws BadJWTException {
    if (now.isAfter(expires.toInstant())) {
      logger.warn("token has expired, now {}, expiration {}", now, expires);
      throw new BadJWTException("token has expired " + expires);
    }
  }

  private static void verifyIssuedAt(Instant now, Date issued) throws BadJWTException {
    if (now.isBefore(issued.toInstant())) {
      logger.warn("token issued in the future, now {}, issued {}", now, issued);
      throw new BadJWTException("future token is not valid " + issued);
    }
  }

  private static void verifyScope(ServiceSecurityContext context, String scope) throws BadJWTException {
    if (!context.isValidClaim(scope)) {
      logger.warn("invalid scope, was {}, expected serviceId {}", scope, context);
      throw new BadJWTException("scope is invalid " + scope);
    }
  }

  @Override
  public void verify(JWTClaimsSet claimsSet, ServiceSecurityContext context) throws BadJWTException {
    Instant now = clock.instant();

    verifyScope(context, getScope(claimsSet));
    verifyExpiration(now, getMessageHandlerExpiration(claimsSet));
    verifyIssuedAt(now, claimsSet.getIssueTime());
  }
}
