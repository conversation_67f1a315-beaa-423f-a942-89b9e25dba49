package com.volvo.tisp.mtdisps.impl.reporter;

import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.model.InterfaceType;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class MtMessageMetricReporter {
  private static final String INTERFACE_TYPE = "interface.type";
  private static final String MT_MESSAGE_FAILED = "mt.message.failed";
  private static final String MT_MESSAGE_RECEIVED = "mt.message.received";
  private static final String REASON = "reason";
  private final MeterRegistry meterRegistry;

  public MtMessageMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onMtMessageFailure(ValidationFailureReason validationFailureReason) {
    Validate.notNull(validationFailureReason, REASON);

    meterRegistry.counter(MT_MESSAGE_FAILED, Tags.of(REASON, validationFailureReason.name())).increment();
  }

  public void onMtMessageReceived(InterfaceType interfaceType) {
    Validate.notNull(interfaceType, INTERFACE_TYPE);

    meterRegistry.counter(MT_MESSAGE_RECEIVED, Tags.of(INTERFACE_TYPE, interfaceType.name())).increment();
  }
}
