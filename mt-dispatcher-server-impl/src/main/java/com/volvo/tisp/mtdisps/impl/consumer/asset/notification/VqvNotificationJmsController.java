package com.volvo.tisp.mtdisps.impl.consumer.asset.notification;

import java.util.Optional;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.JmsException;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.mtdisps.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.mtdisps.impl.reporter.VqvNotificationMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.asset.notification.VqvNotificationDomainManager;
import com.wirelesscar.vqv.v1.api.UpdateNotificationMessageType;

@JmsController(destination = VqvNotificationJmsController.VQV_NOTIFICATION_DESTINATION)
public class VqvNotificationJmsController {
  public static final String VQV_NOTIFICATION_DESTINATION = "VQV.IN";
  private static final Logger logger = LoggerFactory.getLogger(VqvNotificationJmsController.class);

  private final Function<String, Optional<VqvNotification>> vqvNotificationConverter;
  private final VqvNotificationMetricReporter vqvNotificationMetricReporter;
  private final VqvNotificationDomainManager vqvNotificationDomainManager;

  public VqvNotificationJmsController(Function<String, Optional<VqvNotification>> vqvNotificationConverter,
      VqvNotificationDomainManager vqvNotificationDomainManager, VqvNotificationMetricReporter vqvNotificationMetricReporter) {
    this.vqvNotificationConverter = vqvNotificationConverter;
    this.vqvNotificationDomainManager = vqvNotificationDomainManager;
    this.vqvNotificationMetricReporter = vqvNotificationMetricReporter;
  }

  @JmsMessageMapping(
      consumesType = UpdateNotificationMessageType.VIEW_NOTIFICATION_MESSAGE_TYPE,
      consumesVersion = UpdateNotificationMessageType.VIEW_NOTIFICATION_MESSAGE_TYPE_VERSION)
  public void handleNotification(final JmsMessage<String> jmsMessage) throws JmsException {
    logger.debug("received jmsMessage: {}", jmsMessage);

    vqvNotificationConverter
        .apply(jmsMessage.payload())
        .ifPresentOrElse(vqvNotificationDomainManager::process, this::onEmpty);
  }

  private void onEmpty() {
    vqvNotificationMetricReporter.onConversionFailure();
  }
}
