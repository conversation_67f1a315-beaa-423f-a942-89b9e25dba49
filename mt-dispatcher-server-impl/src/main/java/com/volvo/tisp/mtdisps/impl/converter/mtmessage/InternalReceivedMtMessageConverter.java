package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Base64;
import java.util.Optional;

import org.springframework.stereotype.Component;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.dto.PayloadSignatureDetails;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtMessage;
import com.volvo.tisp.mtdisps.impl.model.AcceptedCost;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.Priority;
import com.volvo.tisp.mtdisps.impl.model.ServiceAccessToken;
import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ServiceVersion;
import com.volvo.tisp.mtdisps.impl.model.Ttl;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessageWithAssetHardwareId;

@Component
public class InternalReceivedMtMessageConverter implements ReceivedMtMessageConverter {
  private static Optional<PayloadSignatureDetails> getPayloadSignatureDetails(MtMessage internalMtMessage) {
    return Optional.ofNullable(internalMtMessage.getPayloadSignature())
        .flatMap(payloadSignature -> Optional.ofNullable(internalMtMessage.getKeyId())
            .map(keyId -> new PayloadSignatureDetails(keyId, payloadSignature)));
  }

  private static Optional<PayloadSignatureDetails> getPayloadSignatureDetails(MtMessageWithAssetHardwareId internalMtMessage) {
    return Optional.ofNullable(internalMtMessage.getPayloadSignature())
        .flatMap(payloadSignature -> Optional.ofNullable(internalMtMessage.getKeyId())
            .map(keyId -> new PayloadSignatureDetails(keyId, payloadSignature)));
  }

  @Override
  public Optional<ReceivedMtMessage> convert(Object mtMessage) {
    try {
      if (mtMessage instanceof MtMessage internalMtMessage) {
        return Optional.of(new ReceivedMtMessage(AcceptedCost.valueOf(internalMtMessage.getAcceptedCost().name()),
            internalMtMessage.getVehiclePlatformId(),
            AssetIdentifierType.VPI,
            Optional.ofNullable(internalMtMessage.getCorrelationId()).map(CorrelationId::ofString),
            new ServiceFunction(internalMtMessage.getServiceFunction()),
            internalMtMessage.isOverride(),
            ImmutableByteArray.of(Base64.getDecoder().decode(internalMtMessage.getPayload())),
            getPayloadSignatureDetails(internalMtMessage),
            Optional.ofNullable(internalMtMessage.getPriority()).map(priority -> Priority.valueOf(priority.name())),
            new ServiceAccessToken(internalMtMessage.getServiceAccessToken()),
            new ServiceId(internalMtMessage.getServiceId()),
            new ServiceVersion(internalMtMessage.getServiceVersion()),
            TrackingIdentifier.fromString(internalMtMessage.getTrackingId()),
            Ttl.valueOf(internalMtMessage.getTtl().name()),
            MtMessageType.INTERNAL_MT_MESSAGE));
      } else if (mtMessage instanceof MtMessageWithAssetHardwareId internalMtMessage) {
        return Optional.of(new ReceivedMtMessage(AcceptedCost.valueOf(internalMtMessage.getAcceptedCost().name()),
            internalMtMessage.getAssetHardwareId(),
            AssetIdentifierType.ASSET_HARDWARE_ID,
            Optional.ofNullable(internalMtMessage.getCorrelationId()).map(CorrelationId::ofString),
            new ServiceFunction(internalMtMessage.getServiceFunction()),
            internalMtMessage.isOverride(),
            ImmutableByteArray.of(Base64.getDecoder().decode(internalMtMessage.getPayload())),
            getPayloadSignatureDetails(internalMtMessage),
            Optional.ofNullable(internalMtMessage.getPriority()).map(priority -> Priority.valueOf(priority.name())),
            new ServiceAccessToken(internalMtMessage.getServiceAccessToken()),
            new ServiceId(internalMtMessage.getServiceId()),
            new ServiceVersion(internalMtMessage.getServiceVersion()),
            TrackingIdentifier.fromString(internalMtMessage.getTrackingId()),
            Ttl.valueOf(internalMtMessage.getTtl().name()),
            MtMessageType.INTERNAL_MT_MESSAGE_WITH_ASSET_HARDWARE_ID));
      } else {
        return Optional.empty();
      }
    } catch (IllegalArgumentException e) {
      return Optional.empty();
    }
  }
}
