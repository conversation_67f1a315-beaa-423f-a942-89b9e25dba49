package com.volvo.tisp.mtdisps.impl.reporter;

import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class AssetOperationalNotificationMetricReporter {
  private static final String NAME = "va.operational-status";
  private static final String TYPE = "type";

  private final MeterRegistry meterRegistry;

  public AssetOperationalNotificationMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onFailure(ValidationFailureReason validationFailureReason) {
    Validate.notNull(validationFailureReason, "validationFailureReason");

    meterRegistry.counter(NAME + ".failure", Tags.of(TYPE, validationFailureReason.name())).increment();
  }
}
