package com.volvo.tisp.mtdisps.impl.converter.vqv;

import java.util.Optional;

import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.model.Version;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.va.v1.VehicleNonoperationalNotification;
import com.wirelesscar.va.v1.VehicleOperationalNotification;

@Component
public class AssetOperationalStatusNotificationConverter {
  public Optional<AssetOperationalStatusRecord> convert(VehicleOperationalNotification vehicleOperationalNotification) {
    try {
      return Optional.of(new AssetOperationalStatusRecord(Vpi.ofString(vehicleOperationalNotification.getVehiclePlatformId()),
          new Version(vehicleOperationalNotification.getVehicleVersion().longValue()),
          OperationalStatus.OPERATIONAL)
      );
    } catch (Exception e) {
      return Optional.empty();
    }
  }

  public Optional<AssetOperationalStatusRecord> convert(VehicleNonoperationalNotification vehicleNonoperationalNotification) {
    try {
      return Optional.of(new AssetOperationalStatusRecord(Vpi.ofString(vehicleNonoperationalNotification.getVehiclePlatformId()),
          new Version(vehicleNonoperationalNotification.getVehicleVersion().longValue()),
          OperationalStatus.NON_OPERATIONAL));
    } catch (Exception e) {
      return Optional.empty();
    }
  }
}
