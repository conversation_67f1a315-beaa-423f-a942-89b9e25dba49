package com.volvo.tisp.mtdisps.impl.data.rest;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.mtdisps.impl.conf.LoadTestConfig;
import com.volvo.tisp.mtdisps.impl.data.VehicleInformationGenerator;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@RequestMapping(VehicleInformationRestController.PATH)
@RestController
@Authentication(required = false)
@Profile(LoadTestConfig.LOAD_TEST_PROFILE)
public class VehicleInformationRestController {
  static final int MAX_VALUE = 10000000;
  static final String PATH = "/api/v1/vehicle-information";
  private static final Logger logger = LoggerFactory.getLogger(VehicleInformationRestController.class);

  private final Clock clock;
  private final VehicleInformationGenerator vehicleInformationGenerator;

  public VehicleInformationRestController(Clock clock, VehicleInformationGenerator vehicleInformationGenerator) {
    this.clock = clock;
    this.vehicleInformationGenerator = vehicleInformationGenerator;
  }

  @GetMapping("/generate")
  public ResponseEntity<String> generateVehicles(
      @RequestParam(name = "numberOfVehicles", defaultValue = "100000") int numberOfVehicles,
      @RequestParam(name = "vcmPercentage", defaultValue = "5") int vcmPercentage,
      @RequestParam(name = "outputFilePath", defaultValue = "vehicle.csv") String outputFilePath) {
    Validate.isPositiveAndNotGreaterThan(numberOfVehicles, MAX_VALUE, "numberOfVehicles");
    Validate.isPercentage(vcmPercentage, "vcmPercentage");
    Validate.notNull(outputFilePath, "outputFilePath");

    logger.info("Generate vehicle information begins..");
    Instant start = clock.instant();
    Path outputFilePathObj = Paths.get(outputFilePath).toAbsolutePath();

    int numbeOfVehicles = vehicleInformationGenerator.generateVehicleInformation(numberOfVehicles, vcmPercentage, outputFilePathObj);
    Duration end = Duration.between(start, clock.instant());
    logger.info("Number of vehicle created: {} in {}", numbeOfVehicles, end);
    return ResponseEntity.ok(numbeOfVehicles + " vehicleInformation have been generated in " + end);
  }
}
