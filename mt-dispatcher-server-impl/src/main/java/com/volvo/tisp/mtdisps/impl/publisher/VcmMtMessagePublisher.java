package com.volvo.tisp.mtdisps.impl.publisher;

import java.time.Clock;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.integration.log.IntegrationLogEventPublisher;
import com.volvo.tisp.integration.log.v2.AssetIdentifier;
import com.volvo.tisp.integration.log.v2.IntegrationLogEvent;
import com.volvo.tisp.mtdisps.impl.conf.AppProperties;
import com.volvo.tisp.mtdisps.impl.conf.VcmFlowEnabledCondition;
import com.volvo.tisp.mtdisps.impl.dto.LogType;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.reporter.MessagePublisherMetricReporter;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;
import com.volvo.tisp.vc.amtss.client.protobuf.MessageTypes;

@Component
@Conditional({VcmFlowEnabledCondition.class})
public class VcmMtMessagePublisher extends AsyncMessagePublisher<AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage> {
  public static final Logger logger = LoggerFactory.getLogger(VcmMtMessagePublisher.class);
  private final AppProperties appProperties;
  private final Clock clock;
  private final IntegrationLogEventPublisher integrationLogEventPublisher;
  private final JmsTemplate jmsTemplate;
  private final String queueName;

  public VcmMtMessagePublisher(AppProperties appProperties, Clock clock, IntegrationLogEventPublisher integrationLogEventPublisher,
      JmsTemplate jmsTemplate, MessagePublisherMetricReporter messagePublisherMetricReporter, @Value("${jms.amtss.mt.message.queue.name}") String queueName) {
    super(messagePublisherMetricReporter, FlowType.VCM);

    this.appProperties = appProperties;
    this.clock = clock;
    this.integrationLogEventPublisher = integrationLogEventPublisher;
    this.jmsTemplate = jmsTemplate;
    this.queueName = queueName;

  }

  @Override
  protected CompletableFuture<Void> doPublishAsync(AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage assetMtSchedulerMtMessage) {
    try {
      jmsTemplate.convertAndSend(queueName, assetMtSchedulerMtMessage, message -> {
        message.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), MessageTypes.AMTSS_ASSET_MT_SCHEDULER_MT_MESSAGE);
        message.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), MessageTypes.VERSION_1_0);
        return message;
      });
      return CompletableFuture.completedFuture(null);
    } catch (Exception e) {
      return CompletableFuture.failedFuture(e);
    }
  }

  @Override
  @SuppressWarnings("FutureReturnValueIgnored")
  protected void integrationLog(AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage mtMessage, LogType logType) {
    if (appProperties.isIntegrationLoggingEnabled()) {
      IntegrationLogEvent integrationLogEvent = new IntegrationLogEvent()
          .withTrackingId(mtMessage.getTrackingId())
          .withAssetIdentifiers(Set.of(new AssetIdentifier().withType(AssetIdentifier.Type.ASSET_ID).withAssetIdentifier(mtMessage.getAssetHardwareId())))
          .withId(UUID.randomUUID())
          .withSource("MTDISPS")
          .withTimestamp(clock.instant().toEpochMilli())
          .withDetail(Map.of("ServiceId", mtMessage.getServiceId(),
              "isSoftcar", mtMessage.getIsSoftcar(),
              "publisherType", "VCM"))
          .withDetailType(logType == LogType.SUCCESS ? "VcmMtMessagePublishedSuccess" : "VcmMtMessagePublishedFail")
          .withVersion(IntegrationLogEvent.Version._2);
      integrationLogEventPublisher.publish(integrationLogEvent);
    }
  }
}
