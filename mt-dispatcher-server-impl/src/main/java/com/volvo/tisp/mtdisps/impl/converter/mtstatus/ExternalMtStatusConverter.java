package com.volvo.tisp.mtdisps.impl.converter.mtstatus;

import java.util.Optional;

import org.springframework.stereotype.Component;

import com.volvo.tisp.external.mt.message.client.json.v1.MtStatus;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public final class ExternalMtStatusConverter {
  public MtStatus convert(ProcessableMtStatus processableMtStatus) {
    Validate.notNull(processableMtStatus, "processableMtStatus");

    return new MtStatus()
        .withCorrelationId(processableMtStatus.correlationId().toString())
        .withDescription(Optional.ofNullable(processableMtStatus.description()).orElse(processableMtStatus.status().toString()))
        .withStatus(toStatus(processableMtStatus.status()))
        .withTrackingId(processableMtStatus.trackingIdentifier().toString());
  }

  private MtStatus.Status toStatus(com.volvo.tisp.mtdisps.impl.dto.Status status) {
    return switch (status) {
      case ASSET_NOT_FOUND -> MtStatus.Status.ASSET_NOT_FOUND;
      case CANCELED -> MtStatus.Status.CANCELED;
      case DELIVERED -> MtStatus.Status.DELIVERED;
      case FAILED, OVERRIDDEN -> MtStatus.Status.FAILED;
      case INVALID_TOKEN -> MtStatus.Status.INVALID_TOKEN;
      case NO_PERMISSION -> MtStatus.Status.NO_PERMISSION;
      case MULTIPLE_ASSETS_FOUND -> MtStatus.Status.MULTIPLE_ASSETS_CONFLICT;
      case TIMEOUT -> MtStatus.Status.TIMEOUT;
      case UNSUPPORTED_SERVICE -> MtStatus.Status.UNSUPPORTED_SERVICE;
      case UNSUPPORTED_VERSION -> MtStatus.Status.UNSUPPORTED_VERSION;
    };
  }
}
