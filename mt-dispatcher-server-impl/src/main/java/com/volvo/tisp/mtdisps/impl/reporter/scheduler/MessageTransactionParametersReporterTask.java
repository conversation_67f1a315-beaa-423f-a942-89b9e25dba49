package com.volvo.tisp.mtdisps.impl.reporter.scheduler;

import java.time.Instant;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;

@Component
public class MessageTransactionParametersReporterTask {
  private static final Logger logger = LoggerFactory.getLogger(MessageTransactionParametersReporterTask.class);

  private final DbOperationsMetricReporter dbOperationsMetricReporter;
  private final MessageTransactionParametersRepository messageTransactionParametersRepository;

  public MessageTransactionParametersReporterTask(DbOperationsMetricReporter dbOperationsMetricReporter,
      MessageTransactionParametersRepository messageTransactionParametersRepository) {
    this.dbOperationsMetricReporter = dbOperationsMetricReporter;
    this.messageTransactionParametersRepository = messageTransactionParametersRepository;
  }

  @Scheduled(fixedRateString = "${message.transaction.parameters.db.report-interval:60}", timeUnit = TimeUnit.MINUTES)
  public void getExpiredMessageCountBeforeCleanup() {
    try {
      long recordsBeforeCurrentTime = messageTransactionParametersRepository.countRecordsBeforeCurrentTime(Instant.now());
      dbOperationsMetricReporter.onExpiredMessageTransactionCount(recordsBeforeCurrentTime);

      logger.debug("Number of mt messages awaiting to be cleaned in DB: {},", recordsBeforeCurrentTime);
    } catch (RuntimeException e) {
      logger.error("", e);
    }
  }
}
