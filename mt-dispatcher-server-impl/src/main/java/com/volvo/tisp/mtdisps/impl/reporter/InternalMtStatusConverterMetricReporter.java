package com.volvo.tisp.mtdisps.impl.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class InternalMtStatusConverterMetricReporter {
  public static final String INTERNAL_MT_STATUS_CONVERTER = "internal-mt-status-converter";
  public static final String MISSING_TAG = "missing";
  private final Counter missingVpiCounter;
  private final Counter missingAssetHardwareId;

  public InternalMtStatusConverterMetricReporter(MeterRegistry meterRegistry) {
    missingVpiCounter = meterRegistry.counter(INTERNAL_MT_STATUS_CONVERTER, Tags.of(MISSING_TAG, "vpi"));
    missingAssetHardwareId = meterRegistry.counter(INTERNAL_MT_STATUS_CONVERTER, Tags.of(MISSING_TAG, "asset-hardware-id"));
  }

  public void onMissingAssetHardwareId() {
    missingAssetHardwareId.increment();
  }

  public void onMissingVpi() {
    missingVpiCounter.increment();
  }
}
