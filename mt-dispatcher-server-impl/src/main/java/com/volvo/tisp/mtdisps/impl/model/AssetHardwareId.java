package com.volvo.tisp.mtdisps.impl.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public record AssetHardwareId(PartNumber partNumber, SerialNumber serialNumber) {
  private static final String DELIMITER = "-";

  public AssetHardwareId {
    Validate.notNull(partNumber, "partNumber");
    Validate.notNull(serialNumber, "serialNumber");
  }

  public static AssetHardwareId fromString(String assetHardwareId) {
    Validate.notEmpty(assetHardwareId, "assetHardwareId");

    String[] splitParts = assetHardwareId.split(DELIMITER);
    if (splitParts.length != 2) {
      throw new IllegalArgumentException("Invalid assetHardwareId");
    }

    return new AssetHardwareId(new PartNumber(splitParts[0]), new SerialNumber(splitParts[1]));
  }

  public static AssetHardwareId of(String partNumber, String serialNumber) {
    return new AssetHardwareId(new PartNumber(partNumber), new SerialNumber(serialNumber));
  }

  @Override
  public String toString() {
    return partNumber.value() + DELIMITER + serialNumber.value();
  }
}
