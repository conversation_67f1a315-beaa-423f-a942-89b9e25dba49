package com.volvo.tisp.mtdisps.impl.dto;

import java.util.Optional;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.model.AcceptedCost;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.Priority;
import com.volvo.tisp.mtdisps.impl.model.ServiceAccessToken;
import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ServiceVersion;
import com.volvo.tisp.mtdisps.impl.model.Ttl;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;

public record ReceivedMtMessage(
    AcceptedCost acceptedCost,
    String assetIdentifier,
    AssetIdentifierType assetIdentifierType,
    Optional<CorrelationId> correlationId,
    ServiceFunction serviceFunction,
    boolean override,
    ImmutableByteArray payload,
    Optional<PayloadSignatureDetails> payloadSignatureDetails,
    Optional<Priority> priority,
    ServiceAccessToken serviceAccessToken,
    ServiceId serviceId,
    ServiceVersion serviceVersion,
    TrackingIdentifier trackingIdentifier,
    Ttl ttl,
    MtMessageType mtMessageType) {
  public ReceivedMtMessage {
    Validate.notNull(acceptedCost, "acceptedCost");
    Validate.notNull(assetIdentifier, "assetIdentifier");
    Validate.notNull(assetIdentifierType, "assetIdentifierType");
    Validate.notNull(correlationId, "correlationId");
    Validate.notNull(serviceFunction, "serviceFunction");
    Validate.notNull(payload, "payload");
    Validate.notNull(priority, "priority");
    Validate.notNull(serviceId, "serviceId");
    Validate.notNull(serviceVersion, "serviceVersion");
    Validate.notNull(trackingIdentifier, "trackingIdentifier");
    Validate.notNull(ttl, "ttl");
    Validate.notNull(payloadSignatureDetails, "payloadSignatureDetails");
    Validate.notNull(mtMessageType, "mtMessageType");
  }
}
