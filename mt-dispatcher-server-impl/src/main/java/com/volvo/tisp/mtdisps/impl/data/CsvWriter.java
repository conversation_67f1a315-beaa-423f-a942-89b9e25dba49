package com.volvo.tisp.mtdisps.impl.data;

import java.io.Closeable;
import java.io.IOException;
import java.io.Writer;
import java.util.List;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class CsvWriter implements Closeable {
  private static final CSVFormat CSV_FORMAT = CSVFormat.DEFAULT.withQuote(null).withHeader("vpiOption", "telematicUnitType");
  private final CSVPrinter csvPrinter;

  private CsvWriter(CSVPrinter csvPrinter) {
    Validate.notNull(csvPrinter, "csvPrinter");

    this.csvPrinter = csvPrinter;
  }

  public static CsvWriter create(Writer writer) throws IOException {
    Validate.notNull(writer, "writer");

    CSVPrinter csvPrinter = new CSVPrinter(writer, CSV_FORMAT);
    return new CsvWriter(csvPrinter);
  }

  @Override
  public void close() throws IOException {
    csvPrinter.close();
  }

  int writeToCsv(List<String> vehicleInformationDataList) throws IOException {
    Validate.notEmpty(vehicleInformationDataList, "vehicleInformationDataList");

    csvPrinter.printRecords(vehicleInformationDataList);
    csvPrinter.flush();

    return vehicleInformationDataList.size();
  }
}
