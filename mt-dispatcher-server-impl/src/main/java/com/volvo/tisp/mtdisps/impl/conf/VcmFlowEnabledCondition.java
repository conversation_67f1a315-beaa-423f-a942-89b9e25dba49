package com.volvo.tisp.mtdisps.impl.conf;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class VcmFlowEnabledCondition implements Condition {
  @Override
  public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
    Set<String> activeProfiles = Arrays.stream(context.getEnvironment().getActiveProfiles()).map(String::toLowerCase).collect(Collectors.toSet());
    return activeProfiles.contains("vcm");
  }
}
