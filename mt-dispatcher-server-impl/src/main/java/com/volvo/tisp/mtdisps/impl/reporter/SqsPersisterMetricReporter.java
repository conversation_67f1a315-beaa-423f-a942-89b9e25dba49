package com.volvo.tisp.mtdisps.impl.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

@Component
public class SqsPersisterMetricReporter {
  private static final String COMPLETE = "complete";
  private static final String EXCEPTION = "exception";
  private static final String METRIC_NAME = "message-handler-sqs-published";
  private static final String STATE = "state";
  private static final String STATUS = "status";
  private static final String SUCCESS = "success";

  private final Counter exceptionalStatusCounter;
  private final Counter successfulStatusCounter;

  public SqsPersisterMetricReporter(MeterRegistry meterRegistry) {
    successfulStatusCounter = meterRegistry.counter(METRIC_NAME, Tags.of(Tag.of(STATE, COMPLETE), Tag.of(STATUS, SUCCESS)));
    exceptionalStatusCounter = meterRegistry.counter(METRIC_NAME, Tags.of(Tag.of(STATE, COMPLETE), Tag.of(STATUS, EXCEPTION)));
  }

  public void onExceptionalStatus() {
    exceptionalStatusCounter.increment();
  }

  public void onSuccessfulStatus() {
    successfulStatusCounter.increment();
  }
}
