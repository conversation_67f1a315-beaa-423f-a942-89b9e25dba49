package com.volvo.tisp.mtdisps.impl.conf;

import java.util.Arrays;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class TgwFlowEnabledCondition implements Condition {
  @Override
  public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
    return Arrays.stream(context.getEnvironment().getActiveProfiles()).anyMatch(profile -> profile.equalsIgnoreCase("tgw"));
  }
}
