package com.volvo.tisp.mtdisps.impl.data;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.conf.LoadTestConfig;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
@Profile(LoadTestConfig.LOAD_TEST_PROFILE)
public class VehicleInformationGenerator {
  private final int batchSize;
  private final VehicleInformationRepository vehicleInformationRepository;

  public VehicleInformationGenerator(@Value("${mongo.insert.batch.size:10000}") int batchSize, VehicleInformationRepository vehicleInformationRepository) {
    Validate.isPositive(batchSize, "batchSize");

    this.batchSize = batchSize;
    this.vehicleInformationRepository = vehicleInformationRepository;
  }

  public int generateVehicleInformation(int numberOfVehicles, int vcmPercentage, Path outputFilePathObj) {
    Validate.isPositive(numberOfVehicles, "numberOfVehicles");
    Validate.isPercentage(vcmPercentage, "vcmPercentage");
    Validate.notNull(outputFilePathObj, "outputFilePathObj");

    List<String> vehicleInformationCsvList = new ArrayList<>(batchSize);
    List<VehicleInformationEntity> vehicleInformationEntityList = new ArrayList<>(batchSize);

    int totalVehicleInserted = 0;
    int numberOfVcmVehicle = (numberOfVehicles * vcmPercentage) / 100;

    try (CsvWriter csvWriter = CsvWriter.create(Files.newBufferedWriter(outputFilePathObj))) {

      for (int i = 0; i < numberOfVehicles; i++) {
        VehicleInformationEntity vehicleInformationEntity;
        if (i < numberOfVcmVehicle) {
          vehicleInformationEntity = getVehicleInformationEntity(i, TelematicUnitType.VCM);
        } else {
          vehicleInformationEntity = getVehicleInformationEntity(i, TelematicUnitType.TGW);
        }
        vehicleInformationCsvList.add(vehicleInformationEntity.toCsvString());
        vehicleInformationEntityList.add(vehicleInformationEntity);
        if (vehicleInformationCsvList.size() % batchSize == 0) {
          vehicleInformationRepository.saveAll(vehicleInformationEntityList);
          totalVehicleInserted += csvWriter.writeToCsv(vehicleInformationCsvList);
          vehicleInformationEntityList.clear();
          vehicleInformationCsvList.clear();
        }
      }
      if (!vehicleInformationCsvList.isEmpty()) {
        vehicleInformationRepository.saveAll(vehicleInformationEntityList);
        totalVehicleInserted += csvWriter.writeToCsv(vehicleInformationCsvList);
        vehicleInformationEntityList.clear();
        vehicleInformationCsvList.clear();
      }

    } catch (IOException e) {
      vehicleInformationRepository.deleteAll(vehicleInformationEntityList);
    }
    return totalVehicleInserted;
  }

  private VehicleInformationEntity getVehicleInformationEntity(int i, TelematicUnitType telematicUnitType) {
    VehicleInformationEntity vehicleInformationEntity = new VehicleInformationEntity();
    vehicleInformationEntity.setOperational(true);
    vehicleInformationEntity.setPartNumber("00000000");
    vehicleInformationEntity.setSerialNumber(StringUtils.leftPad(String.valueOf(i), 8, "0"));
    vehicleInformationEntity.setSoftCar(false);
    vehicleInformationEntity.setTelematicUnitType(telematicUnitType);
    vehicleInformationEntity.setTelematicUnitSubType(telematicUnitType.name());
    vehicleInformationEntity.setVersion(0);
    vehicleInformationEntity.setVpi(UUID.randomUUID().toString().replace("-", "").toUpperCase(Locale.ENGLISH));

    return vehicleInformationEntity;
  }
}
