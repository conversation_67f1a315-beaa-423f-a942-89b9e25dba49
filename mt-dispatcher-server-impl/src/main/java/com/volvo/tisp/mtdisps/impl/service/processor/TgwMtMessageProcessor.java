package com.volvo.tisp.mtdisps.impl.service.processor;

import java.util.concurrent.CompletableFuture;

import com.volvo.tisp.framework.jms.DestinationNamingConvention;
import com.volvo.tisp.mtdisps.impl.consumer.mtstatus.TgwMtStatusJmsController;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.TgwMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.publisher.TgwMtMessagePublisher;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class TgwMtMessageProcessor implements MtMessageProcessor {
  private final DestinationNamingConvention destinationNamingConvention;
  private final TgwMtMessageConverter tgwMtMessageConverter;
  private final TgwMtMessagePublisher tgwMtMessagePublisher;

  public TgwMtMessageProcessor(DestinationNamingConvention destinationNamingConvention, TgwMtMessageConverter tgwMtMessageConverter,
      TgwMtMessagePublisher tgwMtMessagePublisher) {
    this.destinationNamingConvention = destinationNamingConvention;
    this.tgwMtMessageConverter = tgwMtMessageConverter;
    this.tgwMtMessagePublisher = tgwMtMessagePublisher;
  }

  public CompletableFuture<Void> process(ProcessableMtMessage processableMtMessage) {
    Validate.notNull(processableMtMessage, "processableMtMessage");

    tgwMtMessagePublisher.publish(
        tgwMtMessageConverter.apply(processableMtMessage, destinationNamingConvention.addQueuePrefix(TgwMtStatusJmsController.TGW_MT_STATUS)));
    return CompletableFuture.completedFuture(null);
  }
}
