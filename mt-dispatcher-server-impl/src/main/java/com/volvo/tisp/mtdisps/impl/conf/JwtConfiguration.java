package com.volvo.tisp.mtdisps.impl.conf;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.net.URI;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.time.Clock;
import java.time.Duration;
import java.util.Collections;
import java.util.Set;

import org.bouncycastle.util.io.pem.PemReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.crypto.factories.DefaultJWSVerifierFactory;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.jwk.source.JWKSourceBuilder;
import com.nimbusds.jose.proc.JWSKeySelector;
import com.nimbusds.jose.proc.JWSVerificationKeySelector;
import com.nimbusds.jose.proc.SecurityContext;
import com.nimbusds.jose.proc.SingleKeyJWSKeySelector;
import com.nimbusds.jose.util.DefaultResourceRetriever;
import com.nimbusds.jose.util.ResourceRetriever;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.proc.ConfigurableJWTProcessor;
import com.nimbusds.jwt.proc.DefaultJWTClaimsVerifier;
import com.nimbusds.jwt.proc.DefaultJWTProcessor;
import com.nimbusds.jwt.proc.JWTClaimsSetVerifier;
import com.nimbusds.jwt.proc.JWTProcessor;
import com.volvo.tisp.mtdisps.impl.jwt.Idpm2mScopeVerifier;
import com.volvo.tisp.mtdisps.impl.jwt.ServiceSecurityContext;

/**
 * App can be launched with either a LocalJWTProcessor or IDP JWTProcessor
 */
@Configuration
public class JwtConfiguration {
  private static final Logger logger = LoggerFactory.getLogger(JwtConfiguration.class);

  private static <C extends SecurityContext> JWSKeySelector<C> createIdpJwsKeySelector(
      URI jwksSourceUri,
      ResourceRetriever resourceRetriever) throws MalformedURLException {

    return new JWSVerificationKeySelector<>(Collections.singleton(JWSAlgorithm.ES384),
        (JWKSource<C>) JWKSourceBuilder.create(jwksSourceUri.toURL(), resourceRetriever)
            .cache(true)
            .retrying(true)
            .build());
  }

  private static JWTProcessor<ServiceSecurityContext> createJwtProcessor(
      JWTClaimsSetVerifier<ServiceSecurityContext> claimsVerifier,
      JWSKeySelector<ServiceSecurityContext> keySelector) {
    ConfigurableJWTProcessor<ServiceSecurityContext> jwtProcessor = new DefaultJWTProcessor<>();
    jwtProcessor.setJWSKeySelector(keySelector);
    jwtProcessor.setJWSVerifierFactory(new DefaultJWSVerifierFactory());
    jwtProcessor.setJWTClaimsSetVerifier(claimsVerifier);
    return jwtProcessor;
  }

  private static DefaultJWTClaimsVerifier<ServiceSecurityContext> createLocalClaimsVerifier() {
    JWTClaimsSet claimSet = new JWTClaimsSet.Builder().claim("scope", "msg.read msg.write").build();
    return new DefaultJWTClaimsVerifier<>(null, claimSet, Set.of("scope"), Collections.emptySet());
  }

  private static JWSKeySelector<ServiceSecurityContext> createLocalJWSKeySelector(Resource resource)
      throws IOException, NoSuchAlgorithmException, InvalidKeySpecException {
    try (InputStream inputStream = resource.getInputStream()) {
      KeyFactory keyFactory = KeyFactory.getInstance("EC");
      PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(new PemReader(new InputStreamReader(inputStream)).readPemObject().getContent()));
      return new SingleKeyJWSKeySelector<>(JWSAlgorithm.ES256, publicKey);
    }
  }

  private static ResourceRetriever createResourceRetriever(Duration timeout, int sizeLimitBytes) {
    int millis = (int) timeout.toMillis();
    return new DefaultResourceRetriever(millis, millis, sizeLimitBytes);
  }

  @Bean
  @ConditionalOnProperty(prefix = "idpm2m", name = "jwks-uri")
  JWTProcessor<ServiceSecurityContext> createJwtProcessorResolver(
      Clock clock,
      @Value("${idpm2m.jwks-uri}") URI idpm2mUri,
      @Value("${idpm2m.jwks-timeout:PT5S}") Duration connectTimeout,
      @Value("${idpm2m.jwks-size-limit-bytes:10485760}") int jwksSizeLimitBytes) throws MalformedURLException {
    ResourceRetriever resourceRetriever = createResourceRetriever(connectTimeout, jwksSizeLimitBytes);
    logger.info("using IDP JWTProcessor: {}", idpm2mUri);

    return createJwtProcessor(new Idpm2mScopeVerifier(clock), createIdpJwsKeySelector(idpm2mUri, resourceRetriever));
  }

  @Bean
  @ConditionalOnProperty(prefix = "token-issuer.public-key", name = "path")
  @ConditionalOnMissingBean(JWTProcessor.class)
  JWTProcessor<ServiceSecurityContext> createLocalKeyJwtProcessorResolver(
      @Value("${token-issuer.public-key.path}") String keyPath,
      @Qualifier("webApplicationContext") ResourceLoader resourceLoader) throws IOException, NoSuchAlgorithmException, InvalidKeySpecException {
    logger.info("using local key JWTProcessor: {}", keyPath);

    return createJwtProcessor(createLocalClaimsVerifier(), createLocalJWSKeySelector(resourceLoader.getResource(keyPath)));
  }
}
