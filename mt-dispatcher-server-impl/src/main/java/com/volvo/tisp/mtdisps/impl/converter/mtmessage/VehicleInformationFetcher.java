package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;

import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.model.MultipleAssetsFoundException;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;

public interface VehicleInformationFetcher {
  Optional<VehicleInformation> fetch(String assetIdentifier, boolean isActivation) throws MultipleAssetsFoundException;

  AssetIdentifierType getSupportedAssetIdentifierType();
}
