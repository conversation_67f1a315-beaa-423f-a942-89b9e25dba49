package com.volvo.tisp.mtdisps.impl.consumer.mtstatus;

import java.util.concurrent.CompletableFuture;

import jakarta.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Conditional;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.mtdisps.impl.conf.VcmFlowEnabledCondition;
import com.volvo.tisp.mtdisps.impl.converter.mtstatus.ReceivedVcmMtStatusConverter;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.MtStatusMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.MtStatusDomainManager;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtStatusProtobuf;
import com.volvo.tisp.vc.amtss.client.protobuf.MessageTypes;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@JmsController(destination = VcmMtStatusJmsController.VCM_MT_STATUS)
@Conditional({VcmFlowEnabledCondition.class})
public class VcmMtStatusJmsController {
  public static final String VCM_MT_STATUS = "VCM-MT-STATUS";
  private static final FlowType FLOW_TYPE = FlowType.VCM;
  private static final Logger logger = LoggerFactory.getLogger(VcmMtStatusJmsController.class);

  private final MtStatusDomainManager mtStatusDomainManager;
  private final MtStatusMetricReporter mtStatusMetricReporter;
  private final ReceivedVcmMtStatusConverter receivedVcmMtStatusConverter;

  public VcmMtStatusJmsController(MtStatusDomainManager mtStatusDomainManager, MtStatusMetricReporter mtStatusMetricReporter,
      ReceivedVcmMtStatusConverter receivedVcmMtStatusConverter) {
    this.mtStatusDomainManager = mtStatusDomainManager;
    this.mtStatusMetricReporter = mtStatusMetricReporter;
    this.receivedVcmMtStatusConverter = receivedVcmMtStatusConverter;
  }

  @JmsMessageMapping(consumesType = MessageTypes.AMTSS_ASSET_MT_SCHEDULER_MT_STATUS, consumesVersion = MessageTypes.VERSION_1_0)
  public CompletableFuture<Void> receiveMtStatus(@Valid JmsMessage<AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus> jmsMessage) {
    Validate.notNull(jmsMessage, "jmsMessage");
    try {
      mtStatusMetricReporter.onMtStatusReceived(FLOW_TYPE);

      logger.trace("received jmsMessage: {}", jmsMessage);
      return receivedVcmMtStatusConverter.apply(jmsMessage.payload())
          .map(receivedMtStatus -> mtStatusDomainManager.processMessage(receivedMtStatus, FLOW_TYPE))
          .orElseGet(this::onEmpty);
    } catch (Exception e) {
      return CompletableFuture.failedFuture(e);
    }
  }

  private CompletableFuture<Void> onEmpty() {
    mtStatusMetricReporter.onMtStatusFailure(ValidationFailureReason.CONVERSION_FAILURE, FLOW_TYPE);
    return CompletableFuture.completedFuture(null);
  }
}
