package com.volvo.tisp.mtdisps.impl.service.asset.notification;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.converter.vqv.AssetOperationalStatusRecord;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.AssetOperationalNotificationMetricReporter;
import com.volvo.tisp.mtdisps.impl.reporter.Crud;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class AssetOperationalNotificationDomainManager {
  private static final Logger logger = LoggerFactory.getLogger(AssetOperationalNotificationDomainManager.class);

  private final AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter;
  private final Clock clock;
  private final DbOperationsMetricReporter dbOperationsMetricReporter;
  private final VehicleInformationRepository vehicleInformationRepository;

  public AssetOperationalNotificationDomainManager(Clock clock, DbOperationsMetricReporter dbOperationsMetricReporter,
      VehicleInformationRepository vehicleInformationRepository, AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter) {
    this.clock = clock;
    this.dbOperationsMetricReporter = dbOperationsMetricReporter;
    this.vehicleInformationRepository = vehicleInformationRepository;
    this.assetOperationalNotificationMetricReporter = assetOperationalNotificationMetricReporter;
  }

  private static void updateVehicleInformationEntity(AssetOperationalStatusRecord assetOperationalStatusRecord,
      VehicleInformationEntity vehicleInformationEntity) {
    vehicleInformationEntity.setOperational(assetOperationalStatusRecord.operationalStatus().getOperationalStatus());
    vehicleInformationEntity.setOperationalVersion(assetOperationalStatusRecord.vehicleVersion().version());
  }

  @Transactional
  public void process(AssetOperationalStatusRecord assetOperationalStatusRecord) {
    Validate.notNull(assetOperationalStatusRecord, "assetOperationalStatusRecord");

    Instant start = clock.instant();
    Optional<VehicleInformationEntity> vehicleInformationEntityOptional = vehicleInformationRepository.findByVpi(
        assetOperationalStatusRecord.vpi().toString());
    dbOperationsMetricReporter.onVehicleInfoCrudDuration(Duration.between(start, clock.instant()), Crud.READ);

    vehicleInformationEntityOptional.ifPresentOrElse(vehicleInformationEntity -> update(vehicleInformationEntity, assetOperationalStatusRecord),
        () -> assetOperationalNotificationMetricReporter.onFailure(ValidationFailureReason.ASSET_NOT_FOUND));
  }

  private void update(VehicleInformationEntity vehicleInformationEntity, AssetOperationalStatusRecord assetOperationalStatusRecord) {
    if (assetOperationalStatusRecord.vehicleVersion().version() > vehicleInformationEntity.getOperationalVersion()) {
      updateVehicleInformationEntity(assetOperationalStatusRecord, vehicleInformationEntity);
      try {
        Instant start = clock.instant();
        vehicleInformationRepository.save(vehicleInformationEntity);
        dbOperationsMetricReporter.onVehicleInfoCrudDuration(Duration.between(start, clock.instant()), Crud.UPDATE);
        logger.debug("updated vehicleInformationEntity: {}", vehicleInformationEntity);
      } catch (RuntimeException e) {
        dbOperationsMetricReporter.onVehicleInfoCrudFailure(Crud.UPDATE);
        throw e;
      }
    }
  }

}
