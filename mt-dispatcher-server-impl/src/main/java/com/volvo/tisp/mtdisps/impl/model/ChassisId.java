package com.volvo.tisp.mtdisps.impl.model;

import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public record ChassisId(String value) {
  private static final Pattern CHASSIS_ID_PATTERN = Pattern.compile("^[A-Z0-9]{1,5}-\\d{6}$");

  public ChassisId {
    Validate.notNull(value, "value");
    if (!CHASSIS_ID_PATTERN.asPredicate().test(value)) {
      throw new IllegalArgumentException("Invalid chassisId pattern");
    }
  }

  public static ChassisId fromComponents(String chassisSeries, String chassisNumber) {
    Validate.notEmpty(chassisSeries, "chassisSeries");
    Validate.notEmpty(chassisNumber, "chassisNumber");

    String paddedChassisNumber = StringUtils.leftPad(chassisNumber, 6, '0');
    return new ChassisId(chassisSeries + "-" + paddedChassisNumber);
  }

  @Override
  public String toString() {
    return value;
  }
}
