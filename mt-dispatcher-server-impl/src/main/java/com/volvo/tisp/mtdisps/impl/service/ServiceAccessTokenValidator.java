package com.volvo.tisp.mtdisps.impl.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.nimbusds.jwt.JWT;
import com.nimbusds.jwt.JWTParser;
import com.nimbusds.jwt.proc.JWTProcessor;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.jwt.ServiceSecurityContext;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class ServiceAccessTokenValidator {
  private static final Logger logger = LoggerFactory.getLogger(ServiceAccessTokenValidator.class);

  private final JWTProcessor<ServiceSecurityContext> jwtProcessor;

  public ServiceAccessTokenValidator(JWTProcessor<ServiceSecurityContext> jwtProcessor) {
    this.jwtProcessor = jwtProcessor;
  }

  public void validateServiceAccessToken(ProcessableMtMessage processableMtMessage) throws ValidationException {
    Validate.notNull(processableMtMessage, "processableMtMessage");

    try {
      JWT jwtToken = JWTParser.parse(processableMtMessage.serviceAccessToken().serviceAccessTokenString());
      jwtProcessor.process(jwtToken, ServiceSecurityContext.of(processableMtMessage.serviceId()));
    } catch (Exception e) {
      logger.warn("error while validating token", e);
      throw new ValidationException(ValidationFailureReason.AUTHENTICATION_FAILURE);
    }
  }
}
