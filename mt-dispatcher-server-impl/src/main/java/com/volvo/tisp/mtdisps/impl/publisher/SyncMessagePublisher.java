package com.volvo.tisp.mtdisps.impl.publisher;

import java.time.Duration;
import java.time.Instant;

import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.reporter.MessagePublisherMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public abstract class SyncMessagePublisher<T> {
  private final FlowType flowType;
  private final MessagePublisherMetricReporter messagePublisherMetricReporter;

  protected SyncMessagePublisher(MessagePublisherMetricReporter messagePublisherMetricReporter, FlowType flowType) {
    Validate.notNull(messagePublisherMetricReporter, "messagePublisherMetricReporter");
    Validate.notNull(flowType, "flowType");

    this.messagePublisherMetricReporter = messagePublisherMetricReporter;
    this.flowType = flowType;
  }

  public void publish(T message) {
    Validate.notNull(message, "message");

    Instant start = Instant.now();
    try {
      doPublishSync(message);
      messagePublisherMetricReporter.onMessagePublishDuration(Duration.between(start, Instant.now()), flowType);
    } catch (Exception e) {
      messagePublisherMetricReporter.onMessagePublishFailure(flowType);
      throw e;
    }
  }

  protected abstract void doPublishSync(T message);
}
