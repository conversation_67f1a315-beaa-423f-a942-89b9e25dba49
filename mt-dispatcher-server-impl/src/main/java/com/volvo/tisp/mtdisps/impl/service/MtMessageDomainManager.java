package com.volvo.tisp.mtdisps.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;
import com.volvo.tisp.mtdisps.impl.conf.AppProperties;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.model.Ttl;
import com.volvo.tisp.mtdisps.impl.reporter.Crud;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.processor.MtMessageProcessor;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class MtMessageDomainManager {
  private final AppProperties appProperties;
  private final Clock clock;
  private final DbOperationsMetricReporter dbOperationsMetricReporter;
  private final MessageTransactionParametersRepository messageTransactionParametersRepository;
  private final MtMessageProcessor tgwMtMessageProcessor;
  private final MtMessageProcessor vcmMtMessageProcessor;

  public MtMessageDomainManager(AppProperties appProperties, Clock clock, DbOperationsMetricReporter dbOperationsMetricReporter,
      MessageTransactionParametersRepository messageTransactionParametersRepository,
      @Qualifier("tgwMtMessageProcessor") MtMessageProcessor tgwMtMessageProcessor,
      @Qualifier("vcmMtMessageProcessor") MtMessageProcessor vcmMtMessageProcessor) {
    this.appProperties = appProperties;
    this.clock = clock;
    this.dbOperationsMetricReporter = dbOperationsMetricReporter;
    this.messageTransactionParametersRepository = messageTransactionParametersRepository;
    this.tgwMtMessageProcessor = tgwMtMessageProcessor;
    this.vcmMtMessageProcessor = vcmMtMessageProcessor;
  }

  private static Instant createExpiryAt(Clock clock, int ttl, Duration ttlMargin) {
    return clock.instant().plusSeconds(ttl + ttlMargin.toSeconds());
  }

  public CompletableFuture<Void> processMessage(ProcessableMtMessage processableMtMessage) {
    Validate.notNull(processableMtMessage, "processableMtMessage");

    return saveMessageTransactionParameters(processableMtMessage).thenCompose(
        unused -> switch (processableMtMessage.vehicleInformation().telematicUnitType()) {
          case TGW -> tgwMtMessageProcessor.process(processableMtMessage);
          case VCM -> vcmMtMessageProcessor.process(processableMtMessage);
        }).whenComplete((unused, throwable) -> {
      if (throwable != null) {
        Instant startTime = clock.instant();
        messageTransactionParametersRepository.deleteById(processableMtMessage.correlationId().toString());
        dbOperationsMetricReporter.onMessageTransactionCrudDuration(Duration.between(startTime, clock.instant()), Crud.DELETE);
      }
    });
  }

  private Optional<MessageTransactionParametersEntity> buildMessageTransactionParametersEntity(ProcessableMtMessage processableMtMessage) {
    return processableMtMessage.correlationId().map(correlationId -> {
      Ttl ttl = processableMtMessage.ttl();
      MessageTransactionParametersEntity messageTransactionParametersEntity = new MessageTransactionParametersEntity();
      messageTransactionParametersEntity.setCorrelationId(correlationId.toString());
      messageTransactionParametersEntity.setExpireAt(createExpiryAt(clock, ttl.toSeconds(), appProperties.getTtlMargin()));
      messageTransactionParametersEntity.setServiceFunction(processableMtMessage.serviceFunction().value());
      messageTransactionParametersEntity.setServiceId(processableMtMessage.serviceId().serviceId());
      messageTransactionParametersEntity.setServiceVersion(processableMtMessage.serviceVersion().serviceVersion());
      messageTransactionParametersEntity.setTtl(ttl.toSeconds());
      messageTransactionParametersEntity.setAssetHardwareId(processableMtMessage.vehicleInformation().assetHardwareId().toString());
      messageTransactionParametersEntity.setMtMessageType(processableMtMessage.mtMessageType().name());
      processableMtMessage.vehicleInformation().vpi().ifPresent(vpi -> messageTransactionParametersEntity.setVpi(vpi.toString()));
      return messageTransactionParametersEntity;
    });
  }

  private CompletableFuture<Object> saveMessageTransactionParameters(ProcessableMtMessage processableMtMessage) {
    return buildMessageTransactionParametersEntity(processableMtMessage).map(messageTransactionParametersEntity -> {
      try {
        Instant start = clock.instant();
        messageTransactionParametersRepository.insert(messageTransactionParametersEntity);
        dbOperationsMetricReporter.onMessageTransactionCrudDuration(Duration.between(start, clock.instant()), Crud.CREATE);
        return CompletableFuture.completedFuture(null);
      } catch (DataAccessException e) {
        dbOperationsMetricReporter.onMessageTransactionCrudFailure(Crud.CREATE);
        return CompletableFuture.failedFuture(e);
      }
    }).orElse(CompletableFuture.completedFuture(null));
  }
}
