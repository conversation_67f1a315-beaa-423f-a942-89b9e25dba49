package com.volvo.tisp.mtdisps.impl.consumer.mtstatus;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Conditional;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.mtdisps.impl.conf.TgwFlowEnabledCondition;
import com.volvo.tisp.mtdisps.impl.converter.mtstatus.ReceivedTgwMtStatusMessageConverter;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.MtStatusMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.MtStatusDomainManager;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

@JmsController(destination = TgwMtStatusJmsController.TGW_MT_STATUS)
@Conditional({TgwFlowEnabledCondition.class})
public class TgwMtStatusJmsController {
  public static final String TGW_MT_STATUS = "TGW-MT-STATUS";
  private static final FlowType FLOW_TYPE = FlowType.TGW;
  private static final Logger logger = LoggerFactory.getLogger(TgwMtStatusJmsController.class);

  private final MtStatusDomainManager mtStatusDomainManager;
  private final MtStatusMetricReporter mtStatusMetricReporter;
  private final ReceivedTgwMtStatusMessageConverter receivedTgwMtStatusMessageConverter;

  public TgwMtStatusJmsController(MtStatusDomainManager mtStatusDomainManager, MtStatusMetricReporter mtStatusMetricReporter,
      ReceivedTgwMtStatusMessageConverter receivedTgwMtStatusMessageConverter) {
    this.mtStatusDomainManager = mtStatusDomainManager;
    this.mtStatusMetricReporter = mtStatusMetricReporter;
    this.receivedTgwMtStatusMessageConverter = receivedTgwMtStatusMessageConverter;
  }

  @JmsMessageMapping(consumesType = MessageTypesJms.TCE_MTSTATUS_MESSAGE_TYPE, consumesVersion = MessageTypesJms.VERSION_2_0)
  public CompletableFuture<Void> receiveMtMessage(JmsMessage<MtStatusMessage> jmsMessage) {
    Validate.notNull(jmsMessage, "jmsMessage");
    mtStatusMetricReporter.onMtStatusReceived(FlowType.TGW);

    logger.trace("received jmsMessage: {}", jmsMessage);
    return receivedTgwMtStatusMessageConverter.apply(jmsMessage.payload())
        .map(receivedMtStatus -> mtStatusDomainManager.processMessage(receivedMtStatus, FLOW_TYPE))
        .orElseGet(this::onEmpty);
  }

  private CompletableFuture<Void> onEmpty() {
    mtStatusMetricReporter.onMtStatusFailure(ValidationFailureReason.CONVERSION_FAILURE, FLOW_TYPE);
    return CompletableFuture.completedFuture(null);
  }
}
