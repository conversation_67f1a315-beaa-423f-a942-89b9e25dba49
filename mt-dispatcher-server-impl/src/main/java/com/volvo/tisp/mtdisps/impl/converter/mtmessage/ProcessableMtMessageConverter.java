package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtMessage;
import com.volvo.tisp.mtdisps.impl.model.MultipleAssetsFoundException;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.service.ValidationException;

@Component
public class ProcessableMtMessageConverter {
  private final Map<AssetIdentifierType, VehicleInformationFetcher> vehicleInformationFetchers;

  public ProcessableMtMessageConverter(Collection<VehicleInformationFetcher> vehicleInformationFetchers) {
    this.vehicleInformationFetchers = vehicleInformationFetchers.stream()
        .collect(Collectors.toMap(VehicleInformationFetcher::getSupportedAssetIdentifierType, Function.identity()));
  }

  public ProcessableMtMessage convert(ReceivedMtMessage receivedMtMessage) throws ValidationException {
    try {
      return vehicleInformationFetchers.get(receivedMtMessage.assetIdentifierType())
          .fetch(receivedMtMessage.assetIdentifier(), receivedMtMessage.serviceId().isActivationService())
          .map(vehicleInformation -> new ProcessableMtMessage(vehicleInformation, receivedMtMessage.acceptedCost(),
              receivedMtMessage.correlationId(),
              receivedMtMessage.serviceFunction(),
              receivedMtMessage.override(), receivedMtMessage.payload(), receivedMtMessage.payloadSignatureDetails(),
              receivedMtMessage.priority(), receivedMtMessage.serviceAccessToken(), receivedMtMessage.serviceId(), receivedMtMessage.serviceVersion(),
              receivedMtMessage.trackingIdentifier(), receivedMtMessage.ttl(), receivedMtMessage.mtMessageType()))
          .orElseThrow(() -> new ValidationException(ValidationFailureReason.ASSET_NOT_FOUND));
    } catch (MultipleAssetsFoundException e) {
      throw new ValidationException(ValidationFailureReason.MULTIPLE_ASSETS_FOUND);
    }
  }
}
