package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.StringJoiner;

import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.conf.ServiceFunctionSendSchemaHintConfig;
import com.volvo.tisp.mtdisps.impl.conf.TgwFlowEnabledCondition;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.model.Priority;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.api.v2.SrpLevel;
import com.wirelesscar.tce.api.v2.SrpOption;

@Component
@Conditional({TgwFlowEnabledCondition.class})
public class TgwMtMessageConverter {
  private static final String DELIMITER = "-";

  private final ServiceFunctionSendSchemaHintConfig serviceFunctionSendSchemaHintConfig;

  public TgwMtMessageConverter(ServiceFunctionSendSchemaHintConfig serviceFunctionSendSchemaHintConfig) {
    this.serviceFunctionSendSchemaHintConfig = serviceFunctionSendSchemaHintConfig;
  }

  private static String calculateEnqueueingType(ProcessableMtMessage processableMtMessage) {
    if (isActivationMessage(processableMtMessage)) {
      return "OVERRIDE_IMMEDIATE";
    }

    return processableMtMessage.priority().map(priority -> {
          if (priority.equals(Priority.LOW) || priority.equals(Priority.MID)) {
            return processableMtMessage.overridePreviousMessageType() ? "OVERRIDE" : "NORMAL";
          } else if (priority.equals(Priority.HIGH)) {
            return processableMtMessage.overridePreviousMessageType() ? "OVERRIDE_IMMEDIATE" : "IMMEDIATE";
          } else {
            return "NORMAL";
          }
        }
    ).orElse("NORMAL");
  }

  private static SrpLevel calculateSrpLevel(ProcessableMtMessage processableMtMessage) {
    if (isEncryptedActivationMessage(processableMtMessage)) {
      return SrpLevel.SRP_12;
    }
    return SrpLevel.SRP_11;
  }

  private static String createHint(ProcessableMtMessage processableMtMessage) {
    return new StringJoiner(DELIMITER)
        .add(processableMtMessage.ttl().name())
        .add(processableMtMessage.acceptedCost().name())
        .toString();
  }

  private static MtStatusReplyOption createMtStatusReplyOption(ProcessableMtMessage processableMtMessage, String tgwMtStatusQueueName) {
    return processableMtMessage.correlationId().map(correlationId -> {
      MtStatusReplyOption mtStatusReplyOption = new MtStatusReplyOption();
      mtStatusReplyOption.setCorrelationId(correlationId.toString());
      mtStatusReplyOption.setReplyDestination(tgwMtStatusQueueName);
      return mtStatusReplyOption;
    }).orElse(null);
  }

  private static String createQueueId(ProcessableMtMessage processableMtMessage) {
    return processableMtMessage.serviceId()
        + DELIMITER
        + processableMtMessage.serviceVersion()
        + DELIMITER
        + processableMtMessage.serviceFunction();
  }

  private static SrpOption createSrpOption(ProcessableMtMessage processableMtMessage) {
    SrpOption srpOption = new SrpOption();
    srpOption.setDstService(processableMtMessage.serviceId().serviceId());
    srpOption.setDstVersion(processableMtMessage.serviceVersion().serviceVersion());
    srpOption.setPriority(4);//hardcoded random value
    srpOption.setSrpLevel(calculateSrpLevel(processableMtMessage));
    return srpOption;
  }

  private static boolean isActivationMessage(ProcessableMtMessage processableMtMessage) {
    return processableMtMessage.serviceId().serviceId() == 1;
  }

  private static boolean isEncryptedActivationMessage(ProcessableMtMessage processableMtMessage) {
    return processableMtMessage.serviceId().serviceId() == 1 && processableMtMessage.serviceVersion().serviceVersion() == 2;
  }

  public MtMessage apply(ProcessableMtMessage processableMtMessage, String tgwMtStatusQueueName) {
    Validate.notNull(processableMtMessage, "processableMtMessage");
    Validate.notEmpty(tgwMtStatusQueueName, "tgwMtStatusQueueName");
    Validate.notNull(processableMtMessage.vehicleInformation().vpi().orElse(null), "vpi");

    MtMessage mtMessage = new MtMessage();
    mtMessage.setClientId(processableMtMessage.serviceId().serviceId() + DELIMITER + processableMtMessage.serviceVersion().serviceVersion());
    mtMessage.setMtStatusReplyOption(createMtStatusReplyOption(processableMtMessage, tgwMtStatusQueueName));
    mtMessage.setPayload(processableMtMessage.payload().toByteArray());
    mtMessage.setSchedulerOption(createSchedulerOption(processableMtMessage));
    mtMessage.setSrpOption(createSrpOption(processableMtMessage));
    mtMessage.setVehiclePlatformId(processableMtMessage.vehicleInformation().vpi().get().toString());

    return mtMessage;
  }

  private SchedulerOption createSchedulerOption(ProcessableMtMessage processableMtMessage) {
    SchedulerOption schedulerOption = new SchedulerOption();
    schedulerOption.setEnqueueingType(calculateEnqueueingType(processableMtMessage));
    schedulerOption.setHint(serviceFunctionSendSchemaHintConfig
        .getSendSchemaHint(processableMtMessage.serviceFunction())
        .orElseGet(() -> createHint(processableMtMessage)));
    schedulerOption.setPriority(4);//hardcoded random value
    schedulerOption.setQueueId(createQueueId(processableMtMessage));
    return schedulerOption;
  }
}
