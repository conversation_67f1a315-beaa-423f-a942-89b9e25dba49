package com.volvo.tisp.mtdisps.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;
import com.volvo.tisp.mtdisps.impl.conf.AppProperties;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtStatus;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ServiceVersion;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.Crud;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.mtdisps.impl.reporter.MtStatusMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.processor.MtStatusProcessor;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class MtStatusDomainManager {
  private static final Logger logger = LoggerFactory.getLogger(MtStatusDomainManager.class);

  private final AppProperties appProperties;
  private final Clock clock;
  private final DbOperationsMetricReporter dbOperationsMetricReporter;
  private final MessageTransactionParametersRepository messageTransactionParametersRepository;
  private final MtStatusMetricReporter mtStatusMetricReporter;
  private final MtStatusProcessor mtStatusProcessor;

  public MtStatusDomainManager(AppProperties appProperties, Clock clock, DbOperationsMetricReporter dbOperationsMetricReporter,
      MtStatusMetricReporter mtStatusMetricReporter, MessageTransactionParametersRepository messageTransactionParametersRepository,
      MtStatusProcessor mtStatusProcessor) {
    this.appProperties = appProperties;
    this.clock = clock;
    this.dbOperationsMetricReporter = dbOperationsMetricReporter;
    this.mtStatusMetricReporter = mtStatusMetricReporter;
    this.mtStatusProcessor = mtStatusProcessor;
    this.messageTransactionParametersRepository = messageTransactionParametersRepository;
  }

  private static ProcessableMtStatus createProcessableMtStatus(
      MessageTransactionParametersEntity messageTransactionParametersEntity,
      ReceivedMtStatus receivedMtStatus) {
    return new ProcessableMtStatus(
        receivedMtStatus.correlationId(),
        receivedMtStatus.description(),
        receivedMtStatus.status(),
        receivedMtStatus.trackingIdentifier(),
        new ServiceFunction(messageTransactionParametersEntity.getServiceFunction()),
        new ServiceId(messageTransactionParametersEntity.getServiceId()),
        new ServiceVersion(messageTransactionParametersEntity.getServiceVersion()),
        Optional.ofNullable(messageTransactionParametersEntity.getAssetHardwareId()),
        Optional.ofNullable(messageTransactionParametersEntity.getVpi()).map(Vpi::ofString),
        MtMessageType.valueOf(messageTransactionParametersEntity.getMtMessageType()));
  }

  public CompletableFuture<Void> processMessage(ReceivedMtStatus receivedMtStatus, FlowType flowType) {
    Validate.notNull(receivedMtStatus, "receivedMtStatus");
    Validate.notNull(flowType, "flowType");

    Instant start = clock.instant();
    Optional<MessageTransactionParametersEntity> messageTransactionParametersEntityOptional = messageTransactionParametersRepository.findById(
        receivedMtStatus.correlationId().toString());
    dbOperationsMetricReporter.onMessageTransactionCrudDuration(Duration.between(start, clock.instant()), Crud.READ);

    return messageTransactionParametersEntityOptional.map(
            messageTransactionParametersEntity -> processAndUpdate(messageTransactionParametersEntity, receivedMtStatus))
        .orElseGet(() -> onCorrelationIdNotFound(flowType, receivedMtStatus));
  }

  private void logMtStatusLatencyMetric(MessageTransactionParametersEntity messageTransactionParametersEntity, Instant instant) {
    Duration difference = Duration.between(
        messageTransactionParametersEntity.getExpireAt().minusSeconds(messageTransactionParametersEntity.getTtl() + appProperties.getTtlMargin().toSeconds()),
        instant);
    mtStatusMetricReporter.onMtStatusReceiveLatency(difference);
  }

  private CompletableFuture<Void> onCorrelationIdNotFound(FlowType flowType, ReceivedMtStatus receivedMtStatus) {
    logger.warn("no correlationId found for receivedMtStatus: {}, flowType: {}", receivedMtStatus, flowType);
    mtStatusMetricReporter.onMtStatusFailure(ValidationFailureReason.CORRELATION_ID_NOT_FOUND, flowType);
    return CompletableFuture.completedFuture(null);
  }

  private CompletableFuture<Void> processAndUpdate(MessageTransactionParametersEntity messageTransactionParametersEntity, ReceivedMtStatus receivedMtStatus) {
    return mtStatusProcessor.process(createProcessableMtStatus(messageTransactionParametersEntity, receivedMtStatus))
        .thenAccept(unused -> {
          Instant instant = clock.instant();
          logMtStatusLatencyMetric(messageTransactionParametersEntity, instant);
          messageTransactionParametersEntity.setExpireAt(instant);

          Instant start = clock.instant();
          messageTransactionParametersRepository.save(messageTransactionParametersEntity);
          dbOperationsMetricReporter.onMessageTransactionCrudDuration(Duration.between(start, clock.instant()), Crud.UPDATE);
        });
  }
}
