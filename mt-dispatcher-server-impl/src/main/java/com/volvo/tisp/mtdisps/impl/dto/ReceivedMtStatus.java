package com.volvo.tisp.mtdisps.impl.dto;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record ReceivedMtStatus(
    CorrelationId correlationId,
    String description,
    Status status,
    TrackingIdentifier trackingIdentifier) {

  public ReceivedMtStatus {
    Validate.notNull(correlationId, "correlationId");
    Validate.notNull(status, "status");
    Validate.notNull(trackingIdentifier, "trackingIdentifier");
  }

  public static final class Builder {
    private CorrelationId correlationId;
    private String description;
    private Status status;
    private TrackingIdentifier trackingIdentifier;

    public ReceivedMtStatus build() {
      return new ReceivedMtStatus(correlationId, description, status, trackingIdentifier);
    }

    public Builder setCorrelationId(CorrelationId correlationId) {
      Validate.notNull(correlationId, "correlationId");

      this.correlationId = correlationId;
      return this;
    }

    public Builder setDescription(String description) {
      this.description = description;
      return this;
    }

    public Builder setStatus(Status status) {
      Validate.notNull(status, "status");

      this.status = status;
      return this;
    }

    public Builder setTrackingIdentifier(TrackingIdentifier trackingIdentifier) {
      Validate.notNull(trackingIdentifier, "trackingIdentifier");

      this.trackingIdentifier = trackingIdentifier;
      return this;
    }
  }
}
