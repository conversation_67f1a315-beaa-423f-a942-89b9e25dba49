package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;
import java.util.function.Function;

import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import com.google.protobuf.ByteString;
import com.volvo.tisp.mtdisps.impl.conf.VcmFlowEnabledCondition;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.model.AcceptedCost;
import com.volvo.tisp.mtdisps.impl.model.Priority;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
@Conditional({VcmFlowEnabledCondition.class})
public final class VcmMtMessageConverter implements Function<ProcessableMtMessage, AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage> {
  private static AssetMtSchedulerMtMessageProtobuf.AcceptedCost createAcceptedCostLevel(AcceptedCost acceptedCost) {
    return switch (acceptedCost) {
      case LOW -> AssetMtSchedulerMtMessageProtobuf.AcceptedCost.COST_LOW;
      case NORMAL -> AssetMtSchedulerMtMessageProtobuf.AcceptedCost.COST_MID;
      case HIGH -> AssetMtSchedulerMtMessageProtobuf.AcceptedCost.COST_HIGH;
      case EXTREME -> AssetMtSchedulerMtMessageProtobuf.AcceptedCost.COST_EXTREME;
    };
  }

  private static AssetMtSchedulerMtMessageProtobuf.Priority createPriority(Optional<Priority> priority) {
    return priority.map(value -> switch (value) {
      case LOW -> AssetMtSchedulerMtMessageProtobuf.Priority.PRIORITY_LOW;
      case MID -> AssetMtSchedulerMtMessageProtobuf.Priority.PRIORITY_MID;
      case HIGH -> AssetMtSchedulerMtMessageProtobuf.Priority.PRIORITY_HIGH;
    }).orElse(AssetMtSchedulerMtMessageProtobuf.Priority.PRIORITY_LOW);
  }

  @Override
  public AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage apply(ProcessableMtMessage processableMtMessage) {
    Validate.notNull(processableMtMessage, "processableMtMessage");
    VehicleInformation vehicleInformation = processableMtMessage.vehicleInformation();
    AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage.Builder builder = AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage.newBuilder()
        .setAcceptedCost(createAcceptedCostLevel(processableMtMessage.acceptedCost()))
        .setAssetHardwareId(vehicleInformation.assetHardwareId().toString())
        .setIsSoftcar(vehicleInformation.softCar())
        .setOverride(processableMtMessage.overridePreviousMessageType())
        .setPayload(ByteString.copyFrom(processableMtMessage.payload().toByteArray()))
        .setPriority(createPriority(processableMtMessage.priority()))
        .setServiceAccessToken(processableMtMessage.serviceAccessToken().serviceAccessTokenString())
        .setServiceFunction(processableMtMessage.serviceFunction().value())
        .setServiceId(processableMtMessage.serviceId().serviceId())
        .setServiceVersion(processableMtMessage.serviceVersion().serviceVersion())
        .setTrackingId(processableMtMessage.trackingIdentifier().toString())
        .setTtl(processableMtMessage.ttl().toSeconds());

    processableMtMessage.payloadSignatureDetails().ifPresent(payloadSignatureDetails -> {
          if (payloadSignatureDetails.keyId() != null && !payloadSignatureDetails.keyId().isEmpty()) {
            builder.setKeyId(payloadSignatureDetails.keyId());
          }
          if (payloadSignatureDetails.payloadSignature() != null && !payloadSignatureDetails.payloadSignature().isEmpty()) {
            builder.setPayloadSignature(payloadSignatureDetails.payloadSignature());
          }
        }
    );
    processableMtMessage.correlationId().ifPresent(correlationId -> builder.setCorrelationId(correlationId.toString()));

    return builder.build();
  }
}
