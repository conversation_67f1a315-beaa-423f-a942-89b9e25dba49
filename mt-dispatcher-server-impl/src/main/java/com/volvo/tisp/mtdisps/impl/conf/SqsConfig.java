package com.volvo.tisp.mtdisps.impl.conf;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import io.awspring.cloud.sqs.MessageExecutionThreadFactory;
import io.awspring.cloud.sqs.config.SqsMessageListenerContainerFactory;
import io.awspring.cloud.sqs.listener.BackPressureMode;
import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementOrdering;
import io.awspring.cloud.sqs.listener.acknowledgement.handler.AcknowledgementMode;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.SqsAsyncClientBuilder;

@Configuration
@Profile(AppConfig.SQS_PROFILE)
public class SqsConfig {
  @Bean
  public SqsAsyncClient createSqsClient(SqsClientConfigProperties sqsClientConfigProperties) {
    SqsAsyncClientBuilder sqsAsyncClientBuilder = SqsAsyncClient.builder();
    sqsAsyncClientBuilder.credentialsProvider(DefaultCredentialsProvider.create());
    sqsAsyncClientBuilder.region(Region.of(sqsClientConfigProperties.getRegion()));
    sqsClientConfigProperties.getEndpointOverride().ifPresent(sqsAsyncClientBuilder::endpointOverride);

    return sqsAsyncClientBuilder.build();
  }

  @Bean
  public SqsMessageListenerContainerFactory<Object> sqsMessageListenerContainerFactory(SqsAsyncClient sqsAsyncClient,
      SqsListenerConfigProperties sqsListenerConfigProperties) {
    return SqsMessageListenerContainerFactory.builder()
        .sqsAsyncClient(sqsAsyncClient)
        .configure(sqsContainerOptionsBuilder -> sqsContainerOptionsBuilder.acknowledgementMode(AcknowledgementMode.MANUAL)
            .acknowledgementInterval(sqsListenerConfigProperties.getAcknowledgeInterval())
            .acknowledgementThreshold(sqsListenerConfigProperties.getAcknowledgementThreshold())
            .acknowledgementOrdering(AcknowledgementOrdering.PARALLEL)
            .maxMessagesPerPoll(sqsListenerConfigProperties.getMaxMessagePerPoll())
            .pollTimeout(sqsListenerConfigProperties.getPollTimeout())
            .maxConcurrentMessages(sqsListenerConfigProperties.getMaxConcurrentMessages())
            .messageVisibility(sqsListenerConfigProperties.getMessageVisibility())
            .componentsTaskExecutor(taskExecutor(sqsListenerConfigProperties))
            .backPressureMode(BackPressureMode.AUTO)
        )
        .build();
  }

  private ThreadPoolTaskExecutor taskExecutor(SqsListenerConfigProperties sqsListenerConfigProperties) {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(sqsListenerConfigProperties.getCorePoolSize());
    executor.setMaxPoolSize(sqsListenerConfigProperties.getMaxPoolSize());
    executor.setThreadFactory(new MessageExecutionThreadFactory("mt-status-listener-executor-"));
    executor.initialize();
    return executor;
  }
}
