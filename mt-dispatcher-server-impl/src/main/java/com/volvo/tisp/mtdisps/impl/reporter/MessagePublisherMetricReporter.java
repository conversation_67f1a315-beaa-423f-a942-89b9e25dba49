package com.volvo.tisp.mtdisps.impl.reporter;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class MessagePublisherMetricReporter {
  private static final String FLOW_TYPE = "flow.type";
  private static final String MT_PUBLISH = "mt.publish";
  private static final String MT_PUBLISH_FAILED = "mt.publish.failed";
  private final MeterRegistry meterRegistry;

  public MessagePublisherMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onMessagePublishDuration(Duration duration, FlowType flowType) {
    Validate.notNegative(duration, "duration");
    Validate.notNull(flowType, "flowType");

    meterRegistry.timer(MT_PUBLISH, Tags.of(FLOW_TYPE, flowType.name())).record(duration);
  }

  public void onMessagePublishFailure(FlowType flowType) {
    Validate.notNull(flowType, "flowType");

    meterRegistry.counter(MT_PUBLISH_FAILED, Tags.of(FLOW_TYPE, flowType.name())).increment();
  }
}
