package com.volvo.tisp.mtdisps.impl.converter.mtstatus;

import java.util.Locale;
import java.util.Optional;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.Status;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.api.v2.MtStatusMessage;

@Component
public final class ReceivedTgwMtStatusMessageConverter implements Function<MtStatusMessage, Optional<ReceivedMtStatus>> {
  private static final Logger logger = LoggerFactory.getLogger(ReceivedTgwMtStatusMessageConverter.class);

  private static String createDescription(MtStatusMessage mtStatusMessage) {
    return "TGW MT Status:" + mtStatusMessage.getStatus() + " for CorrelationId:" + mtStatusMessage.getCorrelationId();
  }

  private static Status getStatus(MtStatusMessage mtStatusMessage) {
    try {
      return Status.valueOf(mtStatusMessage.getStatus().toUpperCase(Locale.ROOT));
    } catch (IllegalArgumentException e) {
      logger.warn("unrecognized status: {}. Defaulting to FAILED", mtStatusMessage, e);
      return Status.FAILED;
    }
  }

  @Override
  public Optional<ReceivedMtStatus> apply(MtStatusMessage mtStatusMessage) {
    Validate.notNull(mtStatusMessage, "mtStatusMessage");

    try {
      return Optional.of(new ReceivedMtStatus.Builder()
          .setCorrelationId(CorrelationId.ofString(mtStatusMessage.getCorrelationId()))
          .setDescription(createDescription(mtStatusMessage))
          .setStatus(getStatus(mtStatusMessage))
          .setTrackingIdentifier(TispContext.current().tid())
          .build());
    } catch (RuntimeException e) {
      logger.error("failed to convert received tgw mtStatusMessage: {} ", mtStatusMessage, e);
      return Optional.empty();
    }
  }
}
