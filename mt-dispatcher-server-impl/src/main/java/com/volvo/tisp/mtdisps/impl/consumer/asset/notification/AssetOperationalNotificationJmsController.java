package com.volvo.tisp.mtdisps.impl.consumer.asset.notification;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.JmsException;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.mtdisps.impl.converter.vqv.AssetOperationalStatusNotificationConverter;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.AssetOperationalNotificationMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.asset.notification.AssetOperationalNotificationDomainManager;
import com.wirelesscar.va.VnnMessageTypesV1;
import com.wirelesscar.va.VonMessageTypesV1;
import com.wirelesscar.va.v1.VehicleNonoperationalNotification;
import com.wirelesscar.va.v1.VehicleOperationalNotification;

@JmsController(destination = AssetOperationalNotificationJmsController.VA_NOTIFICATION_DESTINATION)
public class AssetOperationalNotificationJmsController {
  public static final String VA_NOTIFICATION_DESTINATION = "VA.OPERATIONAL-STATUS.IN";
  private static final Logger logger = LoggerFactory.getLogger(AssetOperationalNotificationJmsController.class);

  private final AssetOperationalNotificationDomainManager assetOperationalNotificationDomainManager;
  private final AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter;
  private final AssetOperationalStatusNotificationConverter assetOperationalStatusNotificationConverter;

  public AssetOperationalNotificationJmsController(AssetOperationalStatusNotificationConverter assetOperationalStatusNotificationConverter,
      AssetOperationalNotificationDomainManager assetOperationalNotificationDomainManager,
      AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter) {
    this.assetOperationalStatusNotificationConverter = assetOperationalStatusNotificationConverter;
    this.assetOperationalNotificationDomainManager = assetOperationalNotificationDomainManager;
    this.assetOperationalNotificationMetricReporter = assetOperationalNotificationMetricReporter;
  }

  @JmsMessageMapping(
      consumesType = VnnMessageTypesV1.VEHICLE_NON_OPERATIONAL_NOTIFICATION_JMS_TYPE,
      consumesVersion = VnnMessageTypesV1.MAJOR_VERSION)
  public void handleNonOperationalNotification(final JmsMessage<VehicleNonoperationalNotification> jmsMessage) throws JmsException {
    logger.debug("received VA Non-Operational vehicle Notification jmsMessage: {}", jmsMessage);

    assetOperationalStatusNotificationConverter.convert(jmsMessage.payload())
        .ifPresentOrElse(assetOperationalNotificationDomainManager::process, this::onEmpty);
  }

  @JmsMessageMapping(
      consumesType = VonMessageTypesV1.VEHICLE_OPERATIONAL_NOTIFICATION_JMS_TYPE,
      consumesVersion = VonMessageTypesV1.MAJOR_VERSION)
  public void handleOperationalNotification(final JmsMessage<VehicleOperationalNotification> jmsMessage) throws JmsException {
    logger.debug("received VA Operational vehicle Notification jmsMessage: {}", jmsMessage);

    assetOperationalStatusNotificationConverter.convert(jmsMessage.payload())
        .ifPresentOrElse(assetOperationalNotificationDomainManager::process, this::onEmpty);
  }

  private void onEmpty() {
    assetOperationalNotificationMetricReporter.onFailure(ValidationFailureReason.CONVERSION_FAILURE);
  }
}
