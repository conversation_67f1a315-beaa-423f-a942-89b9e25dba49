package com.volvo.tisp.mtdisps.impl.model;

public enum Ttl {
  ONE_MINUTE(60),
  FIVE_MINUTES(300),
  TEN_MINUTES(600),
  ONE_HOUR(3600),
  TWELVE_HOURS(43_200),
  ONE_DAY(86_400),
  TWO_DAYS(172_800),
  THREE_DAYS(259_200),
  FOUR_DAYS(345_600),
  FIVE_DAYS(432_000),
  SIX_DAYS(518_400),
  ONE_WEEK(604_800),
  TWO_WEEKS(1_209_600);

  private final int seconds;

  Ttl(int seconds) {
    this.seconds = seconds;
  }

  public int toSeconds() {
    return seconds;
  }
}