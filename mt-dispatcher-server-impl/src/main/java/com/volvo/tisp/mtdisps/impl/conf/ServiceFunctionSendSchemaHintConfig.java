package com.volvo.tisp.mtdisps.impl.conf;

import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;

@ConfigurationProperties(prefix = "service-function-send-schema-mapping")
@ConfigurationPropertiesScan
public class ServiceFunctionSendSchemaHintConfig {
  private static final Logger logger = LoggerFactory.getLogger(ServiceFunctionSendSchemaHintConfig.class);

  private final Map<String, SendSchemaHint> map;

  public ServiceFunctionSendSchemaHintConfig(Map<String, SendSchemaHint> map) {
    logger.info("loaded service function to send schema hint map: {}", map);

    this.map = map;
  }

  public Optional<String> getSendSchemaHint(ServiceFunction serviceFunction) {
    return Optional.ofNullable(map.get(serviceFunction.value())).map(SendSchemaHint::sendSchemaHint);
  }

  record SendSchemaHint(String sendSchemaHint) {
  }
}
