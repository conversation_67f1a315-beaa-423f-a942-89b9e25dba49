package com.volvo.tisp.mtdisps.impl.service.processor;

import java.util.concurrent.CompletableFuture;

import com.volvo.tisp.mtdisps.impl.converter.mtmessage.VcmMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.publisher.VcmMtMessagePublisher;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class VcmMtMessageProcessor implements MtMessageProcessor {
  private final VcmMtMessageConverter vcmMtMessageConverter;
  private final VcmMtMessagePublisher vcmMtMessagePublisher;

  public VcmMtMessageProcessor(VcmMtMessageConverter vcmMtMessageConverter, VcmMtMessagePublisher vcmMtMessagePublisher) {
    this.vcmMtMessageConverter = vcmMtMessageConverter;
    this.vcmMtMessagePublisher = vcmMtMessagePublisher;
  }

  public CompletableFuture<Void> process(ProcessableMtMessage processableMtMessage) {
    Validate.notNull(processableMtMessage, "processableMtMessage");

    return vcmMtMessagePublisher.publish(vcmMtMessageConverter.apply(processableMtMessage));
  }
}
