package com.volvo.tisp.mtdisps.impl.conf;

import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class AppProperties {
  private final boolean integrationLoggingEnabled;
  private final Duration ttlMargin;

  public AppProperties(@Value("${ttl.margin:PT1H}") Duration ttlMargin, @Value("${integration.logging.enabled:true}") boolean integrationLoggingEnabled) {
    Validate.isPositive(ttlMargin, "ttlMargin");

    this.ttlMargin = ttlMargin;
    this.integrationLoggingEnabled = integrationLoggingEnabled;
  }

  public Duration getTtlMargin() {
    return ttlMargin;
  }

  public boolean isIntegrationLoggingEnabled() {
    return integrationLoggingEnabled;
  }
}
