package com.volvo.tisp.mtdisps.impl.service.processor;

import java.util.concurrent.CompletableFuture;

import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;

public class NoOpMtMessageProcessor implements MtMessageProcessor {
  @Override
  public CompletableFuture<Void> process(ProcessableMtMessage processableMtMessage) {
    return CompletableFuture.failedFuture(new IllegalAccessException("Unable to process the message as this flow is not supported"));
  }
}
