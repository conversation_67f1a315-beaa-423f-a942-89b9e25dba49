package com.volvo.tisp.mtdisps.impl.reporter;

import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.jwt.IdpVersion;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class JwtProcessorResolverMetricReporter {
  private final MeterRegistry meterRegistry;

  public JwtProcessorResolverMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onIdpVersion(IdpVersion idpVersion) {
    meterRegistry.counter("idp.version", Tags.of("TYPE", idpVersion.name()));
  }
}
