package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Base64;
import java.util.Optional;

import org.springframework.stereotype.Component;

import com.volvo.tisp.external.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.dto.PayloadSignatureDetails;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtMessage;
import com.volvo.tisp.mtdisps.impl.model.AcceptedCost;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.Priority;
import com.volvo.tisp.mtdisps.impl.model.ServiceAccessToken;
import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ServiceVersion;
import com.volvo.tisp.mtdisps.impl.model.Ttl;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;

@Component
public class ExternalReceivedMtMessageConverter implements ReceivedMtMessageConverter {

  /* Since serviceFunction is not part of external mt api, we provide a hard coded default   */
  private static ServiceFunction createServiceFunction() {
    return new ServiceFunction("EXTERNAL");
  }

  private static Optional<PayloadSignatureDetails> getPayloadSignatureDetails(MtMessage externalMtMessage) {
    return Optional.ofNullable(externalMtMessage.getPayloadSignature())
        .flatMap(payloadSignature -> Optional.ofNullable(externalMtMessage.getKeyId())
            .map(keyId -> new PayloadSignatureDetails(keyId, payloadSignature)));
  }

  @Override
  public Optional<ReceivedMtMessage> convert(Object mtMessage) {
    try {
      if (mtMessage instanceof MtMessage externalMtMessage) {
        return Optional.of(
            new ReceivedMtMessage(
                AcceptedCost.valueOf(externalMtMessage.getAcceptedCost().name()),
                externalMtMessage.getAssetIdentifier(),
                AssetIdentifierType.valueOf(externalMtMessage.getAssetIdentifierType().value()),
                Optional.ofNullable(externalMtMessage.getCorrelationId()).map(CorrelationId::ofString),
                createServiceFunction(),
                false,
                ImmutableByteArray.of(Base64.getDecoder().decode(externalMtMessage.getPayload())),
                getPayloadSignatureDetails(externalMtMessage),
                Optional.ofNullable(externalMtMessage.getPriority()).map(priority -> Priority.valueOf(priority.name())),
                new ServiceAccessToken(externalMtMessage.getServiceAccessToken()),
                new ServiceId(externalMtMessage.getServiceId()),
                new ServiceVersion(externalMtMessage.getServiceVersion()),
                TrackingIdentifier.fromString(externalMtMessage.getTrackingId()),
                Ttl.valueOf(externalMtMessage.getTtl().name()),
                MtMessageType.EXTERNAL_MT_MESSAGE));
      } else {
        return Optional.empty();
      }
    } catch (IllegalArgumentException e) {
      return Optional.empty();
    }
  }
}
