package com.volvo.tisp.mtdisps.impl.dto;

import java.util.Optional;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.model.AcceptedCost;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.Priority;
import com.volvo.tisp.mtdisps.impl.model.ServiceAccessToken;
import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ServiceVersion;
import com.volvo.tisp.mtdisps.impl.model.Ttl;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;

public record ProcessableMtMessage(
    VehicleInformation vehicleInformation,
    AcceptedCost acceptedCost,
    Optional<CorrelationId> correlationId,
    ServiceFunction serviceFunction,
    boolean overridePreviousMessageType,
    ImmutableByteArray payload,
    Optional<PayloadSignatureDetails> payloadSignatureDetails,
    Optional<Priority> priority,
    ServiceAccessToken serviceAccessToken,
    ServiceId serviceId,
    ServiceVersion serviceVersion,
    TrackingIdentifier trackingIdentifier,
    Ttl ttl,
    MtMessageType mtMessageType) {
}
