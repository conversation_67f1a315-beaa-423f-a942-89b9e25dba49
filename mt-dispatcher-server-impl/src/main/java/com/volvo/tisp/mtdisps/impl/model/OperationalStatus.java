package com.volvo.tisp.mtdisps.impl.model;

public enum OperationalStatus {
  OPERATIONAL(true),
  NON_OPERATIONAL(false);

  private final boolean operationalStatus;

  OperationalStatus(boolean operationalStatus) {
    this.operationalStatus = operationalStatus;
  }

  public boolean getOperationalStatus() {
    return operationalStatus;
  }

  public static OperationalStatus fromValue(boolean isOperational) {
    if(isOperational) return OPERATIONAL;
    else return NON_OPERATIONAL;
  }
}
