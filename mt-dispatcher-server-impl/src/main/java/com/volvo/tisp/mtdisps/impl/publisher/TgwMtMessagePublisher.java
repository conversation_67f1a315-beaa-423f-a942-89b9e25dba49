package com.volvo.tisp.mtdisps.impl.publisher;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.mtdisps.impl.conf.TgwFlowEnabledCondition;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.reporter.MessagePublisherMetricReporter;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

@Component
@Conditional({TgwFlowEnabledCondition.class})
public class TgwMtMessagePublisher extends SyncMessagePublisher<MtMessage> {
  private final JmsTemplate jmsTemplate;
  private final String queueName;

  public TgwMtMessagePublisher(JmsTemplate jmsTemplate, MessagePublisherMetricReporter messagePublisherMetricReporter,
      @Value("${jms.tgwmts.mt.message.queue.name}") String queueName) {
    super(messagePublisherMetricReporter, FlowType.TGW);

    this.jmsTemplate = jmsTemplate;
    this.queueName = queueName;
  }

  @Override
  protected void doPublishSync(MtMessage mtMessage) {
    jmsTemplate.convertAndSend(queueName, mtMessage, message -> {
      message.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), MessageTypesJms.TCE_MT_MESSAGE_TYPE);
      message.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), MessageTypesJms.VERSION_2_0);
      return message;
    });
  }
}
