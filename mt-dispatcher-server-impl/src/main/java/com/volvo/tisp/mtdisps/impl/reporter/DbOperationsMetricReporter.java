package com.volvo.tisp.mtdisps.impl.reporter;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class DbOperationsMetricReporter {
  private static final String CRUD = "CRUD";
  private static final String DB_MESSAGE_TRANSACTION_EXPIRED_SIZE = "db.message-transaction-parameters-expired-size";
  private static final String DB_MESSAGE_TRANSACTION_PARAMETERS = "db.message-transaction-parameters";
  private static final String DB_MESSAGE_TRANSACTION_PARAMETERS_FAILURE = "db.message-transaction-parameters.failure";
  private static final String DB_VEHICLE_INFORMATION = "db.vehicle-information";
  private static final String DB_VEHICLE_INFORMATION_FAILURE = "db.vehicle-information.failure";

  private final AtomicLong dbMessageTransactionParameterExpiredSize;
  private final MeterRegistry meterRegistry;

  public DbOperationsMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;

    this.dbMessageTransactionParameterExpiredSize = meterRegistry.gauge(DB_MESSAGE_TRANSACTION_EXPIRED_SIZE, new AtomicLong(0));
  }

  public void onExpiredMessageTransactionCount(long count) {
    Validate.notNegative(count, "count");

    dbMessageTransactionParameterExpiredSize.set(count);
  }

  public void onMessageTransactionCrudDuration(Duration duration, Crud crud) {
    Validate.notNegative(duration, "duration");
    Validate.notNull(crud, "crud");

    meterRegistry.timer(DB_MESSAGE_TRANSACTION_PARAMETERS, Tags.of(CRUD, crud.name())).record(duration);
  }

  public void onMessageTransactionCrudFailure(Crud crud) {
    Validate.notNull(crud, "crud");

    meterRegistry.counter(DB_MESSAGE_TRANSACTION_PARAMETERS_FAILURE, Tags.of(CRUD, crud.name())).increment();
  }

  public void onVehicleInfoCrudDuration(Duration duration, Crud crud) {
    Validate.notNegative(duration, "duration");
    Validate.notNull(crud, "crud");

    meterRegistry.timer(DB_VEHICLE_INFORMATION, Tags.of(CRUD, crud.name())).record(duration);
  }

  public void onVehicleInfoCrudFailure(Crud crud) {
    Validate.notNull(crud, "crud");

    meterRegistry.counter(DB_VEHICLE_INFORMATION_FAILURE, Tags.of(CRUD, crud.name())).increment();
  }
}
