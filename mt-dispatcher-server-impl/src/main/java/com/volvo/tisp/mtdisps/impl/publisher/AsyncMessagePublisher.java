package com.volvo.tisp.mtdisps.impl.publisher;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.mtdisps.impl.dto.LogType;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.reporter.MessagePublisherMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public abstract class AsyncMessagePublisher<T> {
  private static final Logger logger = LoggerFactory.getLogger(AsyncMessagePublisher.class);
  private final FlowType flowType;
  private final MessagePublisherMetricReporter messagePublisherMetricReporter;

  protected AsyncMessagePublisher(MessagePublisherMetricReporter messagePublisherMetricReporter, FlowType flowType) {
    Validate.notNull(messagePublisherMetricReporter, "messagePublisherMetricReporter");
    Validate.notNull(flowType, "flowType");

    this.messagePublisherMetricReporter = messagePublisherMetricReporter;
    this.flowType = flowType;
  }

  public CompletableFuture<Void> publish(T message) {
    Validate.notNull(message, "message");

    Instant start = Instant.now();
    return doPublishAsync(message).whenComplete((unused, throwable) -> {
      if (throwable != null) {
        logger.error("error while publishing using {} publisher", flowType, throwable);
        integrationLog(message, LogType.FAILURE);
        messagePublisherMetricReporter.onMessagePublishFailure(flowType);
      } else {
        integrationLog(message, LogType.SUCCESS);
        messagePublisherMetricReporter.onMessagePublishDuration(Duration.between(start, Instant.now()), flowType);
      }
    });
  }

  protected abstract CompletableFuture<Void> doPublishAsync(T message);

  protected abstract void integrationLog(T message, LogType logType);
}
