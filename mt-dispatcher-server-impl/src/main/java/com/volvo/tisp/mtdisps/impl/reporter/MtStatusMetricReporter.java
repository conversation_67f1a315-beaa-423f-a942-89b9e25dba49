package com.volvo.tisp.mtdisps.impl.reporter;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;

@Component
public class MtStatusMetricReporter {
  static final String LATENCY = "latency";
  static final String MT_STATUS_RECEIVE_LATENCY = "mt.status.receive_latency";
  static final String TAG_EVENT = "event";
  private static final String FLOW_TYPE = "flow.type";
  private static final String MT_STATUS_FAILED = "mt.status.failed";
  private static final String MT_STATUS_RECEIVED = "mt.status.received";
  private static final String REASON = "reason";
  private static final String TYPE = "type";
  private final MeterRegistry meterRegistry;
  private final Timer mtStatusReceiveLatency;

  public MtStatusMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
    this.mtStatusReceiveLatency = meterRegistry.timer(MT_STATUS_RECEIVE_LATENCY, TAG_EVENT, LATENCY);
  }

  public void onMtStatusFailure(ValidationFailureReason validationFailureReason, FlowType flowType) {
    Validate.notNull(validationFailureReason, REASON);
    Validate.notNull(flowType, "flowType");

    meterRegistry.counter(MT_STATUS_FAILED, Tags.of(TYPE, flowType.name(), REASON, validationFailureReason.name())).increment();
  }

  public void onMtStatusReceiveLatency(final Duration duration) {
    Validate.notNull(duration, "duration");

    mtStatusReceiveLatency.record(duration.isNegative() ? Duration.ZERO : duration);
  }

  public void onMtStatusReceived(FlowType flowType) {
    Validate.notNull(flowType, "flowType");

    meterRegistry.counter(MT_STATUS_RECEIVED, Tags.of(FLOW_TYPE, flowType.name())).increment();
  }
}
