package com.volvo.tisp.mtdisps.impl.converter.vqv;

import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.model.Version;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record AssetOperationalStatusRecord(Vpi vpi, Version vehicleVersion, OperationalStatus operationalStatus) {
  public AssetOperationalStatusRecord {
    Validate.notNull(vpi, "vpi");
    Validate.notNull(vehicleVersion, "vehicleVersion");
    Validate.notNull(operationalStatus, "operationalStatus");
  }
}
