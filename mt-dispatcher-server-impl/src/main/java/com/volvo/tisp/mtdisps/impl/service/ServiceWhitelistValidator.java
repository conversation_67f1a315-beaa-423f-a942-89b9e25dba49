package com.volvo.tisp.mtdisps.impl.service;

import java.util.Collection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public final class ServiceWhitelistValidator {
  private static final Logger logger = LoggerFactory.getLogger(ServiceWhitelistValidator.class);

  private final Collection<Integer> whitelist;

  public ServiceWhitelistValidator(@Value("${whitelisted.service-ids:0}") Collection<Integer> whitelist) {
    this.whitelist = whitelist;
  }

  public void validateIsWhitelisted(ProcessableMtMessage processableMtMessage) throws ValidationException {
    Validate.notNull(processableMtMessage, "processableMtMessage");
    ServiceId serviceId = processableMtMessage.serviceId();

    if (!(whitelist.contains(serviceId.serviceId()) || (whitelist.size() == 1 && whitelist.contains(0)))) {
      logger.warn("Service id {} not allowed because it is not in whitelist", serviceId);
      throw new ValidationException(ValidationFailureReason.SERVICE_NOT_WHITELISTED);
    }
  }
}
