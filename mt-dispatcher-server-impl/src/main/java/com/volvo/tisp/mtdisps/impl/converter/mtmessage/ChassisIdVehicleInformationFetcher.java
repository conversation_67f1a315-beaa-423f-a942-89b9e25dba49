package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.model.MultipleAssetsFoundException;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;

@Component
class ChassisIdVehicleInformationFetcher implements VehicleInformationFetcher {
  private final VehicleInformationRepository vehicleInformationRepository;

  public ChassisIdVehicleInformationFetcher(VehicleInformationRepository vehicleInformationRepository) {
    this.vehicleInformationRepository = vehicleInformationRepository;
  }

  @Override
  public Optional<VehicleInformation> fetch(String assetIdentifier, boolean isActivation) throws MultipleAssetsFoundException {
    if (StringUtils.isAllBlank(assetIdentifier)) {
      return Optional.empty();
    }

    try {
      if (isActivation) {
        return vehicleInformationRepository.findByChassisId(assetIdentifier).map(VehicleInformation::fromEntity);
      }

      return vehicleInformationRepository.findByChassisIdAndOperationalIsTrue(assetIdentifier).map(VehicleInformation::fromEntity);
    } catch (IncorrectResultSizeDataAccessException e) {
      throw new MultipleAssetsFoundException(assetIdentifier);
    }
  }

  @Override
  public AssetIdentifierType getSupportedAssetIdentifierType() {
    return AssetIdentifierType.CHASSIS_ID;
  }
}
