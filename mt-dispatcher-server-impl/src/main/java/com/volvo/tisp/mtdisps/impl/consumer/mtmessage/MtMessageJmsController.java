package com.volvo.tisp.mtdisps.impl.consumer.mtmessage;

import java.util.concurrent.CompletableFuture;

import jakarta.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.InternalReceivedMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.ProcessableMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.model.InterfaceType;
import com.volvo.tisp.mtdisps.impl.reporter.MtMessageMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.IntegrationLogger;
import com.volvo.tisp.mtdisps.impl.service.MtMessageDomainManager;
import com.volvo.tisp.mtdisps.impl.service.ServiceAccessTokenValidator;
import com.volvo.tisp.mtdisps.impl.service.ServiceWhitelistValidator;
import com.volvo.tisp.mtdisps.impl.service.processor.MtStatusProcessor;
import com.volvo.tisp.vc.mt.message.client.json.v1.InternalMtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessageWithAssetHardwareId;

@JmsController(destination = MtMessageJmsController.MT_MESSAGE)
public class MtMessageJmsController extends MtMessageConsumer<InternalMtMessage> {
  public static final String MT_MESSAGE = "MT-MESSAGES";
  private static final Logger logger = LoggerFactory.getLogger(MtMessageJmsController.class);

  public MtMessageJmsController(
      MtMessageDomainManager mtMessageDomainManager,
      MtMessageMetricReporter mtMessageMetricReporter,
      ServiceAccessTokenValidator serviceAccessTokenValidator,
      InternalReceivedMtMessageConverter receivedMtMessageConverter,
      ProcessableMtMessageConverter processableMtMessageConverter,
      IntegrationLogger integrationLogger,
      MtStatusProcessor mtStatusProcessor,
      ServiceWhitelistValidator serviceWhitelistValidator) {
    super(mtMessageDomainManager,
        mtMessageMetricReporter,
        serviceAccessTokenValidator,
        processableMtMessageConverter,
        mtStatusProcessor,
        integrationLogger,
        receivedMtMessageConverter,
        serviceWhitelistValidator);
  }

  @JmsMessageMapping(consumesType = MessageTypes.MT_MESSAGE, consumesVersion = MessageTypes.VERSION_1_0)
  public CompletableFuture<Void> receiveMtMessage(@Valid JmsMessage<MtMessage> jmsMessage) {
    logger.trace("received jmsMessage: {}", jmsMessage);
    return consumeMessage(jmsMessage.payload());
  }

  @JmsMessageMapping(consumesType = MessageTypes.MT_MESSAGE_WITH_ASSET_HARDWARE_ID, consumesVersion = MessageTypes.VERSION_1_0)
  public CompletableFuture<Void> receiveMtMessageWithAssetHardwareId(@Valid JmsMessage<MtMessageWithAssetHardwareId> jmsMessage) {
    logger.trace("received MtMessageWithAssetHardwareId: {}", jmsMessage);
    return consumeMessage(jmsMessage.payload());
  }

  @Override
  protected InterfaceType getInterfaceType() {
    return InterfaceType.INTERNAL_JMS;
  }
}
