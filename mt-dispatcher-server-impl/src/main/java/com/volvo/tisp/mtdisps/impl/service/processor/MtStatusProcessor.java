package com.volvo.tisp.mtdisps.impl.service.processor;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.service.IntegrationLogger;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class MtStatusProcessor {
  private static final String SERVICE_FUNCTION = "SERVICE_FUNCTION";
  private static final String SRP_DST_SERVICE = "SRP_DST_SERVICE";
  private static final String SRP_DST_VERSION = "SRP_DST_VERSION";
  private static final Logger logger = LoggerFactory.getLogger(MtStatusProcessor.class);
  private final IntegrationLogger integrationLogger;
  private final Map<MtMessageType, MessagePublisher<ProcessableMtStatus>> mtStatusPublishers;

  public MtStatusProcessor(Map<MtMessageType, MessagePublisher<ProcessableMtStatus>> mtStatusPublishers, IntegrationLogger integrationLogger) {
    this.mtStatusPublishers = mtStatusPublishers;
    this.integrationLogger = integrationLogger;
  }

  public CompletableFuture<Void> process(ProcessableMtStatus processableMtStatus) {
    Validate.notNull(processableMtStatus, "processableMtStatus");

    return publishMessage(processableMtStatus, mtStatusPublishers.get(processableMtStatus.mtMessageType())).handle((subscriberCount, throwable) -> {
      if (throwable != null) {
        integrationLogger.log(processableMtStatus, "MtStatusPublishFailed");
      } else {
        if (subscriberCount == 0) {
          logger.warn("No subscribers found for the status {}", processableMtStatus);
          integrationLogger.log(processableMtStatus, "NoMtStatusSubscribersFound");
        } else {
          integrationLogger.log(processableMtStatus, "MtStatusPublishSucceeded");
        }
      }
      return null;
    });
  }

  private CompletableFuture<Integer> publishMessage(ProcessableMtStatus processableMtStatus, MessagePublisher<ProcessableMtStatus> mtStatusPublisher) {
    return mtStatusPublisher.newMessage()
        .option(SRP_DST_SERVICE, processableMtStatus.serviceId().serviceId())
        .option(SRP_DST_VERSION, processableMtStatus.serviceVersion().serviceVersion())
        .option(SERVICE_FUNCTION, processableMtStatus.serviceFunction().value())
        .publish(processableMtStatus);
  }
}
