package com.volvo.tisp.mtdisps.impl.service.asset.notification;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.impl.dto.vqv.VqvNotification;

@Component
public class VqvNotificationDomainManager {
  private final VqvNotificationProcessor tgwVqvNotificationProcessor;
  private final VqvNotificationProcessor vcmVqvNotificationProcessor;

  public VqvNotificationDomainManager(@Qualifier("tgwVqvNotificationProcessor") VqvNotificationProcessor tgwVqvNotificationProcessor,
      @Qualifier("vcmVqvNotificationProcessor") VqvNotificationProcessor vcmVqvNotificationProcessor) {
    this.tgwVqvNotificationProcessor = tgwVqvNotificationProcessor;
    this.vcmVqvNotificationProcessor = vcmVqvNotificationProcessor;
  }

  public void process(VqvNotification vqvNotification) {
    switch (vqvNotification.telematicUnitType()) {
      case TGW -> tgwVqvNotificationProcessor.process(vqvNotification);
      case VCM -> vcmVqvNotificationProcessor.process(vqvNotification);
    }
  }
}
