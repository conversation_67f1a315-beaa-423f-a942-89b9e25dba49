package com.volvo.tisp.mtdisps.impl.conf;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.integration.log.IntegrationLogEventPublisher;
import com.volvo.tisp.integration.log.IntegrationLogEventRepositoryClientConfiguration;

import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kinesis.KinesisAsyncClient;

@Configuration
public class IntegrationLoggingConfig {
  @Bean
  public IntegrationLogEventPublisher createIntegrationLogEventPublisher(ObjectMapper objectMapper, @Value("${kinesis.data.stream.arn}") String streamArn,
      KinesisAsyncClient kinesisAsyncClient) {

    return new IntegrationLogEventPublisher(objectMapper, kinesisAsyncClient, new IntegrationLogEventRepositoryClientConfiguration(streamArn));
  }

  @Bean
  public KinesisAsyncClient createKinesisAsyncClient(@Value("${spring.cloud.aws.region.static:${site}}") String regionString) {
    return KinesisAsyncClient.builder()
        .credentialsProvider(DefaultCredentialsProvider.create())
        .region(Region.of(regionString))
        .build();
  }
}
