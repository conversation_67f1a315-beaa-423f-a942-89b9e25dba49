package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;

import org.springframework.stereotype.Component;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.model.MultipleAssetsFoundException;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;

@Component
public class VpiVehicleInformationFetcher implements VehicleInformationFetcher {
  private final VehicleInformationRepository vehicleInformationRepository;

  public VpiVehicleInformationFetcher(VehicleInformationRepository vehicleInformationRepository) {
    this.vehicleInformationRepository = vehicleInformationRepository;
  }

  @Override
  public Optional<VehicleInformation> fetch(String assetIdentifier, boolean isActivation) throws MultipleAssetsFoundException {
    if (isActivation) {
      return vehicleInformationRepository.findByVpi(assetIdentifier).map(VehicleInformation::fromEntity);
    }

    return vehicleInformationRepository.findByVpiAndOperationalIsTrue(assetIdentifier).map(VehicleInformation::fromEntity);
  }

  @Override
  public AssetIdentifierType getSupportedAssetIdentifierType() {
    return AssetIdentifierType.VPI;
  }
}
