package com.volvo.tisp.mtdisps.impl.conf;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
@Profile(AppConfig.SQS_PROFILE)
public class SqsListenerConfigProperties {
  private static final Logger logger = LoggerFactory.getLogger(SqsListenerConfigProperties.class);

  private final Duration acknowledgeInterval;
  private final int acknowledgementThreshold;
  private final int corePoolSize;
  private final int maxConcurrentMessages;
  private final int maxMessagePerPoll;
  private final int maxPoolSize;
  private final Duration messageVisibility;
  private final Duration pollTimeout;

  public SqsListenerConfigProperties(
      @Value("${sqs.listener.acknowledgement.interval:5S}") Duration acknowledgementInterval,
      @Value("${sqs.listener.acknowledgement.threshold:50}") int acknowledgementThreshold,
      @Value("${sqs.listener.core.pool.size:10}") int corePoolSize,
      @Value("${sqs.listener.max.concurrent.message:100}") int maxConcurrentMessages,
      @Value("${sqs.listener.max.message.per.poll:10}") int maxMessagePerPoll,
      @Value("${sqs.listener.max.pool.size:100}") int maxPoolSize,
      @Value("${sqs.listener.message.visibility:30S}") Duration messageVisibility,
      @Value("${sqs.listener.poll.timeout:20S}") Duration pollTimeout) {
    Validate.notNull(acknowledgementInterval, "acknowledgementInterval");
    Validate.isPositive(acknowledgementThreshold, "acknowledgementThreshold");
    Validate.isPositive(corePoolSize, "corePoolSize");
    Validate.isPositive(maxConcurrentMessages, "maxConcurrentMessages");
    Validate.isPositiveAndNotGreaterThan(maxMessagePerPoll, 10, "maxMessagePerPoll");
    Validate.isPositive(maxPoolSize, "maxPoolSize");
    Validate.notNull(messageVisibility, "messageVisibility");
    Validate.notNull(pollTimeout, "pollTimeout");
    Validate.isPositiveAndNotGreaterThan(pollTimeout.getSeconds(), 20, "pollTimeout");

    logger.info("SqsListenerConfiguration created with sqs.listener.acknowledgement.interval: {}, sqs.listener.acknowledgement.threshold:{}, "
            + "sqs.listener.core.pool.size:{}, sqs.listener.max.concurrent.message:{}, sqs.listener.max.message.per.poll:{},  sqs.listener.max.poll.size:{},"
            + " sqs.listener.message.visibility:{}, sqs.listener.poll.timeout:{}", acknowledgementInterval, acknowledgementThreshold, corePoolSize,
        maxConcurrentMessages, maxMessagePerPoll, maxPoolSize, messageVisibility, pollTimeout);

    this.acknowledgeInterval = acknowledgementInterval;
    this.acknowledgementThreshold = acknowledgementThreshold;
    this.corePoolSize = corePoolSize;
    this.maxConcurrentMessages = maxConcurrentMessages;
    this.maxMessagePerPoll = maxMessagePerPoll;
    this.maxPoolSize = maxPoolSize;
    this.messageVisibility = messageVisibility;
    this.pollTimeout = pollTimeout;
  }

  public Duration getAcknowledgeInterval() {
    return acknowledgeInterval;
  }

  public int getAcknowledgementThreshold() {
    return acknowledgementThreshold;
  }

  public int getCorePoolSize() {
    return corePoolSize;
  }

  public int getMaxConcurrentMessages() {
    return maxConcurrentMessages;
  }

  public int getMaxMessagePerPoll() {
    return maxMessagePerPoll;
  }

  public int getMaxPoolSize() {
    return maxPoolSize;
  }

  public Duration getMessageVisibility() {
    return messageVisibility;
  }

  public Duration getPollTimeout() {
    return pollTimeout;
  }
}
