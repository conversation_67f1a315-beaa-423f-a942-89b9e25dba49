package com.volvo.tisp.mtdisps.impl.consumer.mtmessage;

import java.util.concurrent.CompletableFuture;

import jakarta.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.external.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.external.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.ExternalReceivedMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.ProcessableMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.model.InterfaceType;
import com.volvo.tisp.mtdisps.impl.reporter.MtMessageMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.IntegrationLogger;
import com.volvo.tisp.mtdisps.impl.service.MtMessageDomainManager;
import com.volvo.tisp.mtdisps.impl.service.ServiceAccessTokenValidator;
import com.volvo.tisp.mtdisps.impl.service.ServiceWhitelistValidator;
import com.volvo.tisp.mtdisps.impl.service.processor.MtStatusProcessor;

@JmsController(destination = ExternalMtMessageJmsController.EXTERNAL_MT_MESSAGES)
public class ExternalMtMessageJmsController extends MtMessageConsumer<MtMessage> {
  public static final String EXTERNAL_MT_MESSAGES = "EXTERNAL.MT.MESSAGES.IN";
  private static final Logger logger = LoggerFactory.getLogger(ExternalMtMessageJmsController.class);

  public ExternalMtMessageJmsController(
      MtMessageDomainManager mtMessageDomainManager,
      MtMessageMetricReporter mtMessageMetricReporter,
      ServiceAccessTokenValidator serviceAccessTokenValidator,
      ExternalReceivedMtMessageConverter receivedMtMessageConverter,
      ProcessableMtMessageConverter processableMtMessageConverter,
      IntegrationLogger integrationLogger,
      MtStatusProcessor mtStatusProcessor,
      ServiceWhitelistValidator serviceWhitelistValidator) {
    super(mtMessageDomainManager,
        mtMessageMetricReporter,
        serviceAccessTokenValidator,
        processableMtMessageConverter,
        mtStatusProcessor,
        integrationLogger,
        receivedMtMessageConverter,
        serviceWhitelistValidator);
  }

  @JmsMessageMapping(consumesType = MessageTypes.EXTERNAL_MT_MESSAGE, consumesVersion = MessageTypes.VERSION_1_0)
  public CompletableFuture<Void> receiveExternalMtMessage(@Valid JmsMessage<MtMessage> jmsMessage) {
    logger.trace("received jmsMessage: {}", jmsMessage);

    return consumeMessage(jmsMessage.payload());
  }

  @Override
  protected InterfaceType getInterfaceType() {
    return InterfaceType.EXTERNAL_JMS;
  }
}
