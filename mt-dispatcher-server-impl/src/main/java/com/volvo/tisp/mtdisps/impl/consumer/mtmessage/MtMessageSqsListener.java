package com.volvo.tisp.mtdisps.impl.consumer.mtmessage;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.InternalReceivedMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.ProcessableMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.model.InterfaceType;
import com.volvo.tisp.mtdisps.impl.reporter.MtMessageMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.IntegrationLogger;
import com.volvo.tisp.mtdisps.impl.service.MtMessageDomainManager;
import com.volvo.tisp.mtdisps.impl.service.ServiceAccessTokenValidator;
import com.volvo.tisp.mtdisps.impl.service.ServiceWhitelistValidator;
import com.volvo.tisp.mtdisps.impl.service.processor.MtStatusProcessor;
import com.volvo.tisp.vc.mt.message.client.json.v1.InternalMtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessageWithAssetHardwareId;

import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;

@Component
@Profile(AppConfig.SQS_PROFILE)
public class MtMessageSqsListener extends MtMessageConsumer<InternalMtMessage> {
  private static final Logger logger = LoggerFactory.getLogger(MtMessageSqsListener.class);

  public MtMessageSqsListener(
      MtMessageDomainManager mtMessageDomainManager,
      MtMessageMetricReporter mtMessageMetricReporter,
      ServiceAccessTokenValidator serviceAccessTokenValidator,
      InternalReceivedMtMessageConverter receivedMtMessageConverter,
      ProcessableMtMessageConverter processableMtMessageConverter,
      IntegrationLogger integrationLogger,
      MtStatusProcessor mtStatusProcessor,
      ServiceWhitelistValidator serviceWhitelistValidator) {
    super(mtMessageDomainManager,
        mtMessageMetricReporter,
        serviceAccessTokenValidator,
        processableMtMessageConverter,
        mtStatusProcessor,
        integrationLogger,
        receivedMtMessageConverter,
        serviceWhitelistValidator);
  }

  @SqsListener(value = "${sqs.incoming-mt-message.queue-url}", factory = "sqsMessageListenerContainerFactory")
  public CompletableFuture<Void> onMtMessage(MtMessage mtMessage, Acknowledgement acknowledgement) {
    logger.trace("received sqs mt message: {}", mtMessage);

    return TispContext.supplyInContext(() -> this.consumeMessage(mtMessage).thenCompose(unused -> acknowledgement.acknowledgeAsync()),
        context -> context.tid(TrackingIdentifier.fromString(mtMessage.getTrackingId())));
  }

  @SqsListener(value = "${sqs.incoming-mt-message-with-asset-hardware-id.queue-url}", factory = "sqsMessageListenerContainerFactory")
  public CompletableFuture<Void> onMtMessageWithAssetHardwareId(MtMessageWithAssetHardwareId mtMessageWithAssetHardwareId, Acknowledgement acknowledgement) {
    logger.trace("received sqs mt message with asset hardware id: {}", mtMessageWithAssetHardwareId);

    return TispContext.supplyInContext(() -> this.consumeMessage(mtMessageWithAssetHardwareId).thenCompose(unused -> acknowledgement.acknowledgeAsync()),
        context -> context.tid(TrackingIdentifier.fromString(mtMessageWithAssetHardwareId.getTrackingId())));
  }

  @Override
  protected InterfaceType getInterfaceType() {
    return InterfaceType.INTERNAL_SQS;
  }
}
