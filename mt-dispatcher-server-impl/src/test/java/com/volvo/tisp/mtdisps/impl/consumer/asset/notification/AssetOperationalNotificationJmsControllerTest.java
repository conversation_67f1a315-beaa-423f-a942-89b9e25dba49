package com.volvo.tisp.mtdisps.impl.consumer.asset.notification;

import java.math.BigInteger;
import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.mtdisps.impl.converter.vqv.AssetOperationalStatusNotificationConverter;
import com.volvo.tisp.mtdisps.impl.converter.vqv.AssetOperationalStatusRecord;
import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.model.Version;
import com.volvo.tisp.mtdisps.impl.reporter.AssetOperationalNotificationMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.asset.notification.AssetOperationalNotificationDomainManager;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.wirelesscar.va.v1.VehicleNonoperationalNotification;
import com.wirelesscar.va.v1.VehicleOperationalNotification;

class AssetOperationalNotificationJmsControllerTest {
  private static AssetOperationalStatusRecord createAssetOperationalStatusRecord(OperationalStatus operationalStatus) {
    return new AssetOperationalStatusRecord(TestUtils.VPI, new Version(1), operationalStatus);
  }

  private static VehicleNonoperationalNotification createNonOperationalNotification() {
    VehicleNonoperationalNotification vehicleNonoperationalNotification = new VehicleNonoperationalNotification();
    vehicleNonoperationalNotification.setVehiclePlatformId(TestUtils.VPI.toString());
    vehicleNonoperationalNotification.setVehicleVersion(BigInteger.ONE);

    return vehicleNonoperationalNotification;
  }

  private static VehicleOperationalNotification createOperationalNotification() {
    VehicleOperationalNotification vehicleOperationalNotification = new VehicleOperationalNotification();
    vehicleOperationalNotification.setVehiclePlatformId(TestUtils.VPI.toString());
    vehicleOperationalNotification.setVehicleVersion(BigInteger.ONE);

    return vehicleOperationalNotification;
  }

  @Test
  void handleNonOperationalNotification_ConversionFailureTest() {
    JmsMessage<VehicleNonoperationalNotification> jmsMessage = Mockito.mock(JmsMessage.class);
    AssetOperationalNotificationDomainManager assetOperationalNotificationDomainManager = Mockito.mock(AssetOperationalNotificationDomainManager.class);
    AssetOperationalStatusNotificationConverter assetOperationalStatusNotificationConverter = Mockito.mock(AssetOperationalStatusNotificationConverter.class);
    AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter = Mockito.mock(AssetOperationalNotificationMetricReporter.class);

    VehicleNonoperationalNotification nonOperationalNotification = createNonOperationalNotification();
    Mockito.when(jmsMessage.payload()).thenReturn(nonOperationalNotification);
    Mockito.when(assetOperationalStatusNotificationConverter.convert(Mockito.any(VehicleNonoperationalNotification.class)))
        .thenReturn(Optional.empty());

    AssetOperationalNotificationJmsController assetOperationalNotificationJmsController = new AssetOperationalNotificationJmsController(
        assetOperationalStatusNotificationConverter, assetOperationalNotificationDomainManager, assetOperationalNotificationMetricReporter);

    assetOperationalNotificationJmsController.handleNonOperationalNotification(jmsMessage);

    Mockito.verifyNoInteractions(assetOperationalNotificationDomainManager);
    Mockito.verify(assetOperationalNotificationMetricReporter).onFailure(ValidationFailureReason.CONVERSION_FAILURE);
    Mockito.verify(assetOperationalStatusNotificationConverter).convert(nonOperationalNotification);
    Mockito.verifyNoMoreInteractions(assetOperationalNotificationMetricReporter, assetOperationalStatusNotificationConverter);
  }

  @Test
  void handleNonOperationalNotification_SuccessTest() {
    JmsMessage<VehicleNonoperationalNotification> jmsMessage = Mockito.mock(JmsMessage.class);
    AssetOperationalNotificationDomainManager assetOperationalNotificationDomainManager = Mockito.mock(AssetOperationalNotificationDomainManager.class);
    AssetOperationalStatusNotificationConverter assetOperationalStatusNotificationConverter = Mockito.mock(AssetOperationalStatusNotificationConverter.class);
    AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter = Mockito.mock(AssetOperationalNotificationMetricReporter.class);

    VehicleNonoperationalNotification nonOperationalNotification = createNonOperationalNotification();
    AssetOperationalStatusRecord assetOperationalStatusRecord = createAssetOperationalStatusRecord(OperationalStatus.NON_OPERATIONAL);
    Mockito.when(jmsMessage.payload()).thenReturn(nonOperationalNotification);
    Mockito.when(assetOperationalStatusNotificationConverter.convert(Mockito.any(VehicleNonoperationalNotification.class)))
        .thenReturn(Optional.of(assetOperationalStatusRecord));

    AssetOperationalNotificationJmsController assetOperationalNotificationJmsController = new AssetOperationalNotificationJmsController(
        assetOperationalStatusNotificationConverter, assetOperationalNotificationDomainManager, assetOperationalNotificationMetricReporter);

    assetOperationalNotificationJmsController.handleNonOperationalNotification(jmsMessage);

    Mockito.verify(assetOperationalNotificationDomainManager).process(assetOperationalStatusRecord);
    Mockito.verifyNoInteractions(assetOperationalNotificationMetricReporter);
    Mockito.verify(assetOperationalStatusNotificationConverter).convert(nonOperationalNotification);
    Mockito.verifyNoMoreInteractions(assetOperationalNotificationDomainManager, assetOperationalStatusNotificationConverter);
  }

  @Test
  void handleOperationalNotification_ConversionFailureTest() {
    JmsMessage<VehicleOperationalNotification> jmsMessage = Mockito.mock(JmsMessage.class);
    AssetOperationalNotificationDomainManager assetOperationalNotificationDomainManager = Mockito.mock(AssetOperationalNotificationDomainManager.class);
    AssetOperationalStatusNotificationConverter assetOperationalStatusNotificationConverter = Mockito.mock(AssetOperationalStatusNotificationConverter.class);
    AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter = Mockito.mock(AssetOperationalNotificationMetricReporter.class);

    VehicleOperationalNotification operationalNotification = createOperationalNotification();
    Mockito.when(jmsMessage.payload()).thenReturn(operationalNotification);
    Mockito.when(assetOperationalStatusNotificationConverter.convert(Mockito.any(VehicleNonoperationalNotification.class)))
        .thenReturn(Optional.empty());

    AssetOperationalNotificationJmsController assetOperationalNotificationJmsController = new AssetOperationalNotificationJmsController(
        assetOperationalStatusNotificationConverter, assetOperationalNotificationDomainManager, assetOperationalNotificationMetricReporter);

    assetOperationalNotificationJmsController.handleOperationalNotification(jmsMessage);

    Mockito.verifyNoInteractions(assetOperationalNotificationDomainManager);
    Mockito.verify(assetOperationalNotificationMetricReporter).onFailure(ValidationFailureReason.CONVERSION_FAILURE);
    Mockito.verify(assetOperationalStatusNotificationConverter).convert(operationalNotification);
    Mockito.verifyNoMoreInteractions(assetOperationalNotificationMetricReporter, assetOperationalStatusNotificationConverter);
  }

  @Test
  void handleOperationalNotification_SuccessTest() {
    JmsMessage<VehicleOperationalNotification> jmsMessage = Mockito.mock(JmsMessage.class);
    AssetOperationalNotificationDomainManager assetOperationalNotificationDomainManager = Mockito.mock(AssetOperationalNotificationDomainManager.class);
    AssetOperationalStatusNotificationConverter assetOperationalStatusNotificationConverter = Mockito.mock(AssetOperationalStatusNotificationConverter.class);
    AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter = Mockito.mock(AssetOperationalNotificationMetricReporter.class);

    VehicleOperationalNotification operationalNotification = createOperationalNotification();
    AssetOperationalStatusRecord assetOperationalStatusRecord = createAssetOperationalStatusRecord(OperationalStatus.OPERATIONAL);
    Mockito.when(jmsMessage.payload()).thenReturn(operationalNotification);
    Mockito.when(assetOperationalStatusNotificationConverter.convert(Mockito.any(VehicleOperationalNotification.class)))
        .thenReturn(Optional.of(assetOperationalStatusRecord));

    AssetOperationalNotificationJmsController assetOperationalNotificationJmsController = new AssetOperationalNotificationJmsController(
        assetOperationalStatusNotificationConverter, assetOperationalNotificationDomainManager, assetOperationalNotificationMetricReporter);

    assetOperationalNotificationJmsController.handleOperationalNotification(jmsMessage);

    Mockito.verify(assetOperationalNotificationDomainManager).process(assetOperationalStatusRecord);
    Mockito.verifyNoInteractions(assetOperationalNotificationMetricReporter);
    Mockito.verify(assetOperationalStatusNotificationConverter).convert(operationalNotification);
    Mockito.verifyNoMoreInteractions(assetOperationalNotificationDomainManager, assetOperationalStatusNotificationConverter);
  }

}
