package com.volvo.tisp.mtdisps.impl.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ServiceVersionTest {
  private static final int SERVICE_VERSION = 1234;

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new ServiceVersion(-1), "serviceVersion must not be negative: -1");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals(Integer.toString(SERVICE_VERSION), new ServiceVersion(SERVICE_VERSION).toString());
  }

  @Test
  void validConstructorTest() {
    Assertions.assertEquals(SERVICE_VERSION, new ServiceVersion(SERVICE_VERSION).serviceVersion());
  }
}