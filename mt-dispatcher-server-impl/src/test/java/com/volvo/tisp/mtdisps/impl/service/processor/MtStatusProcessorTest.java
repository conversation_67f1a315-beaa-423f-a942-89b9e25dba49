package com.volvo.tisp.mtdisps.impl.service.processor;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.mockito.Mockito;

import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.Status;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.service.IntegrationLogger;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;

class MtStatusProcessorTest {
  @Test
  void publishTest() {
    ProcessableMtStatus processableMtStatus = TestUtils.createProcessableMtStatus(Status.DELIVERED);
    MessagePublisher<ProcessableMtStatus> messagePublisher1 = mockedMessagePublisher(1, processableMtStatus);
    MessagePublisher<ProcessableMtStatus> messagePublisher2 = mockedMessagePublisher(1, processableMtStatus);
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    MtStatusProcessor mtStatusProcessor = new MtStatusProcessor(
        Map.of(MtMessageType.INTERNAL_MT_MESSAGE, messagePublisher1, MtMessageType.INTERNAL_MT_MESSAGE_WITH_ASSET_HARDWARE_ID, messagePublisher2),
        integrationLogger);

    CompletableFuture<Void> result = mtStatusProcessor.process(processableMtStatus);
    Assertions.assertThat(result).isCompletedWithValue(null);
    Mockito.verify(messagePublisher1).newMessage();
    Mockito.verify(integrationLogger).log(processableMtStatus, "MtStatusPublishSucceeded");
    Mockito.verifyNoMoreInteractions(messagePublisher1, integrationLogger);
    Mockito.verifyNoInteractions(messagePublisher2);
  }

  @Test
  void publishWithNoSubscribersTest() {
    ProcessableMtStatus processableMtStatus = TestUtils.createProcessableMtStatus(Status.DELIVERED);
    MessagePublisher<ProcessableMtStatus> messagePublisher1 = mockedMessagePublisher(0, processableMtStatus);
    MessagePublisher<ProcessableMtStatus> messagePublisher2 = mockedMessagePublisher(0, processableMtStatus);
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    MtStatusProcessor mtStatusProcessor = new MtStatusProcessor(
        Map.of(MtMessageType.INTERNAL_MT_MESSAGE, messagePublisher1, MtMessageType.INTERNAL_MT_MESSAGE_WITH_ASSET_HARDWARE_ID, messagePublisher2),
        integrationLogger);

    CompletableFuture<Void> result = mtStatusProcessor.process(processableMtStatus);
    Assertions.assertThat(result).isCompletedExceptionally();
    Mockito.verify(messagePublisher1).newMessage();
    Mockito.verify(integrationLogger).log(processableMtStatus, "NoMtStatusSubscribersFound");
    Mockito.verifyNoMoreInteractions(messagePublisher1, integrationLogger);
    Mockito.verifyNoInteractions(messagePublisher2);  }

  private MessagePublisher<ProcessableMtStatus> mockedMessagePublisher(int subscriberCount, ProcessableMtStatus processableMtStatus) {
    MessagePublisher<ProcessableMtStatus> messagePublisher = Mockito.mock(MessagePublisher.class);
    MessagePublisher.Message<ProcessableMtStatus> message = Mockito.mock(MessagePublisher.Message.class);
    Mockito.when(messagePublisher.newMessage()).thenReturn(message);
    Mockito.when(message.option(Mockito.any(), Mockito.any(List.class))).thenReturn(message);
    Mockito.when(message.option(Mockito.any(), Mockito.any(Long.class))).thenReturn(message);
    Mockito.when(message.option(Mockito.any(), Mockito.any(String.class))).thenReturn(message);
    Mockito.when(message.publish(processableMtStatus)).thenReturn(CompletableFuture.completedFuture(subscriberCount));
    return messagePublisher;
  }
}
