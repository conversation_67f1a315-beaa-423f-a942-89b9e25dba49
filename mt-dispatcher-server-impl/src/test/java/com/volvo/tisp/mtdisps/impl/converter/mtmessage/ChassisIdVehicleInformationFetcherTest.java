package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.dao.IncorrectResultSizeDataAccessException;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.model.MultipleAssetsFoundException;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

class ChassisIdVehicleInformationFetcherTest {
  @Test
  void activationFetchTest() throws MultipleAssetsFoundException {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    Mockito.when(vehicleInformationRepository.findByChassisId(TestUtils.CHASSIS_ID.toString()))
        .thenReturn(Optional.of(vehicleInformationEntity));
    ChassisIdVehicleInformationFetcher chassisIdVehicleInformationFetcher = new ChassisIdVehicleInformationFetcher(vehicleInformationRepository);

    Optional<VehicleInformation> vehicleInformationOptional = chassisIdVehicleInformationFetcher.fetch(TestUtils.CHASSIS_ID.value(), true);

    org.assertj.core.api.Assertions.assertThat(vehicleInformationOptional).isPresent().contains(VehicleInformation.fromEntity(vehicleInformationEntity));
    Mockito.verify(vehicleInformationRepository).findByChassisId(TestUtils.CHASSIS_ID.value());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void fetchTest() throws MultipleAssetsFoundException {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    Mockito.when(vehicleInformationRepository.findByChassisIdAndOperationalIsTrue(TestUtils.CHASSIS_ID.toString()))
        .thenReturn(Optional.of(vehicleInformationEntity));
    ChassisIdVehicleInformationFetcher chassisIdVehicleInformationFetcher = new ChassisIdVehicleInformationFetcher(vehicleInformationRepository);

    Optional<VehicleInformation> vehicleInformationOptional = chassisIdVehicleInformationFetcher.fetch(TestUtils.CHASSIS_ID.value(), false);

    org.assertj.core.api.Assertions.assertThat(vehicleInformationOptional).isPresent().contains(VehicleInformation.fromEntity(vehicleInformationEntity));
    Mockito.verify(vehicleInformationRepository).findByChassisIdAndOperationalIsTrue(TestUtils.CHASSIS_ID.value());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void fetchWithMultipleAssetsTest() {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    Mockito.when(vehicleInformationRepository.findByChassisIdAndOperationalIsTrue(TestUtils.CHASSIS_ID.toString()))
        .thenThrow(IncorrectResultSizeDataAccessException.class);
    ChassisIdVehicleInformationFetcher chassisIdVehicleInformationFetcher = new ChassisIdVehicleInformationFetcher(vehicleInformationRepository);

    Assertions.assertThatExceptionOfType(MultipleAssetsFoundException.class)
        .isThrownBy(() -> chassisIdVehicleInformationFetcher.fetch(TestUtils.CHASSIS_ID.toString(), false));

    Mockito.verify(vehicleInformationRepository).findByChassisIdAndOperationalIsTrue(TestUtils.CHASSIS_ID.value());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void getSupportedAssetHardwareIdentifierType() {
    ChassisIdVehicleInformationFetcher chassisIdVehicleInformationFetcher = new ChassisIdVehicleInformationFetcher(
        Mockito.mock(VehicleInformationRepository.class));

    Assertions.assertThat(chassisIdVehicleInformationFetcher.getSupportedAssetIdentifierType()).isEqualTo(AssetIdentifierType.CHASSIS_ID);
  }
}