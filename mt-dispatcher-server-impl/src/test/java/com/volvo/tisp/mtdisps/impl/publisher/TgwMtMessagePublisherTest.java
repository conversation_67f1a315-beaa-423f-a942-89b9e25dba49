package com.volvo.tisp.mtdisps.impl.publisher;

import jakarta.jms.JMSException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessagePostProcessor;

import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.reporter.MessagePublisherMetricReporter;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

class TgwMtMessagePublisherTest {
  private static final String QUEUE_NAME = "queueDetails";

  @Test
  void publishFailureTest() {
    JmsTemplate jmsTemplate = Mockito.mock(JmsTemplate.class);
    MessagePublisherMetricReporter messagePublisherMetricReporter = Mockito.mock(MessagePublisherMetricReporter.class);

    MtMessage mtMessage = TestUtils.createTgwMtMessage();

    Mockito.doThrow(new RuntimeException("simulated test")).when(jmsTemplate).convertAndSend(Mockito.eq(QUEUE_NAME), Mockito.eq(mtMessage), Mockito.any());

    TgwMtMessagePublisher tgwMtMessagePublisher = new TgwMtMessagePublisher(jmsTemplate, messagePublisherMetricReporter, QUEUE_NAME);
    AssertThrows.exception(() -> tgwMtMessagePublisher.publish(mtMessage), "simulated test", RuntimeException.class);

    Mockito.verify(jmsTemplate).convertAndSend(Mockito.eq(QUEUE_NAME), Mockito.eq(mtMessage), Mockito.any());
    Mockito.verify(messagePublisherMetricReporter).onMessagePublishFailure(FlowType.TGW);
    Mockito.verifyNoMoreInteractions(jmsTemplate, messagePublisherMetricReporter);
  }

  @Test
  void publishNullMessageTest() {
    AssertThrows.illegalArgumentException(
        () -> new TgwMtMessagePublisher(Mockito.mock(JmsTemplate.class), Mockito.mock(MessagePublisherMetricReporter.class), "queue-name").publish(null),
        "message must not be null");
  }

  @Test
  void publishTest() throws JMSException {
    JmsTemplate jmsTemplate = Mockito.mock(JmsTemplate.class);
    jakarta.jms.Message message = Mockito.mock(jakarta.jms.Message.class);
    MessagePublisherMetricReporter messagePublisherMetricReporter = Mockito.mock(MessagePublisherMetricReporter.class);

    MtMessage mtMessage = TestUtils.createTgwMtMessage();

    TgwMtMessagePublisher tgwMtMessagePublisher = new TgwMtMessagePublisher(jmsTemplate, messagePublisherMetricReporter, QUEUE_NAME);
    tgwMtMessagePublisher.publish(mtMessage);

    ArgumentCaptor<MessagePostProcessor> postProcessorCaptor = ArgumentCaptor.forClass(MessagePostProcessor.class);
    Mockito.verify(jmsTemplate, Mockito.timeout(1_000))
        .convertAndSend(ArgumentMatchers.eq(QUEUE_NAME), ArgumentMatchers.any(), postProcessorCaptor.capture());
    Assertions.assertInstanceOf(MessagePostProcessor.class, postProcessorCaptor.getValue());
    postProcessorCaptor.getValue().postProcessMessage(message);
    Mockito.verify(message).setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), MessageTypesJms.TCE_MT_MESSAGE_TYPE);
    Mockito.verify(message).setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), MessageTypesJms.VERSION_2_0);

    Mockito.verify(messagePublisherMetricReporter).onMessagePublishDuration(Mockito.any(), Mockito.eq(FlowType.TGW));
    Mockito.verifyNoMoreInteractions(messagePublisherMetricReporter, jmsTemplate);
  }
}
