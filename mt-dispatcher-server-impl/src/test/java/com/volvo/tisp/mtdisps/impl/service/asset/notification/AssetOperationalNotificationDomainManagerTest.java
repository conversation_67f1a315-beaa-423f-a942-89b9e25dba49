package com.volvo.tisp.mtdisps.impl.service.asset.notification;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.converter.vqv.AssetOperationalStatusRecord;
import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.model.Version;
import com.volvo.tisp.mtdisps.impl.reporter.AssetOperationalNotificationMetricReporter;
import com.volvo.tisp.mtdisps.impl.reporter.Crud;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;

class AssetOperationalNotificationDomainManagerTest {
  public static final Vpi VPI = Vpi.ofString("1FAC35BF1EDB1DFF91AAC0BEBC56F22B");

  @Test
  void testProcessWhenAssetNotFound() {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    Clock clock = Mockito.mock(Clock.class);
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter = Mockito.mock(AssetOperationalNotificationMetricReporter.class);

    Instant now = Instant.now();
    Mockito.when(clock.instant()).thenReturn(now);
    Mockito.when(vehicleInformationRepository.findByVpi(VPI.toString())).thenReturn(Optional.empty());

    AssetOperationalStatusRecord assetOperationalStatusRecord = new AssetOperationalStatusRecord(VPI, new Version(1), OperationalStatus.OPERATIONAL);

    // Act
    AssetOperationalNotificationDomainManager domainManager = new AssetOperationalNotificationDomainManager(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, assetOperationalNotificationMetricReporter);
    domainManager.process(assetOperationalStatusRecord);

    // Assert
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Mockito.any(), Mockito.eq(Crud.READ));
    Mockito.verify(assetOperationalNotificationMetricReporter).onFailure(ValidationFailureReason.ASSET_NOT_FOUND);
    Mockito.verify(vehicleInformationRepository).findByVpi(VPI.toString());
    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository, clock, assetOperationalNotificationMetricReporter);
  }

  @Test
  void testProcessWithDBUpdateException() {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    Clock clock = Mockito.mock(Clock.class);
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter = Mockito.mock(AssetOperationalNotificationMetricReporter.class);

    Instant now = Instant.now();
    Mockito.when(clock.instant()).thenReturn(now);
    VehicleInformationEntity entity = Mockito.mock(VehicleInformationEntity.class);
    Mockito.when(entity.getOperationalVersion()).thenReturn(1L);
    Mockito.when(vehicleInformationRepository.findByVpi(VPI.toString())).thenReturn(Optional.of(entity));
    Mockito.when(vehicleInformationRepository.save(Mockito.any())).thenThrow(new RuntimeException("simulated test exception"));

    AssetOperationalStatusRecord assetOperationalStatusRecord = new AssetOperationalStatusRecord(VPI, new Version(2), OperationalStatus.OPERATIONAL);

    // Act
    AssetOperationalNotificationDomainManager domainManager = new AssetOperationalNotificationDomainManager(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, assetOperationalNotificationMetricReporter);

    Assertions.assertThrows(RuntimeException.class, () -> domainManager.process(assetOperationalStatusRecord));
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudFailure(Crud.UPDATE);
    Mockito.verify(clock, Mockito.times(3)).instant();
  }

  @Test
  void testProcessWithHigherVersion() {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    Clock clock = Mockito.mock(Clock.class);
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter = Mockito.mock(AssetOperationalNotificationMetricReporter.class);
    VehicleInformationEntity entity = Mockito.mock(VehicleInformationEntity.class);

    Instant now = Instant.now();
    Mockito.when(clock.instant()).thenReturn(now);
    Mockito.when(entity.getOperationalVersion()).thenReturn(9L);
    Mockito.when(vehicleInformationRepository.findByVpi(VPI.toString())).thenReturn(Optional.of(entity));

    AssetOperationalStatusRecord assetOperationalStatusRecord = new AssetOperationalStatusRecord(VPI, new Version(8), OperationalStatus.OPERATIONAL);

    // Act
    AssetOperationalNotificationDomainManager domainManager = new AssetOperationalNotificationDomainManager(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, assetOperationalNotificationMetricReporter);

    domainManager.process(assetOperationalStatusRecord);

    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Mockito.any(Duration.class), Mockito.eq(Crud.READ));
    Mockito.verify(vehicleInformationRepository, Mockito.never()).save(Mockito.any());
  }

  @Test
  void testProcessWithInsertDbException() {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    Clock clock = Mockito.mock(Clock.class);
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    AssetOperationalNotificationMetricReporter assetOperationalNotificationMetricReporter = Mockito.mock(AssetOperationalNotificationMetricReporter.class);

    Instant now = Instant.now();
    Mockito.when(clock.instant()).thenReturn(now);
    Mockito.when(vehicleInformationRepository.findByVpi(VPI.toString())).thenThrow(new RuntimeException("Simulated Test exception"));

    AssetOperationalStatusRecord assetOperationalStatusRecord = new AssetOperationalStatusRecord(VPI, new Version(1), OperationalStatus.OPERATIONAL);

    // Act
    AssetOperationalNotificationDomainManager domainManager = new AssetOperationalNotificationDomainManager(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, assetOperationalNotificationMetricReporter);

    Assertions.assertThrows(RuntimeException.class, () -> domainManager.process(assetOperationalStatusRecord));
  }
}
