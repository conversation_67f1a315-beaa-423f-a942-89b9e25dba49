package com.volvo.tisp.mtdisps.impl.consumer.asset.notification;

import java.util.Optional;
import java.util.function.Function;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.messaging.support.GenericMessage;

import com.volvo.tisp.mtdisps.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.mtdisps.impl.reporter.VqvNotificationMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.asset.notification.VqvNotificationDomainManager;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

import tisp.framework.internal.jms.JmsMessageImpl;

class VqvNotificationJmsControllerTest {
  private static final String BODY_STRING = "body string";

  private static Function<String, Optional<VqvNotification>> mockFunction(Optional<VqvNotification> optional) {
    Function<String, Optional<VqvNotification>> function = Mockito.mock(Function.class);
    Mockito.when(function.apply(BODY_STRING)).thenReturn(optional);
    return function;
  }

  @Test
  void handleNotificationNotFoundTest() {
    Function<String, Optional<VqvNotification>> vqvNotificationConverter = mockFunction(Optional.empty());
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);
    VqvNotificationDomainManager vqvNotificationDomainManager = Mockito.mock(VqvNotificationDomainManager.class);

    JmsMessageImpl<String> jmsMessage = new JmsMessageImpl<>(new GenericMessage<>(BODY_STRING));
    VqvNotificationJmsController vqvNotificationJmsController = new VqvNotificationJmsController(vqvNotificationConverter, vqvNotificationDomainManager,
        vqvNotificationMetricReporter);

    vqvNotificationJmsController.handleNotification(jmsMessage);

    Mockito.verify(vqvNotificationConverter).apply(BODY_STRING);
    Mockito.verify(vqvNotificationMetricReporter).onConversionFailure();
    Mockito.verifyNoMoreInteractions(vqvNotificationConverter, vqvNotificationMetricReporter, vqvNotificationDomainManager);
  }

  @Test
  void handleNotificationTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    Function<String, Optional<VqvNotification>> vqvNotificationConverter = mockFunction(Optional.of(vqvNotification));
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);
    VqvNotificationDomainManager vqvNotificationDomainManager = Mockito.mock(VqvNotificationDomainManager.class);

    JmsMessageImpl<String> jmsMessage = new JmsMessageImpl<>(new GenericMessage<>(BODY_STRING));
    VqvNotificationJmsController vqvNotificationJmsController = new VqvNotificationJmsController(vqvNotificationConverter, vqvNotificationDomainManager,
        vqvNotificationMetricReporter);

    vqvNotificationJmsController.handleNotification(jmsMessage);

    Mockito.verify(vqvNotificationConverter).apply(BODY_STRING);
    Mockito.verify(vqvNotificationDomainManager).process(vqvNotification);
    Mockito.verifyNoMoreInteractions(vqvNotificationConverter, vqvNotificationMetricReporter, vqvNotificationDomainManager);
  }
}
