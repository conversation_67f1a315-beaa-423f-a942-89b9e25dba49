package com.volvo.tisp.mtdisps.impl.conf;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

import software.amazon.awssdk.services.sqs.SqsAsyncClient;

class SqsConfigTest {
  @Test
  void createSqsAsyncClientTest() {
    try (LocalStackContainer sqsLocalStackContainer = TestUtils.getSQSLocalStackContainer()) {
      Assertions.assertNotNull(new SqsConfig().createSqsClient(TestUtils.getSqsClientConfigProperties(sqsLocalStackContainer)));
    }
  }

  @Test
  void sqsMessageListenerContainerFactoryTest() {
    SqsListenerConfigProperties sqsListenerConfigProperties = new SqsListenerConfigProperties(Duration.ofSeconds(5), 50, 10, 100, 10, 100,
        Duration.ofSeconds(30), Duration.ofSeconds(20));

    Assertions.assertNotNull(new SqsConfig().sqsMessageListenerContainerFactory(Mockito.mock(SqsAsyncClient.class), sqsListenerConfigProperties));
  }
}