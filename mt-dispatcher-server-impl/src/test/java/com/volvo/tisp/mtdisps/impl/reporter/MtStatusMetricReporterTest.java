package com.volvo.tisp.mtdisps.impl.reporter;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.utils.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

class MtStatusMetricReporterTest {
  @Test
  void onMtStatusReceiveLatencyTest() {
    Tags tags = Tags.of(Tag.of(MtStatusMetricReporter.TAG_EVENT, MtStatusMetricReporter.LATENCY));

    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onMtStatusReceiveLatency(Duration.ofSeconds(1));
      MetricsReporterTestUtils.checkTimer(meterRegistry, MtStatusMetricReporter.MT_STATUS_RECEIVE_LATENCY, tags, Duration.ofSeconds(1), 1);
    });

    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (meterRegistry, connectionEstablishedEventMetricReporter) -> {
      connectionEstablishedEventMetricReporter.onMtStatusReceiveLatency(Duration.ofSeconds(-1));
      MetricsReporterTestUtils.checkTimer(meterRegistry, MtStatusMetricReporter.MT_STATUS_RECEIVE_LATENCY, tags, Duration.ZERO, 1);
    });
  }

  @Test
  void onMtStatusReceivedTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onMtStatusReceived(FlowType.VCM);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.status.received", Tags.of("flow.type", FlowType.VCM.name()), 1);

      mtStatusMetricReporter.onMtStatusReceived(FlowType.VCM);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.status.received", Tags.of("flow.type", FlowType.VCM.name()), 2);
    });
  }

  @Test
  void onStatusFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      FlowType flowType = FlowType.VCM;
      mtStatusMetricReporter.onMtStatusFailure(ValidationFailureReason.CONVERSION_FAILURE, flowType);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.status.failed",
          Tags.of("type", flowType.name(), "reason", ValidationFailureReason.CONVERSION_FAILURE.name()), 1);

      mtStatusMetricReporter.onMtStatusFailure(ValidationFailureReason.CONVERSION_FAILURE, flowType);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.status.failed",
          Tags.of("type", flowType.name(), "reason", ValidationFailureReason.CONVERSION_FAILURE.name()), 2);
    });
  }
}
