package com.volvo.tisp.mtdisps.impl.service.asset.notification;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

class VqvNotificationDomainManagerTest {
  @Test
  void processTgwVqvNotification() {
    VqvNotificationProcessor tgwVqvNotificationProcessor = Mockito.mock(VqvNotificationProcessor.class);
    VqvNotificationProcessor vcmVqvNotificationProcessor = Mockito.mock(VqvNotificationProcessor.class);

    VqvNotificationDomainManager vqvNotificationDomainManager = new VqvNotificationDomainManager(tgwVqvNotificationProcessor, vcmVqvNotificationProcessor);
    VqvNotification vqvNotification = TestUtils.createVqvNotification(TelematicUnitType.TGW);
    vqvNotificationDomainManager.process(vqvNotification);

    Mockito.verify(tgwVqvNotificationProcessor).process(vqvNotification);
    Mockito.verifyNoInteractions(vcmVqvNotificationProcessor);
    Mockito.verifyNoMoreInteractions(tgwVqvNotificationProcessor);
  }

  @Test
  void processVcmVqvNotification() {
    VqvNotificationProcessor tgwVqvNotificationProcessor = Mockito.mock(VqvNotificationProcessor.class);
    VqvNotificationProcessor vcmVqvNotificationProcessor = Mockito.mock(VqvNotificationProcessor.class);

    VqvNotificationDomainManager vqvNotificationDomainManager = new VqvNotificationDomainManager(tgwVqvNotificationProcessor, vcmVqvNotificationProcessor);
    VqvNotification vqvNotification = TestUtils.createVqvNotification(TelematicUnitType.VCM);

    vqvNotificationDomainManager.process(vqvNotification);

    Mockito.verify(vcmVqvNotificationProcessor).process(vqvNotification);
    Mockito.verifyNoInteractions(tgwVqvNotificationProcessor);
    Mockito.verifyNoMoreInteractions(vcmVqvNotificationProcessor);
  }
}
