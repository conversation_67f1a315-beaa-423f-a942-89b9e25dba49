package com.volvo.tisp.mtdisps.impl.converter.mtstatus;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.Status;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.reporter.InternalMtStatusConverterMetricReporter;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtStatusConverterTest {

  private static ProcessableMtStatus createProcessableMtStatus(String description, Optional<Vpi> vpiOption) {
    return new ProcessableMtStatus(
        TestUtils.CORRELATION_ID,
        description,
        Status.DELIVERED,
        TestUtils.TRACKING_ID,
        TestUtils.SERVICE_FUNCTION,
        TestUtils.SERVICE_ID,
        TestUtils.SERVICE_VERSION,
        Optional.empty(),
        vpiOption,
        MtMessageType.INTERNAL_MT_MESSAGE);
  }

  private static void verifyMtStatus(com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus mtStatus) {
    Assertions.assertEquals(TestUtils.CORRELATION_ID.toString(), mtStatus.getCorrelationId());
    Assertions.assertEquals(TestUtils.DESCRIPTION, mtStatus.getDescription());
    Assertions.assertEquals(com.volvo.tisp.vc.mt.message.client.json.v1.Status.DELIVERED, mtStatus.getStatus());
    Assertions.assertEquals(TestUtils.TRACKING_ID.toString(), mtStatus.getTrackingId());
    Assertions.assertEquals(TestUtils.VPI.toString(), mtStatus.getVehiclePlatformId());
  }

  @Test
  void applyInvalidTest() {
    MtStatusConverter mtStatusConverter = new MtStatusConverter(Mockito.mock(InternalMtStatusConverterMetricReporter.class));

    AssertThrows.illegalArgumentException(() -> mtStatusConverter.convert(null), "processableMtStatus must not be null");
  }

  @Test
  void applyTest() {
    InternalMtStatusConverterMetricReporter mtStatusConverterMetricReporter = Mockito.mock(InternalMtStatusConverterMetricReporter.class);
    MtStatusConverter mtStatusConverter = new MtStatusConverter(mtStatusConverterMetricReporter);

    verifyMtStatus(mtStatusConverter.convert(TestUtils.createProcessableMtStatus(Status.DELIVERED)));
  }

  @Test
  void descriptionNullTest() {
    InternalMtStatusConverterMetricReporter mtStatusConverterMetricReporter = Mockito.mock(InternalMtStatusConverterMetricReporter.class);
    MtStatusConverter mtStatusConverter = new MtStatusConverter(mtStatusConverterMetricReporter);

    Assertions.assertDoesNotThrow(() -> mtStatusConverter.convert(createProcessableMtStatus(null, Optional.of(TestUtils.VPI))));
  }

  @Test
  void missingVpiTest() {
    InternalMtStatusConverterMetricReporter mtStatusConverterMetricReporter = Mockito.mock(InternalMtStatusConverterMetricReporter.class);
    MtStatusConverter mtStatusConverter = new MtStatusConverter(mtStatusConverterMetricReporter);

    ProcessableMtStatus processableMtStatus = createProcessableMtStatus("", Optional.empty());
    Assertions.assertThrows(IllegalStateException.class, () -> mtStatusConverter.convert(processableMtStatus));
    Mockito.verify(mtStatusConverterMetricReporter).onMissingVpi();
    Mockito.verifyNoMoreInteractions(mtStatusConverterMetricReporter);
  }

  @Test
  void statusTest() {
    InternalMtStatusConverterMetricReporter mtStatusConverterMetricReporter = Mockito.mock(InternalMtStatusConverterMetricReporter.class);
    MtStatusConverter mtStatusConverter = new MtStatusConverter(mtStatusConverterMetricReporter);
    Assertions.assertEquals(com.volvo.tisp.vc.mt.message.client.json.v1.Status.DELIVERED, mtStatusConverter.convert(TestUtils.createProcessableMtStatus(Status.DELIVERED)).getStatus());
    Assertions.assertEquals(com.volvo.tisp.vc.mt.message.client.json.v1.Status.FAILED, mtStatusConverter.convert(TestUtils.createProcessableMtStatus(Status.FAILED)).getStatus());
    Assertions.assertEquals(com.volvo.tisp.vc.mt.message.client.json.v1.Status.TIMEOUT, mtStatusConverter.convert(TestUtils.createProcessableMtStatus(Status.TIMEOUT)).getStatus());
    Assertions.assertEquals(com.volvo.tisp.vc.mt.message.client.json.v1.Status.UNSUPPORTED_SERVICE,
        mtStatusConverter.convert(TestUtils.createProcessableMtStatus(Status.UNSUPPORTED_SERVICE)).getStatus());
    Assertions.assertEquals(com.volvo.tisp.vc.mt.message.client.json.v1.Status.UNSUPPORTED_VERSION,
        mtStatusConverter.convert(TestUtils.createProcessableMtStatus(Status.UNSUPPORTED_VERSION)).getStatus());
    Assertions.assertEquals(com.volvo.tisp.vc.mt.message.client.json.v1.Status.ASSET_NOT_FOUND,
        mtStatusConverter.convert(TestUtils.createProcessableMtStatus(Status.ASSET_NOT_FOUND)).getStatus());
  }
}