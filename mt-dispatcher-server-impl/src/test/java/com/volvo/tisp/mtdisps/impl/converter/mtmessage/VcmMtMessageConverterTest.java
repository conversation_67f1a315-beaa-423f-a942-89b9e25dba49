package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;

class VcmMtMessageConverterTest {
  private static void verifyMtMessage(AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage expected,
      AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage actual) {
    Assertions.assertEquals(expected.getAssetHardwareId(), actual.getAssetHardwareId());
    Assertions.assertEquals(expected.getCorrelationId(), actual.getCorrelationId());
    Assertions.assertEquals(expected.getIsSoftcar(), actual.getIsSoftcar());
    Assertions.assertEquals(expected.getPriority(), actual.getPriority());
    Assertions.assertEquals(expected.getServiceAccessToken(), actual.getServiceAccessToken());
    Assertions.assertEquals(expected.getServiceId(), actual.getServiceId());
    Assertions.assertEquals(expected.getServiceVersion(), actual.getServiceVersion());
    Assertions.assertEquals(expected.getTtl(), actual.getTtl());
  }

  @Test
  void applyTest() {
    TrackingIdentifier trackingIdentifier = TrackingIdentifier.create();

    TispContext.runInContext(() -> {
      AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage mtMessage = new VcmMtMessageConverter().apply(
          TestUtils.createProcessableMtMessage(trackingIdentifier, TelematicUnitType.VCM));
      verifyMtMessage(TestUtils.createAssetMtSchedulerMtMessage(true), mtMessage);
    }, context -> context.tid(trackingIdentifier));
  }
}
