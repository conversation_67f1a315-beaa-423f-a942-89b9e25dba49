package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.external.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtMessage;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

class ExternalReceivedMtMessageConverterTest {
  @Test
  void convert() {
    ExternalReceivedMtMessageConverter externalReceivedMtMessageConverter = new ExternalReceivedMtMessageConverter();
    Optional<ReceivedMtMessage> receivedMtMessageOptional = externalReceivedMtMessageConverter.convert(TestUtils.createExternalMtMessage());
    Assertions.assertThat(receivedMtMessageOptional).isPresent();

    Assertions.assertThat(externalReceivedMtMessageConverter.convert("someMessage")).isEmpty();
  }

  @Test
  void convertWithoutOptionals() {
    ExternalReceivedMtMessageConverter externalReceivedMtMessageConverter = new ExternalReceivedMtMessageConverter();
    MtMessage mtMessage = TestUtils.createExternalMtMessageBase();

    Assertions.assertThat(externalReceivedMtMessageConverter.convert(mtMessage)).isPresent();

    mtMessage.withPriority(TestUtils.EXTERNAL_PRIORITY);
    Assertions.assertThat(externalReceivedMtMessageConverter.convert(mtMessage)).isPresent();

    mtMessage.withCorrelationId(TestUtils.CORRELATION_ID.toString());
    Assertions.assertThat(externalReceivedMtMessageConverter.convert(mtMessage)).isPresent();

    mtMessage.withPayloadSignature(TestUtils.PAYLOAD);
    Assertions.assertThat(externalReceivedMtMessageConverter.convert(mtMessage).orElseThrow().payloadSignatureDetails()).isEmpty();

    mtMessage.withKeyId(TestUtils.KEY_ID);
    Assertions.assertThat(externalReceivedMtMessageConverter.convert(mtMessage).orElseThrow().payloadSignatureDetails()).isPresent();
  }

  @Test
  void defaultServiceFunctionTest() {
    Optional<ReceivedMtMessage> optional = new ExternalReceivedMtMessageConverter().convert(TestUtils.createExternalMtMessage());
    Assertions.assertThat(optional.orElseThrow().serviceFunction()).hasToString("EXTERNAL");
  }
}
