package com.volvo.tisp.mtdisps.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataRetrievalFailureException;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.mtdisps.impl.reporter.Crud;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.mtdisps.impl.reporter.VqvNotificationMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.asset.notification.VqvNotificationProcessorImpl;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.wirelesscar.vqv.v1.api.NotificationType;

class VqvNotificationProcessorImplTest {
  @Test
  void processDeleteExceptionTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotificationBuilder().setMasterDataDeletedTimestamp(TestUtils.INSTANT).build();
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.doThrow(new DataRetrievalFailureException("TEST")).when(vehicleInformationRepository).deleteByVpi(vqvNotification.vpi().toString());

    VqvNotificationProcessorImpl vqvNotificationDomainManager = new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, vqvNotificationMetricReporter);
    Assertions.assertThrows(RuntimeException.class, () -> vqvNotificationDomainManager.process(vqvNotification), "TEST");

    Mockito.verify(vehicleInformationRepository).deleteByVpi(vqvNotification.vpi().toString());
    Mockito.verify(clock).instant();
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudFailure(Crud.DELETE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter, vqvNotificationMetricReporter);
  }

  @Test
  void processDeleteTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotificationBuilder().setMasterDataDeletedTimestamp(TestUtils.INSTANT).build();
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    VqvNotificationProcessorImpl vqvNotificationDomainManager = new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, vqvNotificationMetricReporter);
    vqvNotificationDomainManager.process(vqvNotification);

    Mockito.verify(vehicleInformationRepository).deleteByVpi(vqvNotification.vpi().toString());
    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Duration.ofSeconds(2), Crud.DELETE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter, vqvNotificationMetricReporter);
  }

  @Test
  void processInsertExceptionTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString()))
        .thenReturn(Optional.empty());
    Mockito.when(vehicleInformationRepository.save(Mockito.any()))
        .thenThrow(new DataRetrievalFailureException("Simulated Test"));

    VqvNotificationProcessorImpl vqvNotificationDomainManager = new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, vqvNotificationMetricReporter);

    Assertions.assertThrows(DataAccessException.class, () -> vqvNotificationDomainManager.process(vqvNotification), "Simulated Test");

    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository).save(Mockito.any());
    Mockito.verify(clock, Mockito.times(3)).instant();
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Duration.ofSeconds(2), Crud.READ);
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudFailure(Crud.CREATE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter, vqvNotificationMetricReporter);
  }

  @Test
  void processInsertTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString()))
        .thenReturn(Optional.empty());
    Mockito.when(vehicleInformationRepository.save(Mockito.any()))
        .thenReturn(vehicleInformationEntity);

    VqvNotificationProcessorImpl vqvNotificationProcessorImpl = new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, vqvNotificationMetricReporter);
    vqvNotificationProcessorImpl.process(vqvNotification);

    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository).findAllByChassisId(vqvNotification.vehicleInformation().chassisId().get().toString());
    Mockito.verify(vehicleInformationRepository)
        .findAllByPartNumberAndSerialNumber(vqvNotification.vehicleInformation().assetHardwareId().partNumber().toString(),
            vqvNotification.vehicleInformation().assetHardwareId().serialNumber().toString());
    Mockito.verify(vehicleInformationRepository).save(Mockito.any());
    Mockito.verify(clock, Mockito.times(4)).instant();
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Duration.ofSeconds(2), Crud.READ);
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Duration.ofSeconds(0), Crud.CREATE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter, vqvNotificationMetricReporter);
  }

  @Test
  void processRemovedTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotificationBuilder().setNotificationType(NotificationType.REMOVED).build();
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    VqvNotificationProcessorImpl vqvNotificationDomainManager = new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, vqvNotificationMetricReporter);
    vqvNotificationDomainManager.process(vqvNotification);

    Mockito.verify(vehicleInformationRepository).deleteByVpi(vqvNotification.vpi().toString());
    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Duration.ofSeconds(2), Crud.DELETE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter, vqvNotificationMetricReporter);
  }

  @Test
  void processStaleUpdateTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    vehicleInformationEntity.setVersion(vqvNotification.vehicleInformation().version().version() + 1);

    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString()))
        .thenReturn(Optional.of(vehicleInformationEntity));

    VqvNotificationProcessorImpl vqvNotificationProcessorImpl = new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, vqvNotificationMetricReporter);
    vqvNotificationProcessorImpl.process(vqvNotification);

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Duration.ofSeconds(2), Crud.READ);
    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository).findAllByChassisId(vqvNotification.vehicleInformation().chassisId().get().toString());
    Mockito.verify(vehicleInformationRepository)
        .findAllByPartNumberAndSerialNumber(vqvNotification.vehicleInformation().assetHardwareId().partNumber().toString(),
            vqvNotification.vehicleInformation().assetHardwareId().serialNumber().toString());
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter, vqvNotificationMetricReporter);
  }

  @Test
  void processUpdateExceptionTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    vehicleInformationEntity.setVersion(vqvNotification.vehicleInformation().version().version() - 1);

    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString()))
        .thenReturn(Optional.of(vehicleInformationEntity));
    Mockito.when(vehicleInformationRepository.save(vehicleInformationEntity))
        .thenThrow(new DataRetrievalFailureException("Simulated Test"));

    VqvNotificationProcessorImpl vqvNotificationProcessorImpl = new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, vqvNotificationMetricReporter);
    Assertions.assertThrows(DataAccessException.class, () -> vqvNotificationProcessorImpl.process(vqvNotification), "Simulated Test");

    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository).save(vehicleInformationEntity);
    Mockito.verify(clock, Mockito.times(3)).instant();
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Duration.ofSeconds(2), Crud.READ);
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudFailure(Crud.UPDATE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter, vqvNotificationMetricReporter);
  }

  @Test
  void processUpdateTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    vehicleInformationEntity.setVersion(vqvNotification.vehicleInformation().version().version() - 1);

    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString()))
        .thenReturn(Optional.of(vehicleInformationEntity));

    VqvNotificationProcessorImpl vqvNotificationProcessorImpl = new VqvNotificationProcessorImpl(clock, dbOperationsMetricReporter,
        vehicleInformationRepository, vqvNotificationMetricReporter);
    vqvNotificationProcessorImpl.process(vqvNotification);

    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository).findAllByChassisId(vqvNotification.vehicleInformation().chassisId().get().toString());
    Mockito.verify(vehicleInformationRepository)
        .findAllByPartNumberAndSerialNumber(vqvNotification.vehicleInformation().assetHardwareId().partNumber().toString(),
            vqvNotification.vehicleInformation().assetHardwareId().serialNumber().toString());
    Mockito.verify(vehicleInformationRepository).save(vehicleInformationEntity);
    Mockito.verify(clock, Mockito.times(4)).instant();
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Duration.ofSeconds(2), Crud.READ);
    Mockito.verify(dbOperationsMetricReporter).onVehicleInfoCrudDuration(Duration.ofSeconds(0), Crud.UPDATE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter, vqvNotificationMetricReporter);
  }
}
