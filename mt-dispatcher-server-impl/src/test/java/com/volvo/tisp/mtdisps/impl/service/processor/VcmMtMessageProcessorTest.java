package com.volvo.tisp.mtdisps.impl.service.processor;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.VcmMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.publisher.VcmMtMessagePublisher;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VcmMtMessageProcessorTest {
  @Test
  void processTest() {
    VcmMtMessageConverter vcmMtMessageConverter = Mockito.mock(VcmMtMessageConverter.class);
    VcmMtMessagePublisher vcmMtMessagePublisher = Mockito.mock(VcmMtMessagePublisher.class);

    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage(TrackingIdentifier.create());
    AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage mtMessage = TestUtils.createAssetMtSchedulerMtMessage(true);

    Mockito.when(vcmMtMessageConverter.apply(processableMtMessage)).thenReturn(mtMessage);
    Mockito.when(vcmMtMessagePublisher.publish(mtMessage)).thenReturn(CompletableFuture.completedFuture(null));

    VcmMtMessageProcessor vcmMtMessageProcessor = new VcmMtMessageProcessor(vcmMtMessageConverter, vcmMtMessagePublisher);
    CompletableFuture<Void> result = vcmMtMessageProcessor.process(processableMtMessage);

    Awaitility.await().atMost(Duration.ofSeconds(2)).untilAsserted(() -> Assertions.assertThat(result).isCompleted());

    AssertThrows.illegalArgumentException(() -> vcmMtMessageProcessor.process(null), "processableMtMessage must not be null");
  }
}
