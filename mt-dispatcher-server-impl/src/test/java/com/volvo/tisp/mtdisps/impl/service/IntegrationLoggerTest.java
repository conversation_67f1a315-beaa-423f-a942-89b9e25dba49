package com.volvo.tisp.mtdisps.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import com.volvo.tisp.integration.log.IntegrationLogEventPublisher;
import com.volvo.tisp.integration.log.v2.AssetIdentifier;
import com.volvo.tisp.integration.log.v2.IntegrationLogEvent;
import com.volvo.tisp.mtdisps.impl.conf.AppProperties;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

import junit.framework.AssertionFailedError;

class IntegrationLoggerTest {
  @Test
  void logTest() {
    AppProperties appProperties = new AppProperties(Duration.ofMinutes(1), true);
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochMilli(1000));
    IntegrationLogEventPublisher integrationLogEventPublisher = Mockito.mock(IntegrationLogEventPublisher.class);

    IntegrationLogger integrationLogger = new IntegrationLogger(appProperties, clock, integrationLogEventPublisher);
    integrationLogger.log(TestUtils.createProcessableMtMessage(TestUtils.TRACKING_ID), "a-detail");

    ArgumentCaptor<IntegrationLogEvent> argumentCaptor = ArgumentCaptor.forClass(IntegrationLogEvent.class);

    Mockito.verify(integrationLogEventPublisher).publish(argumentCaptor.capture());
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(integrationLogEventPublisher, clock);

    IntegrationLogEvent integrationLogEvent = argumentCaptor.getValue();
    Set<AssetIdentifier> assetIdentifiers = integrationLogEvent.getAssetIdentifiers();

    Assertions.assertEquals(2, assetIdentifiers.size());
    String expectedAssetId = TestUtils.PART_NUMBER + "-" + TestUtils.SERIAL_NUMBER;
    assetIdentifiers.forEach(assetIdentifier -> {
      switch (assetIdentifier.getType()) {
        case VPI -> Assertions.assertEquals(TestUtils.VPI.toString(), assetIdentifier.getAssetIdentifier());
        case ASSET_ID -> Assertions.assertEquals(expectedAssetId, assetIdentifier.getAssetIdentifier());
        case CHASSIS_ID -> throw new AssertionFailedError("chassisId not expected");
      }
    });

    Assertions.assertNotNull(integrationLogEvent.getId());
    Assertions.assertEquals(TestUtils.TRACKING_ID.toString(), integrationLogEvent.getTrackingId());
    Assertions.assertEquals("MTDISPS", integrationLogEvent.getSource());
    Assertions.assertEquals("a-detail", integrationLogEvent.getDetailType());
    Assertions.assertNotNull(integrationLogEvent.getDetail());
    Assertions.assertEquals(1000, integrationLogEvent.getTimestamp());
  }
}