package com.volvo.tisp.mtdisps.impl.data.rest;

import java.nio.file.Path;
import java.time.Clock;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.volvo.tisp.mtdisps.impl.data.VehicleInformationGenerator;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

class VehicleInformationRestControllerTest {
  @Test
  void generateVehiclesTest() {
    Clock clock = TestUtils.mockClock();
    VehicleInformationGenerator vehicleInformationGenerator = Mockito.mock(VehicleInformationGenerator.class);
    int numberOfVehicles = 100;

    Mockito.when(vehicleInformationGenerator.generateVehicleInformation(ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt(), ArgumentMatchers.any(Path.class)))
        .thenReturn(numberOfVehicles);

    VehicleInformationRestController vehicleInformationRestController = new VehicleInformationRestController(clock, vehicleInformationGenerator);
    ResponseEntity<String> stringResponseEntity = vehicleInformationRestController.generateVehicles(numberOfVehicles, 10, "vehicle.csv");

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(vehicleInformationGenerator)
        .generateVehicleInformation(ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt(), ArgumentMatchers.any(Path.class));
    Mockito.verifyNoMoreInteractions(vehicleInformationGenerator);
    Assertions.assertEquals(HttpStatus.OK, stringResponseEntity.getStatusCode());
    Assertions.assertTrue(stringResponseEntity.getBody().contains(numberOfVehicles + " vehicleInformation have been generated "));
  }
}
