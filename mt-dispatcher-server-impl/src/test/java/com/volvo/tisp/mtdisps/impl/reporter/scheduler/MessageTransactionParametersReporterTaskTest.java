package com.volvo.tisp.mtdisps.impl.reporter.scheduler;

import java.time.Instant;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;

class MessageTransactionParametersReporterTaskTest {
  private static MessageTransactionParametersRepository mockMessageTransactionParametersRepository(long messageCount) {
    MessageTransactionParametersRepository messageTransactionParametersRepository = Mockito.mock(MessageTransactionParametersRepository.class);
    Mockito.when(messageTransactionParametersRepository.countRecordsBeforeCurrentTime(Mockito.any(Instant.class))).thenReturn(messageCount);
    return messageTransactionParametersRepository;
  }

  @Test
  void exceptionTest() {
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MessageTransactionParametersRepository messageTransactionParametersRepository = Mockito.mock(MessageTransactionParametersRepository.class);

    Mockito.doThrow(new RuntimeException("test")).when(messageTransactionParametersRepository).countRecordsBeforeCurrentTime(Mockito.any(Instant.class));

    MessageTransactionParametersReporterTask messageTransactionParametersReporterTask = new MessageTransactionParametersReporterTask(
        dbOperationsMetricReporter, messageTransactionParametersRepository);
    messageTransactionParametersReporterTask.getExpiredMessageCountBeforeCleanup();

    Mockito.verifyNoInteractions(dbOperationsMetricReporter);
  }

  @Test
  void runTest() {
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MessageTransactionParametersRepository messageTransactionParametersRepository = mockMessageTransactionParametersRepository(10);

    MessageTransactionParametersReporterTask messageTransactionParametersReporterTask = new MessageTransactionParametersReporterTask(
        dbOperationsMetricReporter, messageTransactionParametersRepository);
    messageTransactionParametersReporterTask.getExpiredMessageCountBeforeCleanup();

    Mockito.verify(dbOperationsMetricReporter).onExpiredMessageTransactionCount(10);
    Mockito.verifyNoMoreInteractions(dbOperationsMetricReporter);
  }
}
