package com.volvo.tisp.mtdisps.impl.consumer.mtstatus;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.mtdisps.impl.converter.mtstatus.ReceivedVcmMtStatusConverter;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtStatus;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.MtStatusMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.MtStatusDomainManager;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtStatusProtobuf;

class VcmMtStatusJmsControllerTest {
  private static JmsMessage<AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus> mockJmsMessage(
      AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus mtStatusMessage) {
    JmsMessage<AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus> jmsMessage = Mockito.mock(JmsMessage.class);
    Mockito.when(jmsMessage.payload()).thenReturn(mtStatusMessage);
    return jmsMessage;
  }

  @Test
  void receiveMtStatusConversionFailureTest() {
    MtStatusDomainManager mtStatusDomainManager = Mockito.mock(MtStatusDomainManager.class);
    MtStatusMetricReporter mtStatusMetricReporter = Mockito.mock(MtStatusMetricReporter.class);
    ReceivedVcmMtStatusConverter receivedVcmMtStatusConverter = Mockito.mock(ReceivedVcmMtStatusConverter.class);

    AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus mtStatusMessage = TestUtils.createAssetMtSchedulerMtStatus(true);
    JmsMessage<AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus> jmsMessage = mockJmsMessage(mtStatusMessage);

    Mockito.when(receivedVcmMtStatusConverter.apply(mtStatusMessage)).thenReturn(Optional.empty());

    VcmMtStatusJmsController vcmMtStatusJmsController = new VcmMtStatusJmsController(mtStatusDomainManager, mtStatusMetricReporter,
        receivedVcmMtStatusConverter);
    vcmMtStatusJmsController.receiveMtStatus(jmsMessage);

    Mockito.verify(receivedVcmMtStatusConverter).apply(mtStatusMessage);
    Mockito.verify(mtStatusMetricReporter).onMtStatusFailure(ValidationFailureReason.CONVERSION_FAILURE, FlowType.VCM);
    Mockito.verify(mtStatusMetricReporter).onMtStatusReceived(FlowType.VCM);
    Mockito.verifyNoInteractions(mtStatusDomainManager);
    Mockito.verifyNoMoreInteractions(mtStatusMetricReporter, receivedVcmMtStatusConverter);
  }

  @Test
  void receiveMtStatusProcessFailureTest() {
    MtStatusDomainManager mtStatusDomainManager = Mockito.mock(MtStatusDomainManager.class);
    MtStatusMetricReporter mtStatusMetricReporter = Mockito.mock(MtStatusMetricReporter.class);
    ReceivedVcmMtStatusConverter receivedVcmMtStatusConverter = Mockito.mock(ReceivedVcmMtStatusConverter.class);

    ReceivedMtStatus receivedMtStatus = TestUtils.createReceivedMtStatus();
    AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus mtStatusMessage = TestUtils.createAssetMtSchedulerMtStatus(true);
    JmsMessage<AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus> jmsMessage = mockJmsMessage(mtStatusMessage);

    Mockito.when(receivedVcmMtStatusConverter.apply(mtStatusMessage)).thenReturn(Optional.of(receivedMtStatus));
    Mockito.doThrow(new RuntimeException("Simulated Test exception")).when(mtStatusDomainManager).processMessage(receivedMtStatus, FlowType.VCM);

    VcmMtStatusJmsController vcmMtStatusJmsController = new VcmMtStatusJmsController(mtStatusDomainManager, mtStatusMetricReporter,
        receivedVcmMtStatusConverter);
    CompletableFuture<Void> completableFuture = vcmMtStatusJmsController.receiveMtStatus(jmsMessage);

    Assertions.assertTrue(completableFuture.isCompletedExceptionally());
    Mockito.verify(receivedVcmMtStatusConverter).apply(mtStatusMessage);
    Mockito.verify(mtStatusDomainManager).processMessage(receivedMtStatus, FlowType.VCM);
    Mockito.verify(mtStatusMetricReporter).onMtStatusReceived(FlowType.VCM);
    Mockito.verifyNoMoreInteractions(mtStatusDomainManager, receivedVcmMtStatusConverter, mtStatusMetricReporter);
  }

  @Test
  void receiveMtStatusTest() {
    MtStatusDomainManager mtStatusDomainManager = Mockito.mock(MtStatusDomainManager.class);
    MtStatusMetricReporter mtStatusMetricReporter = Mockito.mock(MtStatusMetricReporter.class);
    ReceivedVcmMtStatusConverter receivedVcmMtStatusConverter = Mockito.mock(ReceivedVcmMtStatusConverter.class);

    ReceivedMtStatus receivedMtStatus = TestUtils.createReceivedMtStatus();
    AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus mtStatusMessage = TestUtils.createAssetMtSchedulerMtStatus(true);
    JmsMessage<AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus> jmsMessage = mockJmsMessage(mtStatusMessage);

    Mockito.when(receivedVcmMtStatusConverter.apply(mtStatusMessage)).thenReturn(Optional.of(receivedMtStatus));
    Mockito.when(mtStatusDomainManager.processMessage(receivedMtStatus, FlowType.VCM)).thenReturn(CompletableFuture.completedFuture(null));

    VcmMtStatusJmsController vcmMtStatusJmsController = new VcmMtStatusJmsController(mtStatusDomainManager, mtStatusMetricReporter,
        receivedVcmMtStatusConverter);
    vcmMtStatusJmsController.receiveMtStatus(jmsMessage);

    Mockito.verify(receivedVcmMtStatusConverter).apply(mtStatusMessage);
    Mockito.verify(mtStatusDomainManager).processMessage(receivedMtStatus, FlowType.VCM);
    Mockito.verify(mtStatusMetricReporter).onMtStatusReceived(FlowType.VCM);
    Mockito.verifyNoMoreInteractions(mtStatusDomainManager, receivedVcmMtStatusConverter, mtStatusMetricReporter);
  }
}
