package com.volvo.tisp.mtdisps.impl.service.processor;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.framework.jms.DestinationNamingConvention;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.consumer.mtstatus.TgwMtStatusJmsController;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.TgwMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.publisher.TgwMtMessagePublisher;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.api.v2.MtMessage;

class TgwMtMessageProcessorTest {
  @Test
  void invalidProcessTest() {
    TgwMtMessageProcessor tgwMtMessageProcessor = new TgwMtMessageProcessor(Mockito.mock(DestinationNamingConvention.class),
        Mockito.mock(TgwMtMessageConverter.class), Mockito.mock(TgwMtMessagePublisher.class));
    AssertThrows.illegalArgumentException(() -> tgwMtMessageProcessor.process(null), "processableMtMessage must not be null");
  }

  @Test
  void processTest() {
    TgwMtMessageConverter tgwMtMessageConverter = Mockito.mock(TgwMtMessageConverter.class);
    TgwMtMessagePublisher tgwMtMessagePublisher = Mockito.mock(TgwMtMessagePublisher.class);
    DestinationNamingConvention destinationNamingConvention = Mockito.mock(DestinationNamingConvention.class);

    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage(TrackingIdentifier.create());
    MtMessage mtMessage = TestUtils.createTgwMtMessage();
    String tgwMtStatusQueueName = "TGW-MT-STATUS";

    ArgumentCaptor<String> destinationNamingConventionString = ArgumentCaptor.forClass(String.class);

    Mockito.when(destinationNamingConvention.addQueuePrefix(TgwMtStatusJmsController.TGW_MT_STATUS)).thenReturn(tgwMtStatusQueueName);
    Mockito.when(tgwMtMessageConverter.apply(processableMtMessage, tgwMtStatusQueueName)).thenReturn(mtMessage);
    Mockito.doNothing().when(tgwMtMessagePublisher).publish(mtMessage);

    TgwMtMessageProcessor tgwMtMessageProcessor = new TgwMtMessageProcessor(destinationNamingConvention, tgwMtMessageConverter, tgwMtMessagePublisher);
    tgwMtMessageProcessor.process(processableMtMessage);

    Mockito.verify(tgwMtMessageConverter)
        .apply(ArgumentMatchers.eq(processableMtMessage), destinationNamingConventionString.capture());
    Assertions.assertEquals(tgwMtStatusQueueName, destinationNamingConventionString.getValue());
    Mockito.verify(tgwMtMessagePublisher).publish(ArgumentMatchers.any(MtMessage.class));
    Mockito.verifyNoMoreInteractions(tgwMtMessageConverter, tgwMtMessagePublisher);
  }
}
