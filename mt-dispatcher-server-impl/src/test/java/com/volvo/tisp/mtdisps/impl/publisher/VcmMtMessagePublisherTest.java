package com.volvo.tisp.mtdisps.impl.publisher;

import java.time.Clock;
import java.util.concurrent.CompletableFuture;

import jakarta.jms.JMSException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessagePostProcessor;

import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.integration.log.IntegrationLogEventPublisher;
import com.volvo.tisp.integration.log.v2.IntegrationLogEvent;
import com.volvo.tisp.mtdisps.impl.conf.AppProperties;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.reporter.MessagePublisherMetricReporter;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;
import com.volvo.tisp.vc.amtss.client.protobuf.MessageTypes;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VcmMtMessagePublisherTest {
  private static final String QUEUE_NAME = "queueDetails";

  @Test
  void publishFailureTest() {
    AppProperties appProperties = Mockito.mock(AppProperties.class);
    Clock clock = TestUtils.mockClock();
    IntegrationLogEventPublisher integrationLogEventPublisher = Mockito.mock(IntegrationLogEventPublisher.class);
    JmsTemplate jmsTemplate = Mockito.mock(JmsTemplate.class);
    MessagePublisherMetricReporter messagePublisherMetricReporter = Mockito.mock(MessagePublisherMetricReporter.class);

    AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage mtMessage = TestUtils.createAssetMtSchedulerMtMessage(true);

    Mockito.when(appProperties.isIntegrationLoggingEnabled()).thenReturn(true);
    Mockito.doThrow(new RuntimeException("simulated test")).when(jmsTemplate).convertAndSend(Mockito.eq(QUEUE_NAME), Mockito.eq(mtMessage), Mockito.any());

    VcmMtMessagePublisher vcmMtMessagePublisher = new VcmMtMessagePublisher(appProperties, clock, integrationLogEventPublisher, jmsTemplate,
        messagePublisherMetricReporter, QUEUE_NAME);
    CompletableFuture<Void> result = vcmMtMessagePublisher.publish(mtMessage);

    Assertions.assertTrue(result.isCompletedExceptionally());
    Mockito.verify(appProperties).isIntegrationLoggingEnabled();
    Mockito.verify(integrationLogEventPublisher).publish(ArgumentMatchers.any(IntegrationLogEvent.class));
    Mockito.verify(jmsTemplate).convertAndSend(Mockito.eq(QUEUE_NAME), Mockito.eq(mtMessage), Mockito.any());
    Mockito.verify(messagePublisherMetricReporter).onMessagePublishFailure(FlowType.VCM);
    Mockito.verifyNoMoreInteractions(appProperties, integrationLogEventPublisher, jmsTemplate, messagePublisherMetricReporter);
  }

  @Test
  void publishNullMessageTest() {
    AssertThrows.illegalArgumentException(
        () -> new VcmMtMessagePublisher(Mockito.mock(AppProperties.class), TestUtils.mockClock(), Mockito.mock(IntegrationLogEventPublisher.class),
            Mockito.mock(JmsTemplate.class), Mockito.mock(MessagePublisherMetricReporter.class), "queue-name").publish(null),
        "message must not be null");
  }

  @Test
  void publishTest() throws JMSException {
    AppProperties appProperties = Mockito.mock(AppProperties.class);
    Clock clock = TestUtils.mockClock();
    IntegrationLogEventPublisher integrationLogEventPublisher = Mockito.mock(IntegrationLogEventPublisher.class);
    JmsTemplate jmsTemplate = Mockito.mock(JmsTemplate.class);
    jakarta.jms.Message message = Mockito.mock(jakarta.jms.Message.class);
    MessagePublisherMetricReporter messagePublisherMetricReporter = Mockito.mock(MessagePublisherMetricReporter.class);

    Mockito.when(appProperties.isIntegrationLoggingEnabled()).thenReturn(true);

    AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage mtMessage = TestUtils.createAssetMtSchedulerMtMessage(true);

    VcmMtMessagePublisher vcmMtMessagePublisher = new VcmMtMessagePublisher(appProperties, clock, integrationLogEventPublisher, jmsTemplate,
        messagePublisherMetricReporter, QUEUE_NAME);
    CompletableFuture<Void> result = vcmMtMessagePublisher.publish(mtMessage);

    ArgumentCaptor<MessagePostProcessor> postProcessorCaptor = ArgumentCaptor.forClass(MessagePostProcessor.class);
    Mockito.verify(jmsTemplate, Mockito.timeout(1_000))
        .convertAndSend(ArgumentMatchers.eq(QUEUE_NAME), ArgumentMatchers.any(), postProcessorCaptor.capture());
    Assertions.assertInstanceOf(MessagePostProcessor.class, postProcessorCaptor.getValue());
    postProcessorCaptor.getValue().postProcessMessage(message);
    Mockito.verify(message).setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), MessageTypes.AMTSS_ASSET_MT_SCHEDULER_MT_MESSAGE);
    Mockito.verify(message).setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), MessageTypes.VERSION_1_0);

    Assertions.assertFalse(result.isCompletedExceptionally());
    Mockito.verify(appProperties).isIntegrationLoggingEnabled();
    Mockito.verify(integrationLogEventPublisher).publish(ArgumentMatchers.any(IntegrationLogEvent.class));
    Mockito.verify(messagePublisherMetricReporter).onMessagePublishDuration(Mockito.any(), Mockito.eq(FlowType.VCM));
    Mockito.verifyNoMoreInteractions(messagePublisherMetricReporter, jmsTemplate);
  }
}
