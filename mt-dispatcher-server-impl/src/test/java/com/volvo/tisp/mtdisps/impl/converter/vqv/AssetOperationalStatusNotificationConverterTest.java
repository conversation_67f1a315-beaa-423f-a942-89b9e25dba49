package com.volvo.tisp.mtdisps.impl.converter.vqv;

import java.math.BigInteger;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.va.v1.VehicleNonoperationalNotification;
import com.wirelesscar.va.v1.VehicleOperationalNotification;

class AssetOperationalStatusNotificationConverterTest {
  private static VehicleNonoperationalNotification createNonOperationalNotification(String vpi) {
    VehicleNonoperationalNotification vehicleNonoperationalNotification = new VehicleNonoperationalNotification();
    vehicleNonoperationalNotification.setVehiclePlatformId(vpi);
    vehicleNonoperationalNotification.setVehicleVersion(BigInteger.ONE);

    return vehicleNonoperationalNotification;
  }

  private static VehicleOperationalNotification createOperationalNotification(String vpi) {
    VehicleOperationalNotification vehicleOperationalNotification = new VehicleOperationalNotification();
    vehicleOperationalNotification.setVehiclePlatformId(vpi);
    vehicleOperationalNotification.setVehicleVersion(BigInteger.ONE);

    return vehicleOperationalNotification;
  }

  @Test
  void convertNonOperational_ConversionFailure() {
    AssetOperationalStatusNotificationConverter converter = new AssetOperationalStatusNotificationConverter();
    Optional<AssetOperationalStatusRecord> result = converter.convert(createNonOperationalNotification("VPI-INVALID"));

    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void convertNonOperational_ReturnsEmpty_WhenNullInput() {
    AssetOperationalStatusNotificationConverter converter = new AssetOperationalStatusNotificationConverter();
    Optional<AssetOperationalStatusRecord> result = converter.convert((VehicleNonoperationalNotification) null);

    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void convertNonOperational_Success() {
    AssetOperationalStatusNotificationConverter converter = new AssetOperationalStatusNotificationConverter();
    Optional<AssetOperationalStatusRecord> result = converter.convert(createNonOperationalNotification(TestUtils.VPI.toString()));

    Assertions.assertTrue(result.isPresent());
    Assertions.assertEquals(Vpi.ofString(TestUtils.VPI.toString()), result.get().vpi());
    Assertions.assertEquals(OperationalStatus.NON_OPERATIONAL, result.get().operationalStatus());
    Assertions.assertEquals(1, result.get().vehicleVersion().version());
  }

  @Test
  void convertOperational_ConversionFailure() {
    AssetOperationalStatusNotificationConverter converter = new AssetOperationalStatusNotificationConverter();
    Optional<AssetOperationalStatusRecord> result = converter.convert(createOperationalNotification("VPI-INVALID"));

    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void convertOperational_ReturnsEmpty_WhenNullInput() {
    AssetOperationalStatusNotificationConverter converter = new AssetOperationalStatusNotificationConverter();
    Optional<AssetOperationalStatusRecord> result = converter.convert((VehicleNonoperationalNotification) null);

    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void convertOperational_Success() {
    AssetOperationalStatusNotificationConverter converter = new AssetOperationalStatusNotificationConverter();
    Optional<AssetOperationalStatusRecord> result = converter.convert(createOperationalNotification(TestUtils.VPI.toString()));

    Assertions.assertTrue(result.isPresent());
    Assertions.assertEquals(Vpi.ofString(TestUtils.VPI.toString()), result.get().vpi());
    Assertions.assertEquals(OperationalStatus.OPERATIONAL, result.get().operationalStatus());
    Assertions.assertEquals(1, result.get().vehicleVersion().version());
  }
}
