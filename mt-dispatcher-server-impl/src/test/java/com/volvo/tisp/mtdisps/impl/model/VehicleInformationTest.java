package com.volvo.tisp.mtdisps.impl.model;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VehicleInformationTest {
  @Test
  void builderTest() {
    VehicleInformation.Builder builder = new VehicleInformation.Builder();
    builder.setChassisId(TestUtils.CHASSIS_ID);

    AssertThrows.illegalArgumentException(builder::build, "operationalStatus must not be null");

    builder.setOperationalStatus(OperationalStatus.OPERATIONAL);
    AssertThrows.illegalArgumentException(builder::build, "assetHardwareId must not be null");

    builder.setAssetHardwareId(TestUtils.ASSET_HARDWARE_ID);

    AssertThrows.illegalArgumentException(builder::build, "telematicUnitType must not be null");

    builder.setTelematicUnitType(TestUtils.TELEMATIC_UNIT_TYPE);
    AssertThrows.illegalArgumentException(builder::build, "version must not be null");

    builder.setVersion(TestUtils.VERSION);

    builder.setVpi(TestUtils.VPI);

    builder.setTelematicUnitSubType(null);
    Assertions.assertEquals(Optional.empty(), builder.build().telematicUnitSubType());
    builder.setTelematicUnitSubType(TestUtils.TELEMATIC_UNIT_SUB_TYPE);

    Assertions.assertEquals(TestUtils.createVehicleInformation(), builder.build());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals(
        "chassisId={Optional[SCAR-205045]}, operationalStatus={OPERATIONAL}, assetHardwareId={456-123}, softCar={false}, telematicUnitSubType={Optional[TGW3]}, telematicUnitType={TGW}, version={1}, vpi={Optional[1FAC35BF1EDB1DFF91AAC0BEBC56F22B]}",
        TestUtils.createVehicleInformationBuilder().build().toString());
  }
}
