package com.volvo.tisp.mtdisps.impl.converter.vqv;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.wirelesscar.vqv.v1.api.NotificationType;

class VqvNotificationConverterTest {
  private static final Path REMOVE_PATH = Path.of("src/test/resources/vqv/vqv-remove.json");
  private static final Path SYNC_NULL_SOFTCAR_PATH = Path.of("src/test/resources/vqv/vqv-update-null-scarprov.json");
  private static final Path UPDATE_NULL_STATUS_PATH = Path.of("src/test/resources/vqv/vqv-update-null-status.json");
  private static final Path UPDATE_PATH = Path.of("src/test/resources/vqv/vqv-update.json");
  private static final Path UPDATE_TGW_NULL_ASSETID_PATH = Path.of("src/test/resources/vqv/vqv-update-tgw-null-assetHardwareId.json");
  private static final Path UPDATE_VCM_NULL_ASSETID_PATH = Path.of("src/test/resources/vqv/vqv-update-vcm-null-assetHardwareId.json");

  private static String getInputString(Path path) throws IOException {
    return Files.readString(path, StandardCharsets.UTF_8);
  }

  private static void verifyVehicleInformation(VehicleInformation vehicleInformation, OperationalStatus operationalStatus, boolean isSoftcar) {
    Assertions.assertSame(operationalStatus, vehicleInformation.operationalStatus());
    Assertions.assertEquals(TestUtils.PART_NUMBER, vehicleInformation.assetHardwareId().partNumber());
    Assertions.assertEquals(TestUtils.SERIAL_NUMBER, vehicleInformation.assetHardwareId().serialNumber());
    Assertions.assertEquals(TestUtils.TELEMATIC_UNIT_TYPE, vehicleInformation.telematicUnitType());
    Assertions.assertEquals(TestUtils.TELEMATIC_UNIT_SUB_TYPE, vehicleInformation.telematicUnitSubType().get());
    Assertions.assertNotNull(vehicleInformation.version());
    Assertions.assertEquals(TestUtils.VPI, vehicleInformation.vpi().get());
    Assertions.assertEquals(isSoftcar, vehicleInformation.softCar());
  }

  private static void verifyVehicleInformation(VehicleInformation vehicleInformation) {
    verifyVehicleInformation(vehicleInformation, OperationalStatus.OPERATIONAL, true);
  }

  @Test
  void applyRemoveTest() throws IOException {
    VqvNotificationConverter vqvNotificationConverter = new VqvNotificationConverter();
    Optional<VqvNotification> optional = vqvNotificationConverter.apply(getInputString(REMOVE_PATH));

    VqvNotification vqvNotification = optional.orElseThrow();
    Assertions.assertEquals(TestUtils.INSTANT, vqvNotification.masterDataDeletedTimestamp());
    Assertions.assertEquals(TestUtils.VPI, vqvNotification.vpi());
    Assertions.assertSame(NotificationType.REMOVED, vqvNotification.notificationType());

    verifyVehicleInformation(vqvNotification.vehicleInformation());
  }

  @Test
  void applySyncWithNullSoftcarTest() throws IOException {
    VqvNotificationConverter vqvNotificationConverter = new VqvNotificationConverter();
    Optional<VqvNotification> optionalVqvNotification = vqvNotificationConverter.apply(getInputString(SYNC_NULL_SOFTCAR_PATH));

    VqvNotification vqvNotification = optionalVqvNotification.orElseThrow();
    Assertions.assertNull(vqvNotification.masterDataDeletedTimestamp());
    Assertions.assertEquals(TestUtils.VPI, vqvNotification.vpi());
    Assertions.assertSame(NotificationType.SYNC, vqvNotification.notificationType());

    verifyVehicleInformation(vqvNotification.vehicleInformation(), OperationalStatus.OPERATIONAL, false);
  }

  @Test
  void applyUpdateTest() throws IOException {
    VqvNotificationConverter vqvNotificationConverter = new VqvNotificationConverter();
    Optional<VqvNotification> optional = vqvNotificationConverter.apply(getInputString(UPDATE_PATH));

    VqvNotification vqvNotification = optional.orElseThrow();
    Assertions.assertNull(vqvNotification.masterDataDeletedTimestamp());
    Assertions.assertEquals(TestUtils.VPI, vqvNotification.vpi());
    Assertions.assertSame(NotificationType.UPDATED, vqvNotification.notificationType());

    verifyVehicleInformation(vqvNotification.vehicleInformation());
  }

  @Test
  void applyUpdateWithNullOperationStatusTest() throws IOException {
    VqvNotificationConverter vqvNotificationConverter = new VqvNotificationConverter();
    Optional<VqvNotification> optional = vqvNotificationConverter.apply(getInputString(UPDATE_NULL_STATUS_PATH));

    VqvNotification vqvNotification = optional.orElseThrow();
    Assertions.assertNull(vqvNotification.masterDataDeletedTimestamp());
    Assertions.assertEquals(TestUtils.VPI, vqvNotification.vpi());
    Assertions.assertSame(NotificationType.UPDATED, vqvNotification.notificationType());

    verifyVehicleInformation(vqvNotification.vehicleInformation(), OperationalStatus.NON_OPERATIONAL, true);
  }

  @Test
  void invalidStringTest() {
    VqvNotificationConverter vqvNotificationConverter = new VqvNotificationConverter();
    Optional<VqvNotification> optionalVqvNotification = vqvNotificationConverter.apply("invalid vqv string");

    Assertions.assertEquals(Optional.empty(), optionalVqvNotification);
  }
}
