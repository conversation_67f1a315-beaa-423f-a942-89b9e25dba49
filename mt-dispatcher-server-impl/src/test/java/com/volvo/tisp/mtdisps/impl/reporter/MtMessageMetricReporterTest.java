package com.volvo.tisp.mtdisps.impl.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.utils.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class MtMessageMetricReporterTest {
  @Test
  void onConversionFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      mtMessageMetricReporter.onMtMessageFailure(ValidationFailureReason.CONVERSION_FAILURE);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.message.failed", Tags.of("reason", ValidationFailureReason.CONVERSION_FAILURE.name()), 1);

      mtMessageMetricReporter.onMtMessageFailure(ValidationFailureReason.CONVERSION_FAILURE);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.message.failed", Tags.of("reason", ValidationFailureReason.CONVERSION_FAILURE.name()), 2);
    });
  }

  @Test
  void onProcessFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      mtMessageMetricReporter.onMtMessageFailure(ValidationFailureReason.PROCESS_FAILURE);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.message.failed", Tags.of("reason", ValidationFailureReason.PROCESS_FAILURE.name()), 1);

      mtMessageMetricReporter.onMtMessageFailure(ValidationFailureReason.PROCESS_FAILURE);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.message.failed", Tags.of("reason", ValidationFailureReason.PROCESS_FAILURE.name()), 2);
    });
  }
}