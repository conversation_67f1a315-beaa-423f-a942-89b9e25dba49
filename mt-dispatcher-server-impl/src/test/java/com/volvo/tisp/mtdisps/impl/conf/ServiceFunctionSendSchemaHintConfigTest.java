package com.volvo.tisp.mtdisps.impl.conf;

import java.util.Objects;
import java.util.Properties;

import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertySourceFactory;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;

@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = ServiceFunctionSendSchemaHintConfig.class)
@PropertySource(value = "classpath:application-test.yml", factory = ServiceFunctionSendSchemaHintConfigTest.YamlPropertySourceFactory.class)
class ServiceFunctionSendSchemaHintConfigTest {

  @Autowired
  private ServiceFunctionSendSchemaHintConfig serviceFunctionSendSchemaHintConfig;

  @Test
  void applicationYamlLoaderTest() {
    Assertions.assertEquals("sat-only", serviceFunctionSendSchemaHintConfig.getSendSchemaHint(new ServiceFunction("ping-sat-only")).orElseThrow());
    Assertions.assertEquals("sms-only", serviceFunctionSendSchemaHintConfig.getSendSchemaHint(new ServiceFunction("ping-sms-only")).orElseThrow());
    Assertions.assertEquals("darf-config", serviceFunctionSendSchemaHintConfig.getSendSchemaHint(new ServiceFunction("legacy-darf-config")).orElseThrow());
  }

  public static class YamlPropertySourceFactory implements PropertySourceFactory {
    @Override
    public org.springframework.core.env.@NotNull PropertySource<?> createPropertySource(String name, EncodedResource encodedResource) {
      YamlPropertiesFactoryBean factory = new YamlPropertiesFactoryBean();
      factory.setResources(encodedResource.getResource());

      Properties properties = factory.getObject();

      return new PropertiesPropertySource(Objects.requireNonNull(encodedResource.getResource().getFilename()), Objects.requireNonNull(properties));
    }
  }
}
