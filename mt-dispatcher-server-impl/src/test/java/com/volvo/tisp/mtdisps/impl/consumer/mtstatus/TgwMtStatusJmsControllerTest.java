package com.volvo.tisp.mtdisps.impl.consumer.mtstatus;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.mtdisps.impl.converter.mtstatus.ReceivedTgwMtStatusMessageConverter;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtStatus;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.MtStatusMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.MtStatusDomainManager;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.wirelesscar.tce.api.v2.MtStatusMessage;

class TgwMtStatusJmsControllerTest {
  private static JmsMessage<MtStatusMessage> mockJmsMessage(MtStatusMessage mtStatusMessage) {
    JmsMessage<MtStatusMessage> jmsMessage = Mockito.mock(JmsMessage.class);
    Mockito.when(jmsMessage.payload()).thenReturn(mtStatusMessage);
    return jmsMessage;
  }

  @Test
  void receiveMtMessageConversionFailureTest() {
    MtStatusDomainManager mtStatusDomainManager = Mockito.mock(MtStatusDomainManager.class);
    MtStatusMetricReporter mtStatusMetricReporter = Mockito.mock(MtStatusMetricReporter.class);
    ReceivedTgwMtStatusMessageConverter receivedTgwMtStatusMessageConverter = Mockito.mock(ReceivedTgwMtStatusMessageConverter.class);

    MtStatusMessage mtStatusMessage = TestUtils.createMtStatusMessage();
    JmsMessage<MtStatusMessage> jmsMessage = mockJmsMessage(mtStatusMessage);

    Mockito.when(receivedTgwMtStatusMessageConverter.apply(mtStatusMessage)).thenReturn(Optional.empty());

    TgwMtStatusJmsController tgwMtStatusJmsController = new TgwMtStatusJmsController(mtStatusDomainManager, mtStatusMetricReporter,
        receivedTgwMtStatusMessageConverter);
    tgwMtStatusJmsController.receiveMtMessage(jmsMessage);

    Mockito.verify(receivedTgwMtStatusMessageConverter).apply(mtStatusMessage);
    Mockito.verify(mtStatusMetricReporter).onMtStatusFailure(ValidationFailureReason.CONVERSION_FAILURE, FlowType.TGW);
    Mockito.verify(mtStatusMetricReporter).onMtStatusReceived(FlowType.TGW);
    Mockito.verifyNoInteractions(mtStatusDomainManager);
    Mockito.verifyNoMoreInteractions(mtStatusMetricReporter, receivedTgwMtStatusMessageConverter);
  }

  @Test
  void receiveMtMessageTest() {
    MtStatusDomainManager mtStatusDomainManager = Mockito.mock(MtStatusDomainManager.class);
    MtStatusMetricReporter mtStatusMetricReporter = Mockito.mock(MtStatusMetricReporter.class);
    ReceivedTgwMtStatusMessageConverter receivedTgwMtStatusMessageConverter = Mockito.mock(ReceivedTgwMtStatusMessageConverter.class);

    MtStatusMessage mtStatusMessage = TestUtils.createMtStatusMessage();
    ReceivedMtStatus receivedMtStatus = TestUtils.createReceivedMtStatus();
    JmsMessage<MtStatusMessage> jmsMessage = mockJmsMessage(mtStatusMessage);

    Mockito.when(receivedTgwMtStatusMessageConverter.apply(mtStatusMessage)).thenReturn(Optional.of(receivedMtStatus));
    Mockito.when(mtStatusDomainManager.processMessage(receivedMtStatus, FlowType.TGW)).thenReturn(CompletableFuture.completedFuture(null));

    TgwMtStatusJmsController tgwMtStatusJmsController = new TgwMtStatusJmsController(mtStatusDomainManager, mtStatusMetricReporter,
        receivedTgwMtStatusMessageConverter);
    tgwMtStatusJmsController.receiveMtMessage(jmsMessage);

    Mockito.verify(receivedTgwMtStatusMessageConverter).apply(mtStatusMessage);
    Mockito.verify(mtStatusDomainManager).processMessage(receivedMtStatus, FlowType.TGW);
    Mockito.verify(mtStatusMetricReporter).onMtStatusReceived(FlowType.TGW);
    Mockito.verifyNoMoreInteractions(mtStatusDomainManager, receivedTgwMtStatusMessageConverter, mtStatusMetricReporter);
  }
}
