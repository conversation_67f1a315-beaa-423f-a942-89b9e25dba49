package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Base64;
import java.util.Optional;
import java.util.StringJoiner;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.conf.ServiceFunctionSendSchemaHintConfig;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.model.AcceptedCost;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.Priority;
import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ServiceVersion;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.api.v2.SrpLevel;
import com.wirelesscar.tce.api.v2.SrpOption;

class TgwMtMessageConverterTest {
  public static MtMessage createTgwMtMessage(ServiceId serviceId, ServiceVersion serviceVersion, String enqueuingType, SrpLevel srpLevel) {
    MtMessage mtMessage = new MtMessage();
    mtMessage.setClientId(serviceId + "-" + serviceVersion);
    mtMessage.setVehiclePlatformId(TestUtils.VPI.toString());
    mtMessage.setPayload(Base64.getDecoder().decode(TestUtils.PAYLOAD));
    mtMessage.setSchedulerOption(createSchedulerOption(serviceId, serviceVersion, enqueuingType));
    mtMessage.setSrpOption(createSrpOption(serviceId, serviceVersion, srpLevel));
    mtMessage.setMtStatusReplyOption(TestUtils.createMtStatusReplyOption());
    return mtMessage;
  }

  private static ProcessableMtMessage createProcessableMtMessage(ServiceId serviceId, ServiceVersion serviceVersion, boolean isOverride, Priority priority) {
    return new ProcessableMtMessage(
        VehicleInformation.fromEntity(TestUtils.createVehicleInformationEntity(true)),
        AcceptedCost.valueOf(TestUtils.ACCEPTED_COST.name()),
        Optional.of(TestUtils.CORRELATION_ID),
        TestUtils.SERVICE_FUNCTION,
        isOverride,
        ImmutableByteArray.of(Base64.getDecoder().decode(TestUtils.PAYLOAD)),
        Optional.empty(),
        Optional.of(priority),
        TestUtils.SERVICE_ACCESS_TOKEN,
        new ServiceId(serviceId.serviceId()),
        serviceVersion,
        TestUtils.TRACKING_ID,
        TestUtils.TTL,
        MtMessageType.INTERNAL_MT_MESSAGE);
  }

  private static SchedulerOption createSchedulerOption(ServiceId serviceId, ServiceVersion serviceVersion, String enqueueingType) {
    SchedulerOption schedulerOption = new SchedulerOption();
    schedulerOption.setEnqueueingType(enqueueingType);
    schedulerOption.setHint(new StringJoiner("-").add(TestUtils.TTL.name()).add(TestUtils.PRIORITY.name()).toString());
    schedulerOption.setPriority(4);
    schedulerOption.setQueueId(new StringJoiner("-").add(serviceId + "-" + serviceVersion).add(TestUtils.SERVICE_FUNCTION.value()).toString());
    return schedulerOption;
  }

  private static SrpOption createSrpOption(ServiceId serviceId, ServiceVersion serviceVersion, SrpLevel srpLevel) {
    SrpOption srpOption = new SrpOption();
    srpOption.setDstService(serviceId.serviceId());
    srpOption.setDstVersion(serviceVersion.serviceVersion());
    srpOption.setPriority(4);
    srpOption.setSrpLevel(srpLevel);
    return srpOption;
  }

  private static ServiceFunctionSendSchemaHintConfig mockServiceFunctionSendSchemaHintConfig(Optional<String> legacyHint) {
    ServiceFunctionSendSchemaHintConfig mock = Mockito.mock(ServiceFunctionSendSchemaHintConfig.class);
    Mockito.when(mock.getSendSchemaHint(ArgumentMatchers.any(ServiceFunction.class))).thenReturn(legacyHint);
    return mock;
  }

  private static void verifyMtMessage(MtMessage expected, MtMessage actual) {
    Assertions.assertEquals(expected.getClientId(), actual.getClientId());
    Assertions.assertEquals(expected.getVehiclePlatformId(), actual.getVehiclePlatformId());
    Assertions.assertArrayEquals(expected.getPayload(), actual.getPayload());
    verifySchedulerOption(expected.getSchedulerOption(), actual.getSchedulerOption());
    verifySrpOption(expected.getSrpOption(), actual.getSrpOption());
    verifyMtStatusReplyOption(expected.getMtStatusReplyOption(), actual.getMtStatusReplyOption());
  }

  private static void verifyMtStatusReplyOption(MtStatusReplyOption expected, MtStatusReplyOption actual) {
    Assertions.assertEquals(expected.getReplyDestination(), actual.getReplyDestination());
    Assertions.assertEquals(expected.getCorrelationId(), actual.getCorrelationId());
  }

  private static void verifySchedulerOption(SchedulerOption expected, SchedulerOption actual) {
    Assertions.assertEquals(expected.getQueueId(), actual.getQueueId());
    Assertions.assertEquals(expected.getHint(), actual.getHint());
    Assertions.assertEquals(expected.getPriority(), actual.getPriority());
    Assertions.assertEquals(expected.getEnqueueingType(), actual.getEnqueueingType());
  }

  private static void verifySrpOption(SrpOption expected, SrpOption actual) {
    Assertions.assertEquals(expected.getPriority(), actual.getPriority());
    Assertions.assertEquals(expected.getDstService(), actual.getDstService());
    Assertions.assertEquals(expected.getDstVersion(), actual.getDstVersion());
    Assertions.assertEquals(expected.getSrpLevel(), actual.getSrpLevel());
  }

  @Test
  void applyInvalidTest() {
    ServiceFunctionSendSchemaHintConfig serviceFunctionSendSchemaHintConfig = mockServiceFunctionSendSchemaHintConfig(Optional.empty());
    TgwMtMessageConverter tgwMtMessageConverter = new TgwMtMessageConverter(serviceFunctionSendSchemaHintConfig);

    AssertThrows.illegalArgumentException(() -> tgwMtMessageConverter.apply(null, "replyToQueueName"),
        "processableMtMessage must not be null");
    AssertThrows.illegalArgumentException(
        () -> tgwMtMessageConverter.apply(TestUtils.createProcessableMtMessage(TrackingIdentifier.create()), ""),
        "tgwMtStatusQueueName must not be empty");
  }

  @Test
  void encryptedActivationMessageApplyTest() {
    ServiceFunctionSendSchemaHintConfig serviceFunctionSendSchemaHintConfig = mockServiceFunctionSendSchemaHintConfig(Optional.empty());
    ServiceId serviceId = new ServiceId(1);
    ServiceVersion serviceVersion = new ServiceVersion(2);

    TgwMtMessageConverter tgwMtMessageConverter = new TgwMtMessageConverter(serviceFunctionSendSchemaHintConfig);
    MtMessage mtMessage = tgwMtMessageConverter.apply(createProcessableMtMessage(serviceId, serviceVersion, true, Priority.HIGH), TestUtils.REPLY_TO.toString());

    MtMessage expectedMtMessage = createTgwMtMessage(serviceId, serviceVersion, "OVERRIDE_IMMEDIATE", SrpLevel.SRP_12);
    verifyMtMessage(expectedMtMessage, mtMessage);
  }

  @Test
  void legacySendSchemaHintTest() {
    ServiceFunctionSendSchemaHintConfig serviceFunctionSendSchemaHintConfig = mockServiceFunctionSendSchemaHintConfig(Optional.of("some-legacy-schema"));

    TgwMtMessageConverter tgwMtMessageConverter = new TgwMtMessageConverter(serviceFunctionSendSchemaHintConfig);
    MtMessage mtMessage = tgwMtMessageConverter.apply(TestUtils.createProcessableMtMessage(TestUtils.TRACKING_ID), TestUtils.REPLY_TO.toString());

    Assertions.assertEquals("some-legacy-schema", mtMessage.getSchedulerOption().getHint());
  }

  @Test
  void normalMessageApplyTest() {
    ServiceFunctionSendSchemaHintConfig serviceFunctionSendSchemaHintConfig = mockServiceFunctionSendSchemaHintConfig(Optional.empty());
    ServiceId serviceId = new ServiceId(42);
    ServiceVersion serviceVersion = new ServiceVersion(33);

    TgwMtMessageConverter tgwMtMessageConverter = new TgwMtMessageConverter(serviceFunctionSendSchemaHintConfig);
    MtMessage mtMessage = tgwMtMessageConverter.apply(createProcessableMtMessage(serviceId, serviceVersion, false, Priority.MID), TestUtils.REPLY_TO.toString());

    MtMessage expectedMtMessage = createTgwMtMessage(serviceId, serviceVersion, "NORMAL", SrpLevel.SRP_11);
    verifyMtMessage(expectedMtMessage, mtMessage);
  }

  @Test
  void overrideMessageApplyTest() {
    ServiceFunctionSendSchemaHintConfig serviceFunctionSendSchemaHintConfig = mockServiceFunctionSendSchemaHintConfig(Optional.empty());
    ServiceId serviceId = new ServiceId(42);
    ServiceVersion serviceVersion = new ServiceVersion(33);

    TgwMtMessageConverter tgwMtMessageConverter = new TgwMtMessageConverter(serviceFunctionSendSchemaHintConfig);
    MtMessage mtMessage = tgwMtMessageConverter.apply(createProcessableMtMessage(serviceId, serviceVersion, true, Priority.LOW), TestUtils.REPLY_TO.toString());

    MtMessage expectedMtMessage = createTgwMtMessage(serviceId, serviceVersion, "OVERRIDE", SrpLevel.SRP_11);
    verifyMtMessage(expectedMtMessage, mtMessage);
  }

  @Test
  void unEncryptedActivationMessageApplyTest() {
    ServiceFunctionSendSchemaHintConfig serviceFunctionSendSchemaHintConfig = mockServiceFunctionSendSchemaHintConfig(Optional.empty());
    ServiceId serviceId = new ServiceId(1);
    ServiceVersion serviceVersion = new ServiceVersion(1);

    TgwMtMessageConverter tgwMtMessageConverter = new TgwMtMessageConverter(serviceFunctionSendSchemaHintConfig);
    MtMessage mtMessage = tgwMtMessageConverter.apply(createProcessableMtMessage(serviceId, serviceVersion, true, Priority.HIGH), TestUtils.REPLY_TO.toString());

    MtMessage expectedMtMessage = createTgwMtMessage(serviceId, serviceVersion, "OVERRIDE_IMMEDIATE", SrpLevel.SRP_11);
    verifyMtMessage(expectedMtMessage, mtMessage);
  }
}