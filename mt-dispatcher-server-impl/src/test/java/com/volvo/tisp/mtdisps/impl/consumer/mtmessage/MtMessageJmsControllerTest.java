package com.volvo.tisp.mtdisps.impl.consumer.mtmessage;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.InternalReceivedMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.converter.mtmessage.ProcessableMtMessageConverter;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtMessage;
import com.volvo.tisp.mtdisps.impl.dto.Status;
import com.volvo.tisp.mtdisps.impl.model.InterfaceType;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.MtMessageMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.IntegrationLogger;
import com.volvo.tisp.mtdisps.impl.service.MtMessageDomainManager;
import com.volvo.tisp.mtdisps.impl.service.ServiceAccessTokenValidator;
import com.volvo.tisp.mtdisps.impl.service.ServiceWhitelistValidator;
import com.volvo.tisp.mtdisps.impl.service.ValidationException;
import com.volvo.tisp.mtdisps.impl.service.processor.MtStatusProcessor;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;

class MtMessageJmsControllerTest {
  private static JmsMessage<MtMessage> mockJmsMessage(MtMessage mtMessage) {
    JmsMessage<MtMessage> jmsMessage = Mockito.mock(JmsMessage.class);
    Mockito.when(jmsMessage.payload()).thenReturn(mtMessage);
    return jmsMessage;
  }

  private static void verifyMtStatus(ProcessableMtStatus processableMtStatus, ProcessableMtMessage processableMtMessage) {
    Assertions.assertEquals(Status.INVALID_TOKEN, processableMtStatus.status());
    Assertions.assertEquals(processableMtMessage.correlationId().get(), processableMtStatus.correlationId());
    Assertions.assertEquals(processableMtMessage.trackingIdentifier(), processableMtStatus.trackingIdentifier());
    Assertions.assertEquals(processableMtMessage.serviceId(), processableMtStatus.serviceId());
    Assertions.assertEquals(processableMtMessage.serviceVersion(), processableMtStatus.serviceVersion());
  }

  @Test
  void receiveMtMessageInvalidServiceAccessTokenTest() throws ValidationException {
    MtMessageDomainManager mtMessageDomainManager = Mockito.mock(MtMessageDomainManager.class);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    ServiceAccessTokenValidator serviceAccessTokenValidator = Mockito.mock(ServiceAccessTokenValidator.class);
    InternalReceivedMtMessageConverter receivedMtMessageConverter = Mockito.mock(InternalReceivedMtMessageConverter.class);
    ProcessableMtMessageConverter processableMtMessageConverter = Mockito.mock(ProcessableMtMessageConverter.class);
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    MtStatusProcessor mtStatusProcessor = Mockito.mock(MtStatusProcessor.class);
    ServiceWhitelistValidator serviceWhitelistValidator = new ServiceWhitelistValidator(List.of(0));

    MtMessageJmsController mtMessageJmsController = new MtMessageJmsController(mtMessageDomainManager, mtMessageMetricReporter, serviceAccessTokenValidator,
        receivedMtMessageConverter, processableMtMessageConverter, integrationLogger, mtStatusProcessor, serviceWhitelistValidator);

    MtMessage mtMessage = TestUtils.createMtMessage();
    JmsMessage<MtMessage> jmsMessage = mockJmsMessage(mtMessage);
    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage(TrackingIdentifier.fromString(mtMessage.getTrackingId()),
        TelematicUnitType.VCM);
    ReceivedMtMessage receivedMtMessage = TestUtils.createReceivedMtMessage(TrackingIdentifier.fromString(mtMessage.getTrackingId()));
    ArgumentCaptor<ProcessableMtStatus> processableMtStatusArgumentCaptor = ArgumentCaptor.forClass(ProcessableMtStatus.class);
    Mockito.when(
        receivedMtMessageConverter.convert(ArgumentMatchers.any(MtMessage.class))).thenReturn(Optional.of(receivedMtMessage));
    Mockito.when(processableMtMessageConverter.convert(receivedMtMessage)).thenReturn(processableMtMessage);

    Mockito.when(mtStatusProcessor.process(Mockito.any(ProcessableMtStatus.class))).thenReturn(CompletableFuture.completedFuture(null));
    Mockito.doThrow(new ValidationException(ValidationFailureReason.AUTHENTICATION_FAILURE))
        .when(serviceAccessTokenValidator)
        .validateServiceAccessToken(processableMtMessage);

    CompletableFuture<Void> completableFuture = mtMessageJmsController.receiveMtMessage(jmsMessage);

    Assertions.assertTrue(completableFuture.isDone());

    Mockito.verify(serviceAccessTokenValidator).validateServiceAccessToken(processableMtMessage);
    Mockito.verify(mtMessageMetricReporter).onMtMessageFailure(ValidationFailureReason.AUTHENTICATION_FAILURE);
    Mockito.verify(mtMessageMetricReporter).onMtMessageReceived(InterfaceType.INTERNAL_JMS);
    Mockito.verify(mtStatusProcessor).process(processableMtStatusArgumentCaptor.capture());
    Mockito.verify(receivedMtMessageConverter).convert(ArgumentMatchers.any(MtMessage.class));
    Mockito.verify(integrationLogger).log(processableMtMessage, "MtMessageReceived");
    Mockito.verify(integrationLogger)
        .log(Mockito.any(ProcessableMtStatus.class), Mockito.eq("MtMessageProcessingFailed_" + ValidationFailureReason.AUTHENTICATION_FAILURE));
    verifyMtStatus(processableMtStatusArgumentCaptor.getValue(), processableMtMessage);
    Mockito.verifyNoInteractions(mtMessageDomainManager);
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, serviceAccessTokenValidator, mtStatusProcessor, integrationLogger);
  }

  @Test
  void receiveMtMessageTest() throws ValidationException {
    MtMessageDomainManager mtMessageDomainManager = Mockito.mock(MtMessageDomainManager.class);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    ServiceAccessTokenValidator serviceAccessTokenValidator = Mockito.mock(ServiceAccessTokenValidator.class);
    InternalReceivedMtMessageConverter receivedMtMessageConverter = Mockito.mock(InternalReceivedMtMessageConverter.class);
    ProcessableMtMessageConverter processableMtMessageConverter = Mockito.mock(ProcessableMtMessageConverter.class);
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    MtStatusProcessor mtStatusProcessor = Mockito.mock(MtStatusProcessor.class);
    ServiceWhitelistValidator serviceWhitelistValidator = new ServiceWhitelistValidator(List.of(0));

    MtMessageJmsController mtMessageJmsController = new MtMessageJmsController(mtMessageDomainManager, mtMessageMetricReporter, serviceAccessTokenValidator,
        receivedMtMessageConverter, processableMtMessageConverter, integrationLogger, mtStatusProcessor, serviceWhitelistValidator);

    MtMessage mtMessage = TestUtils.createMtMessage();
    JmsMessage<MtMessage> jmsMessage = mockJmsMessage(mtMessage);
    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage(TrackingIdentifier.fromString(mtMessage.getTrackingId()),
        TelematicUnitType.VCM);
    ReceivedMtMessage receivedMtMessage = TestUtils.createReceivedMtMessage(TrackingIdentifier.fromString(mtMessage.getTrackingId()));
    Mockito.when(
        receivedMtMessageConverter.convert(ArgumentMatchers.any(MtMessage.class))).thenReturn(Optional.of(receivedMtMessage));
    Mockito.when(processableMtMessageConverter.convert(receivedMtMessage)).thenReturn(processableMtMessage);

    Mockito.when(mtMessageDomainManager.processMessage(processableMtMessage)).thenReturn(CompletableFuture.completedFuture(null));
    Mockito.doNothing()
        .when(serviceAccessTokenValidator)
        .validateServiceAccessToken(processableMtMessage);

    CompletableFuture<Void> completableFuture = mtMessageJmsController.receiveMtMessage(jmsMessage);

    Assertions.assertTrue(completableFuture.isDone());

    Mockito.verify(mtMessageDomainManager).processMessage(processableMtMessage);
    Mockito.verify(mtMessageMetricReporter).onMtMessageReceived(InterfaceType.INTERNAL_JMS);
    Mockito.verify(integrationLogger).log(processableMtMessage, "MtMessageReceived");
    Mockito.verifyNoInteractions(mtStatusProcessor);
    Mockito.verifyNoMoreInteractions(mtMessageDomainManager, integrationLogger, mtMessageMetricReporter);
  }
}
