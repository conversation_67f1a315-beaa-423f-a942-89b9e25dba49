package com.volvo.tisp.mtdisps.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.mongodb.assertions.Assertions;
import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;
import com.volvo.tisp.mtdisps.impl.conf.AppProperties;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtStatus;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ServiceVersion;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.reporter.Crud;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.mtdisps.impl.reporter.MtStatusMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.processor.MtStatusProcessor;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtStatusDomainManagerTest {
  private static final Duration TTL_MARGIN = Duration.ofSeconds(10);

  @Test
  void processMessageCorrelationIdNotFoundTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MtStatusMetricReporter mtStatusMetricReporter = Mockito.mock(MtStatusMetricReporter.class);
    MessageTransactionParametersRepository messageTransactionParametersRepository = Mockito.mock(MessageTransactionParametersRepository.class);
    MtStatusProcessor mtStatusProcessor = Mockito.mock(MtStatusProcessor.class);
    AppProperties appProperties = Mockito.mock(AppProperties.class);
    Mockito.when(appProperties.getTtlMargin()).thenReturn(TTL_MARGIN);

    MtStatusDomainManager mtStatusDomainManager = new MtStatusDomainManager(appProperties, clock, dbOperationsMetricReporter, mtStatusMetricReporter,
        messageTransactionParametersRepository, mtStatusProcessor);
    ReceivedMtStatus receivedMtStatus = TestUtils.createReceivedMtStatus();
    FlowType flowType = FlowType.VCM;

    Mockito.when(messageTransactionParametersRepository.findById(receivedMtStatus.correlationId().toString())).thenReturn(Optional.empty());

    CompletableFuture<Void> future = mtStatusDomainManager.processMessage(receivedMtStatus, flowType);

    Assertions.assertTrue(future.isDone());

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(messageTransactionParametersRepository).findById(receivedMtStatus.correlationId().toString());
    Mockito.verify(dbOperationsMetricReporter).onMessageTransactionCrudDuration(Duration.ofSeconds(2), Crud.READ);
    Mockito.verify(mtStatusMetricReporter).onMtStatusFailure(ValidationFailureReason.CORRELATION_ID_NOT_FOUND, flowType);
    Mockito.verifyNoInteractions(appProperties, mtStatusProcessor);
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, messageTransactionParametersRepository, mtStatusMetricReporter, mtStatusProcessor);
  }

  @Test
  void processMessageInvalidTest() {
    AssertThrows.illegalArgumentException(
        () -> new MtStatusDomainManager(Mockito.mock(AppProperties.class), Mockito.mock(Clock.class), Mockito.mock(DbOperationsMetricReporter.class),
            Mockito.mock(MtStatusMetricReporter.class), Mockito.mock(MessageTransactionParametersRepository.class),
            Mockito.mock(MtStatusProcessor.class)).processMessage(null, FlowType.VCM),
        "receivedMtStatus must not be null");

    AssertThrows.illegalArgumentException(
        () -> new MtStatusDomainManager(Mockito.mock(AppProperties.class), Mockito.mock(Clock.class), Mockito.mock(DbOperationsMetricReporter.class),
            Mockito.mock(MtStatusMetricReporter.class), Mockito.mock(MessageTransactionParametersRepository.class),
            Mockito.mock(MtStatusProcessor.class)).processMessage(
            TestUtils.createReceivedMtStatus(), null), "flowType must not be null");
  }

  @Test
  void processMessageTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MtStatusMetricReporter mtStatusMetricReporter = Mockito.mock(MtStatusMetricReporter.class);
    MessageTransactionParametersRepository messageTransactionParametersRepository = Mockito.mock(MessageTransactionParametersRepository.class);
    MtStatusProcessor mtStatusProcessor = Mockito.mock(MtStatusProcessor.class);
    AppProperties appProperties = Mockito.mock(AppProperties.class);
    Mockito.when(appProperties.getTtlMargin()).thenReturn(TTL_MARGIN);

    MtStatusDomainManager mtStatusDomainManager = new MtStatusDomainManager(appProperties, clock, dbOperationsMetricReporter, mtStatusMetricReporter,
        messageTransactionParametersRepository, mtStatusProcessor);
    ReceivedMtStatus receivedMtStatus = TestUtils.createReceivedMtStatus();
    MessageTransactionParametersEntity messageTransactionParameters = TestUtils.createMessageTransactionParameters();

    Mockito.when(messageTransactionParametersRepository.findById(receivedMtStatus.correlationId().toString()))
        .thenReturn(Optional.of(messageTransactionParameters));
    ProcessableMtStatus processableMtStatus = new ProcessableMtStatus(receivedMtStatus.correlationId(), receivedMtStatus.description(),
        receivedMtStatus.status(), receivedMtStatus.trackingIdentifier(), new ServiceFunction(messageTransactionParameters.getServiceFunction()),
        new ServiceId(messageTransactionParameters.getServiceId()), new ServiceVersion(
        messageTransactionParameters.getServiceVersion()), Optional.of(messageTransactionParameters.getAssetHardwareId()), Optional.of(TestUtils.VPI), MtMessageType.INTERNAL_MT_MESSAGE);
    Mockito.when(mtStatusProcessor.process(processableMtStatus))
        .thenReturn(CompletableFuture.completedFuture(null));

    mtStatusDomainManager.processMessage(receivedMtStatus, FlowType.TGW);

    Mockito.verify(messageTransactionParametersRepository).findById(receivedMtStatus.correlationId().toString());
    Mockito.verify(mtStatusMetricReporter).onMtStatusReceiveLatency(Mockito.any(Duration.class));
    Mockito.verify(messageTransactionParametersRepository).save(ArgumentMatchers.any(MessageTransactionParametersEntity.class));
    Mockito.verify(clock, Mockito.times(5)).instant();
    Mockito.verify(dbOperationsMetricReporter).onMessageTransactionCrudDuration(Duration.ofSeconds(2), Crud.READ);
    Mockito.verify(dbOperationsMetricReporter).onMessageTransactionCrudDuration(Duration.ofSeconds(0), Crud.UPDATE);
    Mockito.verify(mtStatusProcessor).process(processableMtStatus);
    Mockito.verify(appProperties).getTtlMargin();
    Mockito.verifyNoMoreInteractions(appProperties, clock, dbOperationsMetricReporter, messageTransactionParametersRepository, mtStatusMetricReporter,
        mtStatusProcessor);
  }
}
