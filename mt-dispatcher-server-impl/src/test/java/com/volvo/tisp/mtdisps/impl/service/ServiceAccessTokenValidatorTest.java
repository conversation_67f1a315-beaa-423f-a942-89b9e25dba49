package com.volvo.tisp.mtdisps.impl.service;

import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Clock;
import java.time.Instant;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.Optional;
import java.util.Set;

import org.bouncycastle.util.io.pem.PemReader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.KeySourceException;
import com.nimbusds.jose.crypto.ECDSASigner;
import com.nimbusds.jose.crypto.factories.DefaultJWSVerifierFactory;
import com.nimbusds.jose.jwk.Curve;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.jwk.source.JWKSourceBuilder;
import com.nimbusds.jose.proc.JWSAlgorithmFamilyJWSKeySelector;
import com.nimbusds.jose.proc.SecurityContext;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.PlainJWT;
import com.nimbusds.jwt.SignedJWT;
import com.nimbusds.jwt.proc.ConfigurableJWTProcessor;
import com.nimbusds.jwt.proc.DefaultJWTClaimsVerifier;
import com.nimbusds.jwt.proc.DefaultJWTProcessor;
import com.nimbusds.jwt.proc.JWTClaimsSetVerifier;
import com.nimbusds.jwt.proc.JWTProcessor;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.jwt.Idpm2mScopeVerifier;
import com.volvo.tisp.mtdisps.impl.jwt.ServiceSecurityContext;
import com.volvo.tisp.mtdisps.impl.model.AcceptedCost;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.Priority;
import com.volvo.tisp.mtdisps.impl.model.ServiceAccessToken;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ServiceAccessTokenValidatorTest {
  public static final String DEFAULT_V0_SCOPE = "msg.read msg.write";
  public static final String DEFAULT_V1_SCOPE = "mh.w svc.1";

  private static String createExpiredJwt(String scope) throws NoSuchAlgorithmException, IOException, InvalidKeySpecException, JOSEException {
    SignedJWT signedJWT = new SignedJWT(new JWSHeader.Builder(JWSAlgorithm.ES256).type(JOSEObjectType.JWT).build(),
        new JWTClaimsSet.Builder().issueTime(Date.from(Instant.now()))
            .expirationTime(Date.from(Instant.now().minusSeconds(100)))
            .claim("exp_mh", Date.from(Instant.now().minusSeconds(100)))
            .notBeforeTime(Date.from(Instant.now()))
            .claim("scope", scope)
            .build());
    signedJWT.sign(createJwsSigner());
    return signedJWT.serialize();
  }

  private static JWSSigner createJwsSigner() throws NoSuchAlgorithmException, IOException, InvalidKeySpecException, JOSEException {
    KeyFactory keyFactory = KeyFactory.getInstance("EC");
    PrivateKey tokenIssuerPrivateKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(
        new PemReader(new InputStreamReader(new ClassPathResource("tokenIssuerPrivateKey.pem").getInputStream())).readPemObject().getContent()));

    return new ECDSASigner(tokenIssuerPrivateKey, Curve.P_256);
  }

  private static <C extends SecurityContext> JWTProcessor<C> createJwtProcessor(JWTClaimsSetVerifier<C> claimsVerifier) throws IOException, KeySourceException {
    ConfigurableJWTProcessor<C> jwtProcessor = new DefaultJWTProcessor<>();
    jwtProcessor.setJWSVerifierFactory(new DefaultJWSVerifierFactory());
    JWKSource<SecurityContext> jwkSource = JWKSourceBuilder.create(new File("src/test/resources/jwks.json").toURI().toURL()).cache(true).retrying(true).build();
    jwtProcessor.setJWSKeySelector(JWSAlgorithmFamilyJWSKeySelector.fromJWKSource((JWKSource<C>) jwkSource));
    jwtProcessor.setJWSVerifierFactory(new DefaultJWSVerifierFactory());
    jwtProcessor.setJWTClaimsSetVerifier(claimsVerifier);
    return jwtProcessor;
  }

  private static DefaultJWTClaimsVerifier<ServiceSecurityContext> createLegacyClaimsVerifier() {
    JWTClaimsSet claimSet = new JWTClaimsSet.Builder().claim("scope", DEFAULT_V0_SCOPE).build();
    return new DefaultJWTClaimsVerifier<>(null, claimSet, Set.of("scope"), Collections.emptySet());
  }

  private static ProcessableMtMessage createProcessableMtMessage(String serviceAccessTokenString, ServiceId serviceId) {
    return new ProcessableMtMessage(
        TestUtils.createVehicleInformation(),
        AcceptedCost.valueOf(TestUtils.ACCEPTED_COST.name()),
        Optional.of(TestUtils.CORRELATION_ID),
        TestUtils.SERVICE_FUNCTION,
        false,
        ImmutableByteArray.of(Base64.getDecoder().decode(TestUtils.PAYLOAD)),
        Optional.empty(),
        Optional.of(Priority.valueOf(TestUtils.PRIORITY.name())),
        new ServiceAccessToken(serviceAccessTokenString),
        serviceId,
        TestUtils.SERVICE_VERSION,
        TestUtils.TRACKING_ID,
        TestUtils.TTL,
        MtMessageType.INTERNAL_MT_MESSAGE);
  }

  private static ProcessableMtMessage createProcessableMtMessage(String serviceAccessTokenString) {
    return createProcessableMtMessage(serviceAccessTokenString, TestUtils.SERVICE_ID);
  }

  private static String createUnknownKeyIdJwt(String scope) throws NoSuchAlgorithmException, IOException, InvalidKeySpecException, JOSEException {
    SignedJWT signedJWT = new SignedJWT(new JWSHeader.Builder(JWSAlgorithm.ES256).type(JOSEObjectType.JWT).keyID("unknown").build(),
        new JWTClaimsSet.Builder().issueTime(Date.from(Instant.now()))
            .expirationTime(Date.from(Instant.now().plusSeconds(100)))
            .notBeforeTime(Date.from(Instant.now()))
            .claim("scope", scope)
            .build());
    signedJWT.sign(createJwsSigner());
    return signedJWT.serialize();
  }

  private static String createUnsignedJwt() {
    PlainJWT plainJWT = new PlainJWT(new JWTClaimsSet.Builder().issueTime(Date.from(Instant.now()))
        .expirationTime(Date.from(Instant.now().plusSeconds(100)))
        .notBeforeTime(Date.from(Instant.now()))
        .build());
    return plainJWT.serialize();
  }

  private static String createValidJwt(String scope) throws NoSuchAlgorithmException, IOException, InvalidKeySpecException, JOSEException {
    SignedJWT signedJWT = new SignedJWT(new JWSHeader.Builder(JWSAlgorithm.ES256).type(JOSEObjectType.JWT).build(),
        new JWTClaimsSet.Builder().issueTime(Date.from(Instant.now()))
            .expirationTime(Date.from(Instant.now().plusSeconds(100)))
            .claim("exp_mh", Date.from(Instant.now().plusSeconds(100)))
            .notBeforeTime(Date.from(Instant.now()))
            .claim("scope", scope)
            .build());
    signedJWT.sign(createJwsSigner());
    return signedJWT.serialize();
  }

  @Test
  void validateExpiredServiceAccessTokenV0() throws IOException, JOSEException {
    JWTProcessor<ServiceSecurityContext> jwtProcessor = createJwtProcessor(createLegacyClaimsVerifier());
    ServiceAccessTokenValidator validator = new ServiceAccessTokenValidator(jwtProcessor);

    AssertThrows.exception(() -> validator.validateServiceAccessToken(createProcessableMtMessage(createExpiredJwt(DEFAULT_V0_SCOPE))),
        ValidationFailureReason.AUTHENTICATION_FAILURE.name(), ValidationException.class);
  }

  @Test
  void validateExpiredServiceAccessTokenV1() throws IOException, JOSEException {
    JWTProcessor<ServiceSecurityContext> jwtProcessor = createJwtProcessor(new Idpm2mScopeVerifier(Clock.systemDefaultZone()));
    ServiceAccessTokenValidator validator = new ServiceAccessTokenValidator(jwtProcessor);

    AssertThrows.exception(() -> validator.validateServiceAccessToken(createProcessableMtMessage(createExpiredJwt(DEFAULT_V1_SCOPE))),
        ValidationFailureReason.AUTHENTICATION_FAILURE.name(), ValidationException.class);
  }

  @Test
  void validateServiceAccessTokenWithUnknownKeyIdV0() throws IOException, JOSEException {
    JWTProcessor<ServiceSecurityContext> jwtProcessor = createJwtProcessor(createLegacyClaimsVerifier());
    ServiceAccessTokenValidator validator = new ServiceAccessTokenValidator(jwtProcessor);

    AssertThrows.exception(() -> validator.validateServiceAccessToken(createProcessableMtMessage(createUnknownKeyIdJwt(DEFAULT_V0_SCOPE))),
        ValidationFailureReason.AUTHENTICATION_FAILURE.name(), ValidationException.class);
  }

  @Test
  void validateServiceAccessTokenWithUnknownKeyIdV1() throws IOException, JOSEException {
    JWTProcessor<ServiceSecurityContext> jwtProcessor = createJwtProcessor(new Idpm2mScopeVerifier(Clock.systemDefaultZone()));
    ServiceAccessTokenValidator validator = new ServiceAccessTokenValidator(jwtProcessor);

    AssertThrows.exception(() -> validator.validateServiceAccessToken(createProcessableMtMessage(createUnknownKeyIdJwt(DEFAULT_V1_SCOPE))),
        ValidationFailureReason.AUTHENTICATION_FAILURE.name(), ValidationException.class);
  }

  @Test
  void validateUnsignedServiceAccessTokenV0() throws IOException, KeySourceException {
    JWTProcessor<ServiceSecurityContext> jwtProcessor = createJwtProcessor(createLegacyClaimsVerifier());
    ServiceAccessTokenValidator validator = new ServiceAccessTokenValidator(jwtProcessor);

    AssertThrows.exception(() -> validator.validateServiceAccessToken(createProcessableMtMessage(createUnsignedJwt())),
        ValidationFailureReason.AUTHENTICATION_FAILURE.name(), ValidationException.class);
  }

  @Test
  void validateUnsignedServiceAccessTokenV1() throws IOException, KeySourceException {
    JWTProcessor<ServiceSecurityContext> jwtProcessor = createJwtProcessor(new Idpm2mScopeVerifier(Clock.systemDefaultZone()));
    ServiceAccessTokenValidator validator = new ServiceAccessTokenValidator(jwtProcessor);

    AssertThrows.exception(() -> validator.validateServiceAccessToken(createProcessableMtMessage(createUnsignedJwt())),
        ValidationFailureReason.AUTHENTICATION_FAILURE.name(), ValidationException.class);
  }

  @Test
  void validateValidServiceAccessTokenV0() throws IOException, JOSEException {
    JWTProcessor<ServiceSecurityContext> jwtProcessor = createJwtProcessor(createLegacyClaimsVerifier());
    ServiceAccessTokenValidator validator = new ServiceAccessTokenValidator(jwtProcessor);

    Assertions.assertDoesNotThrow(() -> validator.validateServiceAccessToken(createProcessableMtMessage(createValidJwt(DEFAULT_V0_SCOPE))));
  }

  @Test
  void validateValidServiceAccessTokenV1() throws IOException, JOSEException {
    JWTProcessor<ServiceSecurityContext> jwtProcessor = createJwtProcessor(new Idpm2mScopeVerifier(Clock.systemDefaultZone()));
    ServiceAccessTokenValidator validator = new ServiceAccessTokenValidator(jwtProcessor);

    Assertions.assertDoesNotThrow(() -> validator.validateServiceAccessToken(createProcessableMtMessage(createValidJwt(DEFAULT_V1_SCOPE))));
  }

  @Test
  void validateValidServiceAccessTokenV1WithWrongServiceId() throws IOException, JOSEException {
    JWTProcessor<ServiceSecurityContext> jwtProcessor = createJwtProcessor(new Idpm2mScopeVerifier(Clock.systemDefaultZone()));
    ServiceAccessTokenValidator validator = new ServiceAccessTokenValidator(jwtProcessor);

    AssertThrows.exception(() -> validator.validateServiceAccessToken(createProcessableMtMessage(createValidJwt(DEFAULT_V1_SCOPE), new ServiceId(42))),
        ValidationFailureReason.AUTHENTICATION_FAILURE.name(), ValidationException.class);
  }
}
