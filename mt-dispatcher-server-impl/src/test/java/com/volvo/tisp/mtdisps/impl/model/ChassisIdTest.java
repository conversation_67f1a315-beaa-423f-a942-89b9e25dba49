package com.volvo.tisp.mtdisps.impl.model;

import org.assertj.core.api.Assertions;
import org.junit.Test;

public class ChassisIdTest {
  @Test
  public void chassisIdTest() {
    Assertions.assertThat(ChassisId.fromComponents("AA", "1234").value()).isEqualTo("AA-001234");
    Assertions.assertThat(ChassisId.fromComponents("AAA", "123456").value()).isEqualTo("AAA-123456");
    Assertions.assertThatIllegalArgumentException().isThrownBy(() -> ChassisId.fromComponents("AA", "1234567"));
    Assertions.assertThatIllegalArgumentException().isThrownBy(() -> ChassisId.fromComponents("AA", ""));
    Assertions.assertThatIllegalArgumentException().isThrownBy(() -> ChassisId.fromComponents("", ""));
    Assertions.assertThatIllegalArgumentException().isThrownBy(() -> ChassisId.fromComponents("AAAAAA", ""));
    Assertions.assertThatIllegalArgumentException().isThrownBy(() -> ChassisId.fromComponents("AAAAAA", "123456"));
  }
}
