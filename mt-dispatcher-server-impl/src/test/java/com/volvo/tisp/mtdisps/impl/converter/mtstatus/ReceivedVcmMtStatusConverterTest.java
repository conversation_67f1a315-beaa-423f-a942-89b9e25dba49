package com.volvo.tisp.mtdisps.impl.converter.mtstatus;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtStatus;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ReceivedVcmMtStatusConverterTest {
  private static void verifyReceivedMtStatus(ReceivedMtStatus expected, ReceivedMtStatus actual) {
    Assertions.assertEquals(expected.correlationId(), actual.correlationId());
    Assertions.assertEquals(expected.description(), actual.description());
    Assertions.assertEquals(expected.status(), actual.status());
    Assertions.assertEquals(expected.trackingIdentifier(), actual.trackingIdentifier());
  }

  @Test
  void applyInvalidTest() {
    ReceivedVcmMtStatusConverter receivedVcmMtStatusConverter = new ReceivedVcmMtStatusConverter();

    AssertThrows.illegalArgumentException(() -> receivedVcmMtStatusConverter.apply(null), "mtStatus must not be null");
  }

  @Test
  void applyTest() {
    ReceivedVcmMtStatusConverter receivedVcmMtStatusConverter = new ReceivedVcmMtStatusConverter();

    ReceivedMtStatus receivedMtStatus = receivedVcmMtStatusConverter.apply(TestUtils.createAssetMtSchedulerMtStatus(true)).get();
    verifyReceivedMtStatus(TestUtils.createReceivedMtStatus(), receivedMtStatus);

    Assertions.assertTrue(receivedVcmMtStatusConverter.apply(TestUtils.createAssetMtSchedulerMtStatus(false)).isPresent());
  }
}
