package com.volvo.tisp.mtdisps.impl.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ClientIdTest {
  private static final String CLIENT_ID_STRING = "Some client Id";

  @Test
  void clientIdInvalidTest() {
    AssertThrows.illegalArgumentException(() -> new ClientId(""), "clientIdString must not be empty");
    AssertThrows.illegalArgumentException(() -> new ClientId(null), "clientIdString must not be null");
  }

  @Test
  void clientIdTest() {
    Assertions.assertSame(CLIENT_ID_STRING, new ClientId(CLIENT_ID_STRING).clientIdString());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals(CLIENT_ID_STRING, new ClientId(CLIENT_ID_STRING).toString());
  }
}