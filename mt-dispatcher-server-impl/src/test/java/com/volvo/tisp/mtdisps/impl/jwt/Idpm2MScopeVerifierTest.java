package com.volvo.tisp.mtdisps.impl.jwt;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.proc.BadJWTException;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;

class Idpm2MScopeVerifierTest {
  public static final Instant EARLY_INSTANT = Instant.ofEpochSecond(0);
  public static final Instant LATE_INSTANT = Instant.ofEpochSecond(10);
  public static final Instant MIDDLE_INSTANT = Instant.ofEpochSecond(5);
  public static final String SCOPE = "mh.w svc.42";
  public static final ServiceId SERVICE_ID = new ServiceId(42);

  private static JWTClaimsSet createClaimSet(String scope) {
    return new JWTClaimsSet.Builder()
        .claim("scope", scope)
        .claim("exp_mh", Date.from(LATE_INSTANT).toInstant().getEpochSecond())
        .issueTime(Date.from(EARLY_INSTANT))
        .build();
  }

  private static JWTClaimsSet createClaimSetWithDate() {
    return new JWTClaimsSet.Builder()
        .claim("scope", SCOPE)
        .claim("exp_mh", Date.from(LATE_INSTANT))
        .issueTime(Date.from(EARLY_INSTANT))
        .build();
  }

  private static JWTClaimsSet createClaimSetWithoutExpMh() {
    return new JWTClaimsSet.Builder()
        .claim("scope", SCOPE)
        .issueTime(Date.from(EARLY_INSTANT))
        .build();
  }

  private static Clock mockClock(Instant instant) {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(instant);
    return clock;
  }

  @Test
  void expMhNotSet() {
    Clock clock = mockClock(MIDDLE_INSTANT);
    JWTClaimsSet claimsSet = createClaimSetWithoutExpMh();

    Idpm2mScopeVerifier idpM2MScopeVerifier = new Idpm2mScopeVerifier(clock);
    Assertions.assertThrows(BadJWTException.class, () -> idpM2MScopeVerifier.verify(claimsSet, ServiceSecurityContext.of(SERVICE_ID)));
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock);
  }

  @Test
  void expiredTest() {
    Clock clock = mockClock(LATE_INSTANT.plus(Duration.ofSeconds(1)));
    JWTClaimsSet claimsSet = createClaimSet(SCOPE);

    Idpm2mScopeVerifier idpM2MScopeVerifier = new Idpm2mScopeVerifier(clock);
    Assertions.assertThrows(BadJWTException.class, () -> idpM2MScopeVerifier.verify(claimsSet, ServiceSecurityContext.of(SERVICE_ID)));
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock);
  }

  @Test
  void invalidClaimTest() {
    Clock clock = mockClock(MIDDLE_INSTANT);
    JWTClaimsSet claimsSet = createClaimSet("mh.w svc.66");

    Idpm2mScopeVerifier idpM2MScopeVerifier = new Idpm2mScopeVerifier(clock);
    Assertions.assertThrows(BadJWTException.class, () -> idpM2MScopeVerifier.verify(claimsSet, ServiceSecurityContext.of(SERVICE_ID)));
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock);
  }

  @Test
  void issuedInFutureTest() {
    Clock clock = mockClock(EARLY_INSTANT.minus(Duration.ofSeconds(1)));
    JWTClaimsSet claimsSet = createClaimSet(SCOPE);

    Idpm2mScopeVerifier idpM2MScopeVerifier = new Idpm2mScopeVerifier(clock);
    Assertions.assertThrows(BadJWTException.class, () -> idpM2MScopeVerifier.verify(claimsSet, ServiceSecurityContext.of(SERVICE_ID)));
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock);
  }

  @Test
  void verifySuccessTest() {
    Clock clock = mockClock(MIDDLE_INSTANT);
    JWTClaimsSet claimsSet = createClaimSet(SCOPE);

    Idpm2mScopeVerifier idpM2MScopeVerifier = new Idpm2mScopeVerifier(clock);
    Assertions.assertDoesNotThrow(() -> idpM2MScopeVerifier.verify(claimsSet, ServiceSecurityContext.of(SERVICE_ID)));
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock);
  }

  @Test
  void verifySuccessWithMhAsDateTest() {
    Clock clock = mockClock(MIDDLE_INSTANT);
    JWTClaimsSet claimsSet = createClaimSetWithDate();

    Idpm2mScopeVerifier idpM2MScopeVerifier = new Idpm2mScopeVerifier(clock);
    Assertions.assertDoesNotThrow(() -> idpM2MScopeVerifier.verify(claimsSet, ServiceSecurityContext.of(SERVICE_ID)));
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock);
  }
}
