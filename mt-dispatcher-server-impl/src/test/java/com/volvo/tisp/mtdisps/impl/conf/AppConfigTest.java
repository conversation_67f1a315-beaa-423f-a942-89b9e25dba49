package com.volvo.tisp.mtdisps.impl.conf;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.reactive.function.client.WebClient;

class AppConfigTest {
  @Test
  void cacheManagerTest() {
    Assertions.assertNotNull(new AppConfig().cacheManager(Duration.ofSeconds(1), 2, 3));
  }

  @Test
  void createClockTest() {
    Assertions.assertNotNull(new AppConfig().createClock());
  }

  @Test
  void createMongoClientTest() {
    ConnectionPoolConfigProperties connectionPoolConfigProperties = new ConnectionPoolConfigProperties(10, 30, 100, 10, 10);

    Assertions.assertNotNull(new AppConfig().createMongoClient(connectionPoolConfigProperties));
  }

  @Test
  void createMongockConnectionTest() {
    Assertions.assertNotNull(new AppConfig().createMongockConnection(Mockito.mock(MongoTemplate.class)));
  }

  @Test
  void createObjectMapperTest() {
    Assertions.assertNotNull(new AppConfig().createObjectMapper());
  }

  @Test
  void createViewRegistryTest() {
    Assertions.assertNotNull(new AppConfig().createViewRegistry(WebClient.builder()));
  }
}
