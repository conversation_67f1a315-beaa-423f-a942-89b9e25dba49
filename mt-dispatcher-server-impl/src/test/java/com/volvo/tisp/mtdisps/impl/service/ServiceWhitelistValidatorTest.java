package com.volvo.tisp.mtdisps.impl.service;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

class ServiceWhitelistValidatorTest {
  @Test
  void defaultWhitelistTest() {
    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage();
    Assertions.assertDoesNotThrow(() -> new ServiceWhitelistValidator(List.of(0)).validateIsWhitelisted(processableMtMessage));
  }

  @Test
  void isNotWhitelistedTest() {
    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage();
    Assertions.assertThrows(ValidationException.class, () -> new ServiceWhitelistValidator(List.of(42)).validateIsWhitelisted(processableMtMessage));
    Assertions.assertThrows(ValidationException.class, () -> new ServiceWhitelistValidator(List.of()).validateIsWhitelisted(processableMtMessage));
  }

  @Test
  void isWhitelistedTest() {
    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage();
    Assertions.assertDoesNotThrow(() -> new ServiceWhitelistValidator(List.of(1)).validateIsWhitelisted(processableMtMessage));
  }
}
