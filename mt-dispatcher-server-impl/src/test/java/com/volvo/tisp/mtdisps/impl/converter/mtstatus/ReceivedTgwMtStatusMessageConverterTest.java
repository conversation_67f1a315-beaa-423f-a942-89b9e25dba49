package com.volvo.tisp.mtdisps.impl.converter.mtstatus;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.Status;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.api.v2.MtStatusMessage;

class ReceivedTgwMtStatusMessageConverterTest {
  private static void verifyMtStatus(ReceivedMtStatus expected, ReceivedMtStatus actual) {
    Assertions.assertEquals(expected.correlationId(), actual.correlationId());
    Assertions.assertEquals(expected.status(), actual.status());
    Assertions.assertEquals(expected.trackingIdentifier(), actual.trackingIdentifier());
  }

  @Test
  void applyInvalidStatusTest() {
    ReceivedTgwMtStatusMessageConverter receivedTgwMtStatusMessageConverter = new ReceivedTgwMtStatusMessageConverter();

    MtStatusMessage mtStatusMessage = TestUtils.createMtStatusMessage();
    mtStatusMessage.setStatus("INVALID");

    TispContext.runInContext(() -> Assertions.assertSame(Status.FAILED, receivedTgwMtStatusMessageConverter.apply(mtStatusMessage).orElseThrow().status()));
  }

  @Test
  void applyInvalidTest() {
    ReceivedTgwMtStatusMessageConverter receivedTgwMtStatusMessageConverter = new ReceivedTgwMtStatusMessageConverter();

    AssertThrows.illegalArgumentException(() -> receivedTgwMtStatusMessageConverter.apply(null), "mtStatusMessage must not be null");
  }

  @Test
  void applyTest() {
    ReceivedTgwMtStatusMessageConverter receivedTgwMtStatusMessageConverter = new ReceivedTgwMtStatusMessageConverter();

    TispContext.runInContext(() -> {
      ReceivedMtStatus receivedMtStatus = receivedTgwMtStatusMessageConverter.apply(TestUtils.createMtStatusMessage()).get();
      verifyMtStatus(TestUtils.createReceivedMtStatus(), receivedMtStatus);
    }, context -> context.tid(TestUtils.TRACKING_ID));
  }

  @Test
  void runtimeExceptionTest() {
    ReceivedTgwMtStatusMessageConverter receivedTgwMtStatusMessageConverter = new ReceivedTgwMtStatusMessageConverter();

    MtStatusMessage mtStatusMessage = TestUtils.createMtStatusMessage();

    Assertions.assertSame(Optional.empty(), receivedTgwMtStatusMessageConverter.apply(mtStatusMessage));
  }
}
