package com.volvo.tisp.mtdisps.impl.reporter;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.utils.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class DbOperationsMetricReporterTest {
  private static final String CRUD = "CRUD";
  private static final String DB_MESSAGE_TRANSACTION_EXPIRED_SIZE = "db.message-transaction-parameters-expired-size";
  private static final String DB_MESSAGE_TRANSACTION_PARAMETERS = "db.message-transaction-parameters";
  private static final String DB_MESSAGE_TRANSACTION_PARAMETERS_FAILURE = "db.message-transaction-parameters.failure";
  private static final String DB_VEHICLE_INFORMATION = "db.vehicle-information";
  private static final String DB_VEHICLE_INFORMATION_FAILURE = "db.vehicle-information.failure";
  private static final Duration DEFAULT_DURATION = Duration.ofSeconds(1);
  private static final Duration TOTAL_DURATION = Duration.ofSeconds(2);

  @Test
  void onExpiredMessageTransactionCountTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbOperationsMetricReporter::new, (meterRegistry, dbOperationsMetricReporter) -> {
      dbOperationsMetricReporter.onExpiredMessageTransactionCount(10);
      MetricsReporterTestUtils.checkGauge(meterRegistry, DB_MESSAGE_TRANSACTION_EXPIRED_SIZE, 10);

      dbOperationsMetricReporter.onExpiredMessageTransactionCount(5);
      MetricsReporterTestUtils.checkGauge(meterRegistry, DB_MESSAGE_TRANSACTION_EXPIRED_SIZE, 5);
    });
  }

  @Test
  void onMessageTransactionCrudDurationTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbOperationsMetricReporter::new, (meterRegistry, dbOperationsMetricReporter) -> {
      dbOperationsMetricReporter.onMessageTransactionCrudDuration(DEFAULT_DURATION, Crud.READ);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS, Tags.of(CRUD, Crud.READ.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onMessageTransactionCrudDuration(DEFAULT_DURATION, Crud.CREATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS, Tags.of(CRUD, Crud.CREATE.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onMessageTransactionCrudDuration(DEFAULT_DURATION, Crud.UPDATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS, Tags.of(CRUD, Crud.UPDATE.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onMessageTransactionCrudDuration(DEFAULT_DURATION, Crud.DELETE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS, Tags.of(CRUD, Crud.DELETE.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onMessageTransactionCrudDuration(DEFAULT_DURATION, Crud.READ);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS, Tags.of(CRUD, Crud.READ.name()), TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onMessageTransactionCrudDuration(DEFAULT_DURATION, Crud.CREATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS, Tags.of(CRUD, Crud.CREATE.name()), TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onMessageTransactionCrudDuration(DEFAULT_DURATION, Crud.UPDATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS, Tags.of(CRUD, Crud.UPDATE.name()), TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onMessageTransactionCrudDuration(DEFAULT_DURATION, Crud.DELETE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS, Tags.of(CRUD, Crud.DELETE.name()), TOTAL_DURATION, 2);
    });
  }

  @Test
  void onMessageTransactionCrudFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbOperationsMetricReporter::new, (meterRegistry, dbOperationsMetricReporter) -> {
      dbOperationsMetricReporter.onMessageTransactionCrudFailure(Crud.READ);
      MetricsReporterTestUtils.checkCounter(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS_FAILURE, Tags.of(CRUD, Crud.READ.name()), 1);

      dbOperationsMetricReporter.onMessageTransactionCrudFailure(Crud.READ);
      MetricsReporterTestUtils.checkCounter(meterRegistry, DB_MESSAGE_TRANSACTION_PARAMETERS_FAILURE, Tags.of(CRUD, Crud.READ.name()), 2);
    });
  }

  @Test
  void onVehicleInfoCrudDurationTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbOperationsMetricReporter::new, (meterRegistry, dbOperationsMetricReporter) -> {
      dbOperationsMetricReporter.onVehicleInfoCrudDuration(DEFAULT_DURATION, Crud.READ);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onVehicleInfoCrudDuration(DEFAULT_DURATION, Crud.CREATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.CREATE.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onVehicleInfoCrudDuration(DEFAULT_DURATION, Crud.UPDATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.UPDATE.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onVehicleInfoCrudDuration(DEFAULT_DURATION, Crud.DELETE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.DELETE.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onVehicleInfoCrudDuration(DEFAULT_DURATION, Crud.READ);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name()), TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onVehicleInfoCrudDuration(DEFAULT_DURATION, Crud.CREATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.CREATE.name()), TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onVehicleInfoCrudDuration(DEFAULT_DURATION, Crud.UPDATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.UPDATE.name()), TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onVehicleInfoCrudDuration(DEFAULT_DURATION, Crud.DELETE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.DELETE.name()), TOTAL_DURATION, 2);
    });
  }

  @Test
  void onVehicleInfoCrudFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbOperationsMetricReporter::new, (meterRegistry, dbOperationsMetricReporter) -> {
      dbOperationsMetricReporter.onVehicleInfoCrudFailure(Crud.READ);
      MetricsReporterTestUtils.checkCounter(meterRegistry, DB_VEHICLE_INFORMATION_FAILURE, Tags.of(CRUD, Crud.READ.name()), 1);

      dbOperationsMetricReporter.onVehicleInfoCrudFailure(Crud.READ);
      MetricsReporterTestUtils.checkCounter(meterRegistry, DB_VEHICLE_INFORMATION_FAILURE, Tags.of(CRUD, Crud.READ.name()), 2);
    });
  }
}
