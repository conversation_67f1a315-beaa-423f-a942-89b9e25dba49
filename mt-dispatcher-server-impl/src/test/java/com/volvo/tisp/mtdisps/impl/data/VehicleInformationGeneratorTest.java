package com.volvo.tisp.mtdisps.impl.data;

import java.nio.file.Path;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;

class VehicleInformationGeneratorTest {
  @Test
  void generateVehicleInformationBatchTest() {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    int numberOfVehicle = 100;

    VehicleInformationGenerator vehicleInformationGenerator = new VehicleInformationGenerator(10, vehicleInformationRepository);
    int vehicleGenerated = vehicleInformationGenerator.generateVehicleInformation(numberOfVehicle, 10, Path.of("vehicle.csv"));

    Assertions.assertEquals(numberOfVehicle, vehicleGenerated);
    Mockito.verify(vehicleInformationRepository, Mockito.times(10)).saveAll(ArgumentMatchers.anyList());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void generateVehicleInformationTest() {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    int numberOfVehicle = 9;

    VehicleInformationGenerator vehicleInformationGenerator = new VehicleInformationGenerator(10, vehicleInformationRepository);
    int vehicleGenerated = vehicleInformationGenerator.generateVehicleInformation(numberOfVehicle, 10, Path.of("vehicle.csv"));

    Assertions.assertEquals(numberOfVehicle, vehicleGenerated);
    Mockito.verify(vehicleInformationRepository, Mockito.times(1)).saveAll(ArgumentMatchers.anyList());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }
}
