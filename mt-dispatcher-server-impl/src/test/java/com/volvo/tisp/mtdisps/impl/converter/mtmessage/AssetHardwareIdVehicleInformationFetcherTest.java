package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.dao.IncorrectResultSizeDataAccessException;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.model.AssetHardwareId;
import com.volvo.tisp.mtdisps.impl.model.MultipleAssetsFoundException;
import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.model.Version;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

class AssetHardwareIdVehicleInformationFetcherTest {
  @Test
  void activationFetchTest() throws MultipleAssetsFoundException {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    Mockito.when(vehicleInformationRepository.findByPartNumberAndSerialNumber(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value()))
        .thenReturn(Optional.of(vehicleInformationEntity));
    AssetHardwareIdVehicleInformationFetcher assetHardwareIdVehicleInformationFetcher = new AssetHardwareIdVehicleInformationFetcher(
        vehicleInformationRepository, false);

    Optional<VehicleInformation> vehicleInformationOptional = assetHardwareIdVehicleInformationFetcher.fetch(
        new AssetHardwareId(TestUtils.PART_NUMBER, TestUtils.SERIAL_NUMBER).toString(), true);

    org.assertj.core.api.Assertions.assertThat(vehicleInformationOptional).isPresent().contains(VehicleInformation.fromEntity(vehicleInformationEntity));
    Mockito.verify(vehicleInformationRepository).findByPartNumberAndSerialNumber(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void activationFetchWhenUnIdentifiedAssetMessagesAreAllowedTest() throws MultipleAssetsFoundException {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    Mockito.when(vehicleInformationRepository.findByPartNumberAndSerialNumber(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value()))
        .thenReturn(Optional.empty());
    AssetHardwareIdVehicleInformationFetcher assetHardwareIdVehicleInformationFetcher = new AssetHardwareIdVehicleInformationFetcher(
        vehicleInformationRepository, true);

    Optional<VehicleInformation> vehicleInformationOptional = assetHardwareIdVehicleInformationFetcher.fetch(
        new AssetHardwareId(TestUtils.PART_NUMBER, TestUtils.SERIAL_NUMBER).toString(), true);

    org.assertj.core.api.Assertions.assertThat(vehicleInformationOptional)
        .isPresent()
        .contains(new VehicleInformation(Optional.empty(), OperationalStatus.OPERATIONAL,
            TestUtils.ASSET_HARDWARE_ID, false,
            Optional.empty(), TelematicUnitType.VCM, new Version(1L), Optional.empty()));
    Mockito.verify(vehicleInformationRepository).findByPartNumberAndSerialNumber(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void fetchTest() throws MultipleAssetsFoundException {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    Mockito.when(
            vehicleInformationRepository.findByPartNumberAndSerialNumberAndOperationalIsTrue(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value()))
        .thenReturn(Optional.of(vehicleInformationEntity));
    AssetHardwareIdVehicleInformationFetcher assetHardwareIdVehicleInformationFetcher = new AssetHardwareIdVehicleInformationFetcher(
        vehicleInformationRepository, false);

    Optional<VehicleInformation> vehicleInformationOptional = assetHardwareIdVehicleInformationFetcher.fetch(
        new AssetHardwareId(TestUtils.PART_NUMBER, TestUtils.SERIAL_NUMBER).toString(), false);

    org.assertj.core.api.Assertions.assertThat(vehicleInformationOptional).isPresent().contains(VehicleInformation.fromEntity(vehicleInformationEntity));
    Mockito.verify(vehicleInformationRepository)
        .findByPartNumberAndSerialNumberAndOperationalIsTrue(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void fetchWhenUnIdentifiedAssetMessagesAreAllowedTest() throws MultipleAssetsFoundException {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    Mockito.when(
            vehicleInformationRepository.findByPartNumberAndSerialNumberAndOperationalIsTrue(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value()))
        .thenReturn(Optional.empty());
    AssetHardwareIdVehicleInformationFetcher assetHardwareIdVehicleInformationFetcher = new AssetHardwareIdVehicleInformationFetcher(
        vehicleInformationRepository, true);

    Optional<VehicleInformation> vehicleInformationOptional = assetHardwareIdVehicleInformationFetcher.fetch(
        new AssetHardwareId(TestUtils.PART_NUMBER, TestUtils.SERIAL_NUMBER).toString(), false);

    org.assertj.core.api.Assertions.assertThat(vehicleInformationOptional)
        .isPresent()
        .contains(new VehicleInformation(Optional.empty(), OperationalStatus.OPERATIONAL,
            TestUtils.ASSET_HARDWARE_ID, false,
            Optional.empty(), TelematicUnitType.VCM, new Version(1L), Optional.empty()));
    Mockito.verify(vehicleInformationRepository)
        .findByPartNumberAndSerialNumberAndOperationalIsTrue(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void fetchWithMultipleAssetsTest() {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    Mockito.when(
            vehicleInformationRepository.findByPartNumberAndSerialNumberAndOperationalIsTrue(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value()))
        .thenThrow(IncorrectResultSizeDataAccessException.class);
    AssetHardwareIdVehicleInformationFetcher assetHardwareIdVehicleInformationFetcher = new AssetHardwareIdVehicleInformationFetcher(
        vehicleInformationRepository, false);

    Assertions.assertThatExceptionOfType(MultipleAssetsFoundException.class)
        .isThrownBy(
            () -> assetHardwareIdVehicleInformationFetcher.fetch(new AssetHardwareId(TestUtils.PART_NUMBER, TestUtils.SERIAL_NUMBER).toString(), false));

    Mockito.verify(vehicleInformationRepository)
        .findByPartNumberAndSerialNumberAndOperationalIsTrue(TestUtils.PART_NUMBER.value(), TestUtils.SERIAL_NUMBER.value());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void getSupportedAssetHardwareIdentifierType() {
    AssetHardwareIdVehicleInformationFetcher assetHardwareIdVehicleInformationFetcher = new AssetHardwareIdVehicleInformationFetcher(
        Mockito.mock(VehicleInformationRepository.class), false);

    Assertions.assertThat(assetHardwareIdVehicleInformationFetcher.getSupportedAssetIdentifierType()).isEqualTo(AssetIdentifierType.ASSET_HARDWARE_ID);
  }

}