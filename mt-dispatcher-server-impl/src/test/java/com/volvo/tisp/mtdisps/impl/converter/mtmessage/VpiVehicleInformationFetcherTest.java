package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.model.MultipleAssetsFoundException;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

class VpiVehicleInformationFetcherTest {
  @Test
  void activationFetchTest() throws MultipleAssetsFoundException {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    Mockito.when(vehicleInformationRepository.findByVpi(TestUtils.VPI.toString()))
        .thenReturn(Optional.of(vehicleInformationEntity));
    VpiVehicleInformationFetcher vpiVehicleInformationFetcher = new VpiVehicleInformationFetcher(vehicleInformationRepository);

    Optional<VehicleInformation> vehicleInformationOptional = vpiVehicleInformationFetcher.fetch(TestUtils.VPI.toString(), true);

    org.assertj.core.api.Assertions.assertThat(vehicleInformationOptional).isPresent().contains(VehicleInformation.fromEntity(vehicleInformationEntity));
    Mockito.verify(vehicleInformationRepository).findByVpi(TestUtils.VPI.toString());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void fetchTest() throws MultipleAssetsFoundException {
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    Mockito.when(vehicleInformationRepository.findByVpiAndOperationalIsTrue(TestUtils.VPI.toString()))
        .thenReturn(Optional.of(vehicleInformationEntity));
    VpiVehicleInformationFetcher vpiVehicleInformationFetcher = new VpiVehicleInformationFetcher(vehicleInformationRepository);

    Optional<VehicleInformation> vehicleInformationOptional = vpiVehicleInformationFetcher.fetch(TestUtils.VPI.toString(), false);

    org.assertj.core.api.Assertions.assertThat(vehicleInformationOptional).isPresent().contains(VehicleInformation.fromEntity(vehicleInformationEntity));
    Mockito.verify(vehicleInformationRepository).findByVpiAndOperationalIsTrue(TestUtils.VPI.toString());
    Mockito.verifyNoMoreInteractions(vehicleInformationRepository);
  }

  @Test
  void getSupportedAssetHardwareIdentifierType() {
    VpiVehicleInformationFetcher vpiVehicleInformationFetcher = new VpiVehicleInformationFetcher(Mockito.mock(VehicleInformationRepository.class));

    Assertions.assertThat(vpiVehicleInformationFetcher.getSupportedAssetIdentifierType()).isEqualTo(AssetIdentifierType.VPI);
  }
}