package com.volvo.tisp.mtdisps.impl.conf;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ConnectionPoolConfigPropertiesTest {
  @Test
  void validateTest() {
    AssertThrows.illegalArgumentException(
        () -> new ConnectionPoolConfigProperties(0, 50, 100, 10, 10),
        "maxConnectionIdleTime must be positive: 0");
    AssertThrows.illegalArgumentException(
        () -> new ConnectionPoolConfigProperties(10, -1, 100, 10, 10),
        "maxConnectionLifeTime must be positive: -1");
    AssertThrows.illegalArgumentException(
        () -> new ConnectionPoolConfigProperties(10, 30, 0, 10, 10),
        "maxPoolSize must be positive: 0");
    AssertThrows.illegalArgumentException(
        () -> new ConnectionPoolConfigProperties(10, 30, 100, -1, 10),
        "maxWaitTime must be positive: -1");
    AssertThrows.illegalArgumentException(
        () -> new ConnectionPoolConfigProperties(10, 30, 100, 10, -1),
        "minPoolSize must be positive: -1");
  }
}
