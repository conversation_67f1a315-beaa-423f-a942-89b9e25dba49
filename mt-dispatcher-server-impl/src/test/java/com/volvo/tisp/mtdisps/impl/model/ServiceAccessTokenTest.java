package com.volvo.tisp.mtdisps.impl.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ServiceAccessTokenTest {
  private static final String SERVICE_ACCESS_TOKEN_STRING = "some_token";

  @Test
  void messageTypeTest() {
    Assertions.assertSame(SERVICE_ACCESS_TOKEN_STRING, new ServiceAccessToken(SERVICE_ACCESS_TOKEN_STRING).serviceAccessTokenString());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals(SERVICE_ACCESS_TOKEN_STRING, new ServiceAccessToken(SERVICE_ACCESS_TOKEN_STRING).toString());
  }
}