package com.volvo.tisp.mtdisps.impl.jwt;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.model.ServiceId;

class ServiceSecurityContextTest {
  @Test
  void isValidClaimTest() {
    ServiceSecurityContext context = ServiceSecurityContext.of(new ServiceId(42));
    Assertions.assertTrue(context.isValidClaim("mh.w svc.42"));
    Assertions.assertTrue(context.isValidClaim("svc.42 mh.w "));
    Assertions.assertTrue(context.isValidClaim(" svc.42 mh.w  "));
    Assertions.assertTrue(context.isValidClaim("mh.w svc.42 svc.41"));
    Assertions.assertFalse(context.isValidClaim("mh.w svc.66"));
    Assertions.assertFalse(context.isValidClaim("mh.r svc.42"));
    Assertions.assertFalse(context.isValidClaim("mh.r"));
    Assertions.assertFalse(context.isValidClaim("svc.42"));
  }
}
