package com.volvo.tisp.mtdisps.impl.utils;

import java.time.Clock;
import java.time.Instant;
import java.util.Base64;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

import org.mockito.Mockito;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.utility.DockerImageName;

import com.google.protobuf.ByteString;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.conf.SqsClientConfigProperties;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtMessage;
import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtStatus;
import com.volvo.tisp.mtdisps.impl.dto.Status;
import com.volvo.tisp.mtdisps.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.mtdisps.impl.model.AcceptedCost;
import com.volvo.tisp.mtdisps.impl.model.AssetHardwareId;
import com.volvo.tisp.mtdisps.impl.model.ChassisId;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.impl.model.PartNumber;
import com.volvo.tisp.mtdisps.impl.model.Priority;
import com.volvo.tisp.mtdisps.impl.model.SerialNumber;
import com.volvo.tisp.mtdisps.impl.model.ServiceAccessToken;
import com.volvo.tisp.mtdisps.impl.model.ServiceFunction;
import com.volvo.tisp.mtdisps.impl.model.ServiceId;
import com.volvo.tisp.mtdisps.impl.model.ServiceVersion;
import com.volvo.tisp.mtdisps.impl.model.TelematicUnitSubType;
import com.volvo.tisp.mtdisps.impl.model.Ttl;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.model.Version;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtStatusProtobuf;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.api.v2.SrpLevel;
import com.wirelesscar.tce.api.v2.SrpOption;
import com.wirelesscar.vqv.v1.api.NotificationType;

public class TestUtils {
  public static final MtMessage.AcceptedCost ACCEPTED_COST = MtMessage.AcceptedCost.HIGH;
  public static final AssetMtSchedulerMtMessageProtobuf.AcceptedCost ASSET_MT_SCHEDULER_ACCEPTED_COST = AssetMtSchedulerMtMessageProtobuf.AcceptedCost.COST_HIGH;
  public static final AssetMtSchedulerMtMessageProtobuf.Priority ASSET_MT_SCHEDULER_PRIORITY = AssetMtSchedulerMtMessageProtobuf.Priority.PRIORITY_HIGH;
  public static final ChassisId CHASSIS_ID = ChassisId.fromComponents("SCAR", "205045");
  public static final CorrelationId CORRELATION_ID = CorrelationId.ofString("f739a254-0560-11ee-be56-0242ac120002");
  public static final String DESCRIPTION = "some_description";
  public static final com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.Priority EXTERNAL_PRIORITY = com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.Priority.HIGH;
  public static final Instant INSTANT = Instant.ofEpochSecond(1664878057, 971000000);
  public static final String KEY_ID = "some_key_id";
  public static final PartNumber PART_NUMBER = new PartNumber("456");
  public static final String PAYLOAD = Base64.getEncoder().encodeToString(new byte[] {1, -2, 3, -4, 5});
  public static final MtMessage.Priority PRIORITY = MtMessage.Priority.HIGH;
  public static final ReplyTo REPLY_TO = ReplyTo.ofString("QUEUE_NAME");
  public static final SerialNumber SERIAL_NUMBER = new SerialNumber("123");
  public static final AssetHardwareId ASSET_HARDWARE_ID = AssetHardwareId.of(PART_NUMBER.value(), SERIAL_NUMBER.value());
  public static final ServiceAccessToken SERVICE_ACCESS_TOKEN = new ServiceAccessToken("some_token");
  public static final ServiceFunction SERVICE_FUNCTION = new ServiceFunction("some_service_function");
  public static final ServiceId SERVICE_ID = new ServiceId(1);
  public static final ServiceVersion SERVICE_VERSION = new ServiceVersion(2);
  public static final boolean SOFTCAR = false;
  public static final TelematicUnitSubType TELEMATIC_UNIT_SUB_TYPE = new TelematicUnitSubType("TGW3");
  public static final TelematicUnitType TELEMATIC_UNIT_TYPE = TelematicUnitType.TGW;
  public static final TrackingIdentifier TRACKING_ID = TrackingIdentifier.fromString("81CADC37C0A04E01A0B1DFCEAEDB5FA3");
  public static final Ttl TTL = Ttl.ONE_MINUTE;
  public static final Version VERSION = new Version(1);
  public static final Vpi VPI = Vpi.ofString("1FAC35BF1EDB1DFF91AAC0BEBC56F22B");
  private static final String DELIMITER = "-";
  private static final String ENQUEUEING_TYPE = "OVERRIDE";
  private static final com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.AcceptedCost EXTERNAL_ACCEPTED_COST = com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.AcceptedCost.HIGH;
  private static final boolean OVERRIDE = true;
  private static final SrpLevel SRP_LEVEL = SrpLevel.SRP_12;
  private static final com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.Ttl TTL_EXTERNAL = com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.Ttl.ONE_MINUTE;

  public static AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage createAssetMtSchedulerMtMessage(boolean withOptional) {
    AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage.Builder builder = AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage.newBuilder()
        .setAcceptedCost(ASSET_MT_SCHEDULER_ACCEPTED_COST)
        .setAssetHardwareId(ASSET_HARDWARE_ID.toString())
        .setIsSoftcar(SOFTCAR)
        .setOverride(OVERRIDE)
        .setPayload(ByteString.copyFrom(PAYLOAD.getBytes()))
        .setServiceAccessToken(SERVICE_ACCESS_TOKEN.serviceAccessTokenString())
        .setServiceFunction(SERVICE_FUNCTION.value())
        .setServiceId(SERVICE_ID.serviceId())
        .setServiceVersion(SERVICE_VERSION.serviceVersion())
        .setTrackingId(TRACKING_ID.toString())
        .setTtl(60);

    if (withOptional) {
      builder.setPriority(ASSET_MT_SCHEDULER_PRIORITY);
      builder.setKeyId(KEY_ID);
      builder.setCorrelationId(CORRELATION_ID.toString());
      builder.setPayloadSignature(PAYLOAD);
    }
    return builder.build();
  }

  public static AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus createAssetMtSchedulerMtStatus(boolean withOptional) {
    AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus.Builder builder = AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus.newBuilder()
        .setStatus(AssetMtSchedulerMtStatusProtobuf.Status.DELIVERED)
        .setCorrelationId(CORRELATION_ID.toString())
        .setTrackingId(TRACKING_ID.toString());

    if (withOptional) {
      builder.setDescription(DESCRIPTION);
    }
    return builder.build();
  }

  public static com.volvo.tisp.external.mt.message.client.json.v1.MtMessage createExternalMtMessage() {
    return new com.volvo.tisp.external.mt.message.client.json.v1.MtMessage().withAcceptedCost(EXTERNAL_ACCEPTED_COST)
        .withAssetIdentifier(CHASSIS_ID.value())
        .withAssetIdentifierType(com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.AssetIdentifierType.CHASSIS_ID)
        .withCorrelationId(CORRELATION_ID.toString())
        .withKeyId(KEY_ID)
        .withPayload(PAYLOAD)
        .withPayloadSignature(PAYLOAD)
        .withPriority(EXTERNAL_PRIORITY)
        .withServiceAccessToken(SERVICE_ACCESS_TOKEN.serviceAccessTokenString())
        .withServiceId(SERVICE_ID.serviceId())
        .withServiceVersion(SERVICE_VERSION.serviceVersion())
        .withTtl(TTL_EXTERNAL)
        .withTrackingId(TRACKING_ID.toString());
  }

  public static com.volvo.tisp.external.mt.message.client.json.v1.MtMessage createExternalMtMessageBase() {
    return new com.volvo.tisp.external.mt.message.client.json.v1.MtMessage()
        .withAcceptedCost(EXTERNAL_ACCEPTED_COST)
        .withPayload(PAYLOAD)
        .withServiceAccessToken(SERVICE_ACCESS_TOKEN.serviceAccessTokenString())
        .withServiceId(SERVICE_ID.serviceId())
        .withServiceVersion(SERVICE_VERSION.serviceVersion())
        .withTtl(com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.Ttl.valueOf(TTL.name()))
        .withAssetIdentifier(CHASSIS_ID.toString())
        .withAssetIdentifierType(com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.AssetIdentifierType.CHASSIS_ID)
        .withTrackingId(TrackingIdentifier.create().toString());
  }

  public static MessageTransactionParametersEntity createMessageTransactionParameters() {
    MessageTransactionParametersEntity messageTransactionParametersEntity = new MessageTransactionParametersEntity();
    messageTransactionParametersEntity.setCorrelationId(CORRELATION_ID.toString());
    messageTransactionParametersEntity.setExpireAt(Instant.now(Clock.systemUTC()).plus(3, TimeUnit.MINUTES.toChronoUnit()));
    messageTransactionParametersEntity.setServiceFunction(SERVICE_FUNCTION.value());
    messageTransactionParametersEntity.setServiceId(SERVICE_ID.serviceId());
    messageTransactionParametersEntity.setServiceVersion(SERVICE_VERSION.serviceVersion());
    messageTransactionParametersEntity.setTtl(TTL.toSeconds());
    messageTransactionParametersEntity.setAssetHardwareId(ASSET_HARDWARE_ID.toString());
    messageTransactionParametersEntity.setVpi(VPI.toString());
    messageTransactionParametersEntity.setMtMessageType(MtMessageType.INTERNAL_MT_MESSAGE.name());

    return messageTransactionParametersEntity;
  }

  public static MtMessage createMtMessage() {
    return new MtMessage().withAcceptedCost(ACCEPTED_COST)
        .withCorrelationId(CORRELATION_ID.toString())
        .withServiceFunction(SERVICE_FUNCTION.value())
        .withOverride(OVERRIDE)
        .withPayload(PAYLOAD)
        .withPriority(PRIORITY)
        .withServiceAccessToken(SERVICE_ACCESS_TOKEN.serviceAccessTokenString())
        .withServiceId(SERVICE_ID.serviceId())
        .withServiceVersion(SERVICE_VERSION.serviceVersion())
        .withTtl(MtMessage.Ttl.valueOf(TTL.name()))
        .withVehiclePlatformId(VPI.toString())
        .withTrackingId(TrackingIdentifier.create().toString());
  }

  public static MtMessage createMtMessageBase() {
    return new MtMessage()
        .withAcceptedCost(ACCEPTED_COST)
        .withServiceFunction(SERVICE_FUNCTION.value())
        .withPayload(PAYLOAD)
        .withServiceAccessToken(SERVICE_ACCESS_TOKEN.serviceAccessTokenString())
        .withServiceId(SERVICE_ID.serviceId())
        .withServiceVersion(SERVICE_VERSION.serviceVersion())
        .withTtl(MtMessage.Ttl.valueOf(TTL.name()))
        .withVehiclePlatformId(VPI.toString())
        .withTrackingId(TrackingIdentifier.create().toString());
  }

  public static MtStatusMessage createMtStatusMessage() {
    MtStatusMessage mtStatusMessage = new MtStatusMessage();
    mtStatusMessage.setCorrelationId(CORRELATION_ID.toString());
    mtStatusMessage.setHandle("HANDLE");
    mtStatusMessage.setStatus("DELIVERED");
    mtStatusMessage.setVehiclePlatformId(VPI.toString());
    return mtStatusMessage;
  }

  public static MtStatusReplyOption createMtStatusReplyOption() {
    MtStatusReplyOption mtStatusReplyOption = new MtStatusReplyOption();
    mtStatusReplyOption.setCorrelationId(CORRELATION_ID.toString());
    mtStatusReplyOption.setReplyDestination(REPLY_TO.toString());
    return mtStatusReplyOption;
  }

  public static ProcessableMtMessage createProcessableMtMessage(TrackingIdentifier trackingIdentifier, TelematicUnitType telematicUnitType) {
    return createProcessableMtMessage(trackingIdentifier, TestUtils.createVehicleInformationEntity(true, telematicUnitType));
  }

  public static ProcessableMtMessage createProcessableMtMessage() {
    return createProcessableMtMessage(TestUtils.TRACKING_ID);
  }

  public static ProcessableMtMessage createProcessableMtMessage(TrackingIdentifier trackingIdentifier) {
    return createProcessableMtMessage(trackingIdentifier, TelematicUnitType.TGW);
  }

  public static ProcessableMtStatus createProcessableMtStatus(Status status) {
    return new ProcessableMtStatus(CORRELATION_ID, DESCRIPTION, status, TRACKING_ID, SERVICE_FUNCTION, SERVICE_ID, SERVICE_VERSION, Optional.empty(),
        Optional.of(VPI), MtMessageType.INTERNAL_MT_MESSAGE);
  }

  public static ReceivedMtMessage createReceivedMtMessage(TrackingIdentifier trackingIdentifier) {
    return new ReceivedMtMessage(AcceptedCost.valueOf(ACCEPTED_COST.name()), VPI.toString(), AssetIdentifierType.VPI, Optional.of(CORRELATION_ID),
        SERVICE_FUNCTION, OVERRIDE, ImmutableByteArray.of(Base64.getDecoder().decode(PAYLOAD)), Optional.empty(),
        Optional.of(Priority.valueOf(PRIORITY.name())), SERVICE_ACCESS_TOKEN, SERVICE_ID, SERVICE_VERSION, trackingIdentifier, TTL,
        MtMessageType.INTERNAL_MT_MESSAGE);
  }

  public static ReceivedMtStatus createReceivedMtStatus() {
    return createReceivedMtStatusBuilder().build();
  }

  public static ReceivedMtStatus.Builder createReceivedMtStatusBuilder() {
    return new ReceivedMtStatus.Builder().setCorrelationId(CORRELATION_ID)
        .setDescription(DESCRIPTION)
        .setStatus(com.volvo.tisp.mtdisps.impl.dto.Status.DELIVERED)
        .setTrackingIdentifier(TRACKING_ID);
  }

  public static com.wirelesscar.tce.api.v2.MtMessage createTgwMtMessage() {
    com.wirelesscar.tce.api.v2.MtMessage mtMessage = new com.wirelesscar.tce.api.v2.MtMessage();
    mtMessage.setClientId(SERVICE_ID.serviceId() + "-" + SERVICE_VERSION.serviceVersion());
    mtMessage.setVehiclePlatformId(VPI.toString());
    mtMessage.setPayload(Base64.getDecoder().decode(PAYLOAD));
    mtMessage.setSchedulerOption(createSchedulerOption());
    mtMessage.setSrpOption(createSrpOption());
    mtMessage.setMtStatusReplyOption(createMtStatusReplyOption());
    return mtMessage;
  }

  public static VehicleInformation createVehicleInformation(PartNumber partNumber, SerialNumber serialNumber) {
    return createVehicleInformationBuilder(partNumber, serialNumber, TELEMATIC_UNIT_TYPE).build();
  }

  public static VehicleInformation createVehicleInformation(PartNumber partNumber, SerialNumber serialNumber, TelematicUnitType telematicUnitType) {
    return createVehicleInformationBuilder(partNumber, serialNumber, telematicUnitType).build();
  }

  public static VehicleInformation createVehicleInformation() {
    return createVehicleInformationBuilder(PART_NUMBER, SERIAL_NUMBER, TELEMATIC_UNIT_TYPE).build();
  }

  public static VehicleInformation.Builder createVehicleInformationBuilder(PartNumber partNumber, SerialNumber serialNumber,
      TelematicUnitType telematicUnitType) {
    return new VehicleInformation.Builder().setChassisId(CHASSIS_ID)
        .setOperationalStatus(OperationalStatus.OPERATIONAL)
        .setAssetHardwareId(AssetHardwareId.of(partNumber.value(), serialNumber.value()))
        .setTelematicUnitSubType(TELEMATIC_UNIT_SUB_TYPE)
        .setTelematicUnitType(telematicUnitType)
        .setVersion(VERSION)
        .setVpi(VPI);
  }

  public static VehicleInformation.Builder createVehicleInformationBuilder() {
    return new VehicleInformation.Builder().setChassisId(CHASSIS_ID)
        .setOperationalStatus(OperationalStatus.OPERATIONAL)
        .setAssetHardwareId(AssetHardwareId.of(PART_NUMBER.value(), SERIAL_NUMBER.value()))
        .setTelematicUnitSubType(TELEMATIC_UNIT_SUB_TYPE)
        .setTelematicUnitType(TELEMATIC_UNIT_TYPE)
        .setVersion(VERSION)
        .setVpi(VPI);
  }

  public static VehicleInformationEntity createVehicleInformationEntity(boolean operationalStatus, TelematicUnitType telematicUnitType) {
    VehicleInformationEntity vehicleInformationEntity = new VehicleInformationEntity();
    Instant now = Clock.systemUTC().instant();

    vehicleInformationEntity.setCreatedAt(now);
    vehicleInformationEntity.setOperational(operationalStatus);
    vehicleInformationEntity.setPartNumber(PART_NUMBER.value());
    vehicleInformationEntity.setSerialNumber(SERIAL_NUMBER.value());
    vehicleInformationEntity.setSoftCar(false);
    vehicleInformationEntity.setTelematicUnitSubType(TELEMATIC_UNIT_SUB_TYPE.telematicUnitSubType());
    vehicleInformationEntity.setTelematicUnitType(telematicUnitType);
    vehicleInformationEntity.setVersion(VERSION.version());
    vehicleInformationEntity.setVpi(VPI.toString());
    vehicleInformationEntity.setChassisId(CHASSIS_ID.value());
    vehicleInformationEntity.setUpdatedAt(now);

    return vehicleInformationEntity;
  }

  public static VehicleInformationEntity createVehicleInformationEntity(boolean operationalStatus) {
    return createVehicleInformationEntity(operationalStatus, TELEMATIC_UNIT_TYPE);
  }

  public static VqvNotification createVqvNotification() {
    return createVqvNotificationBuilder(PART_NUMBER, SERIAL_NUMBER, TELEMATIC_UNIT_TYPE).build();
  }

  public static VqvNotification createVqvNotification(TelematicUnitType telematicUnitType) {
    return createVqvNotificationBuilder(PART_NUMBER, SERIAL_NUMBER, telematicUnitType).build();
  }

  public static VqvNotification.Builder createVqvNotificationBuilder(PartNumber partNumber, SerialNumber serialNumber, TelematicUnitType telematicUnitType) {
    return new VqvNotification.Builder().setNotificationType(NotificationType.UPDATED)
        .setVehicleInformation(createVehicleInformation(partNumber, serialNumber, telematicUnitType))
        .setVpi(VPI);
  }

  public static VqvNotification.Builder createVqvNotificationBuilder() {
    return new VqvNotification.Builder().setNotificationType(NotificationType.UPDATED)
        .setVehicleInformation(createVehicleInformation(PART_NUMBER, SERIAL_NUMBER))
        .setVpi(VPI);
  }

  public static LocalStackContainer getSQSLocalStackContainer() {
    LocalStackContainer sqsLocalStackContainer = new LocalStackContainer(DockerImageName.parse("localstack/localstack:latest")).withServices(
        LocalStackContainer.Service.SQS);
    System.setProperty("aws.accessKeyId", "FAKE");
    System.setProperty("aws.secretAccessKey", "FAKE");
    System.setProperty("aws.region", "us-east-1");
    sqsLocalStackContainer.start();
    return sqsLocalStackContainer;
  }

  public static SqsClientConfigProperties getSqsClientConfigProperties(LocalStackContainer sqsLocalStackContainer) {
    return new SqsClientConfigProperties(sqsLocalStackContainer.getRegion(),
        sqsLocalStackContainer.getEndpointOverride(LocalStackContainer.Service.SQS).toString());
  }

  public static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(3), Instant.ofEpochSecond(5));
    return clock;
  }

  private static ProcessableMtMessage createProcessableMtMessage(TrackingIdentifier trackingIdentifier, VehicleInformationEntity vehicleInformationEntity) {
    return new ProcessableMtMessage(VehicleInformation.fromEntity(vehicleInformationEntity), AcceptedCost.valueOf(ACCEPTED_COST.name()),
        Optional.of(CORRELATION_ID), SERVICE_FUNCTION, OVERRIDE, ImmutableByteArray.of(Base64.getDecoder().decode(PAYLOAD)), Optional.empty(),
        Optional.of(Priority.valueOf(PRIORITY.name())), SERVICE_ACCESS_TOKEN, SERVICE_ID, SERVICE_VERSION, trackingIdentifier, TTL,
        MtMessageType.INTERNAL_MT_MESSAGE);
  }

  private static SchedulerOption createSchedulerOption() {
    SchedulerOption schedulerOption = new SchedulerOption();
    schedulerOption.setEnqueueingType(ENQUEUEING_TYPE);
    schedulerOption.setHint(new StringJoiner("-").add(TTL.name()).add(PRIORITY.name()).toString());
    schedulerOption.setPriority(4);
    schedulerOption.setQueueId(new StringJoiner("-").add(SERVICE_ID + "-" + SERVICE_VERSION).add(SERVICE_FUNCTION.value()).toString());
    return schedulerOption;
  }

  private static SrpOption createSrpOption() {
    SrpOption srpOption = new SrpOption();
    srpOption.setDstService(SERVICE_ID.serviceId());
    srpOption.setDstVersion(SERVICE_VERSION.serviceVersion());
    srpOption.setPriority(4);
    srpOption.setSrpLevel(SRP_LEVEL);
    return srpOption;
  }
}
