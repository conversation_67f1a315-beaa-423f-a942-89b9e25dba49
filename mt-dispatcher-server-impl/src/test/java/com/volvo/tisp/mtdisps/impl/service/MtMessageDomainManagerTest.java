package com.volvo.tisp.mtdisps.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.dao.DuplicateKeyException;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.impl.conf.AppProperties;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.reporter.Crud;
import com.volvo.tisp.mtdisps.impl.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.mtdisps.impl.service.processor.TgwMtMessageProcessor;
import com.volvo.tisp.mtdisps.impl.service.processor.VcmMtMessageProcessor;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtMessageDomainManagerTest {
  private static final Duration TTL_MARGIN = Duration.ofSeconds(10);

  @Test
  void processMessageExceptionWhileInsertTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MessageTransactionParametersRepository messageTransactionParametersRepository = Mockito.mock(MessageTransactionParametersRepository.class);
    TgwMtMessageProcessor tgwMtMessageProcessor = Mockito.mock(TgwMtMessageProcessor.class);
    VcmMtMessageProcessor vcmMtMessageProcessor = Mockito.mock(VcmMtMessageProcessor.class);
    AppProperties appProperties = Mockito.mock(AppProperties.class);
    Mockito.when(appProperties.getTtlMargin()).thenReturn(TTL_MARGIN);

    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage(TrackingIdentifier.create());
    Mockito.when(messageTransactionParametersRepository.insert(ArgumentMatchers.any(MessageTransactionParametersEntity.class)))
        .thenThrow(new DuplicateKeyException("Simulated Test"));
    Mockito.when(tgwMtMessageProcessor.process(processableMtMessage)).thenReturn(CompletableFuture.completedFuture(null));

    MtMessageDomainManager mtMessageDomainManager = new MtMessageDomainManager(appProperties, clock, dbOperationsMetricReporter,
        messageTransactionParametersRepository, tgwMtMessageProcessor, vcmMtMessageProcessor);
    CompletableFuture<Void> result = mtMessageDomainManager.processMessage(processableMtMessage);

    Awaitility.await().atMost(Duration.ofSeconds(2)).untilAsserted(() -> Assertions.assertThat(result).isCompletedExceptionally());
    Mockito.verify(appProperties).getTtlMargin();
    Mockito.verify(messageTransactionParametersRepository).insert(ArgumentMatchers.any(MessageTransactionParametersEntity.class));
    Mockito.verify(messageTransactionParametersRepository).deleteById(processableMtMessage.correlationId().toString());
    Mockito.verify(dbOperationsMetricReporter).onMessageTransactionCrudDuration(Duration.ofSeconds(0), Crud.DELETE);
    Mockito.verify(dbOperationsMetricReporter).onMessageTransactionCrudFailure(Crud.CREATE);
    Mockito.verifyNoInteractions(tgwMtMessageProcessor);
    Mockito.verifyNoMoreInteractions(appProperties, dbOperationsMetricReporter, messageTransactionParametersRepository, vcmMtMessageProcessor);

  }

  @Test
  void processMessageInvalidTest() {
    AssertThrows.illegalArgumentException(
        () -> new MtMessageDomainManager(Mockito.mock(AppProperties.class), Mockito.mock(Clock.class), Mockito.mock(DbOperationsMetricReporter.class),
            Mockito.mock(MessageTransactionParametersRepository.class), Mockito.mock(TgwMtMessageProcessor.class),
            Mockito.mock(VcmMtMessageProcessor.class)).processMessage(null), "processableMtMessage must not be null");
  }

  @Test
  void processMessageTgwFlowTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MessageTransactionParametersRepository messageTransactionParametersRepository = Mockito.mock(MessageTransactionParametersRepository.class);
    TgwMtMessageProcessor tgwMtMessageProcessor = Mockito.mock(TgwMtMessageProcessor.class);
    VcmMtMessageProcessor vcmMtMessageProcessor = Mockito.mock(VcmMtMessageProcessor.class);
    AppProperties appProperties = Mockito.mock(AppProperties.class);
    Mockito.when(appProperties.getTtlMargin()).thenReturn(TTL_MARGIN);

    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage(TrackingIdentifier.create(), TelematicUnitType.TGW);

    Mockito.when(tgwMtMessageProcessor.process(processableMtMessage)).thenReturn(CompletableFuture.completedFuture(null));

    MtMessageDomainManager mtMessageDomainManager = new MtMessageDomainManager(appProperties, clock, dbOperationsMetricReporter,
        messageTransactionParametersRepository, tgwMtMessageProcessor, vcmMtMessageProcessor);
    mtMessageDomainManager.processMessage(processableMtMessage);

    Mockito.verify(appProperties).getTtlMargin();
    Mockito.verify(tgwMtMessageProcessor).process(processableMtMessage);
    Mockito.verify(dbOperationsMetricReporter).onMessageTransactionCrudDuration(Duration.ofSeconds(0), Crud.CREATE);
    Mockito.verify(messageTransactionParametersRepository).insert(ArgumentMatchers.any(MessageTransactionParametersEntity.class));
    Mockito.verifyNoMoreInteractions(appProperties, dbOperationsMetricReporter, vcmMtMessageProcessor, tgwMtMessageProcessor);
  }

  @Test
  void processMessageVcmFlowTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MessageTransactionParametersRepository messageTransactionParametersRepository = Mockito.mock(MessageTransactionParametersRepository.class);
    TgwMtMessageProcessor tgwMtMessageProcessor = Mockito.mock(TgwMtMessageProcessor.class);
    VcmMtMessageProcessor vcmMtMessageProcessor = Mockito.mock(VcmMtMessageProcessor.class);
    AppProperties appProperties = Mockito.mock(AppProperties.class);
    Mockito.when(appProperties.getTtlMargin()).thenReturn(TTL_MARGIN);
    Mockito.when(appProperties.isIntegrationLoggingEnabled()).thenReturn(true);

    ProcessableMtMessage processableMtMessage = TestUtils.createProcessableMtMessage(TrackingIdentifier.create(), TelematicUnitType.VCM);

    Mockito.when(vcmMtMessageProcessor.process(processableMtMessage)).thenReturn(CompletableFuture.completedFuture(null));

    MtMessageDomainManager mtMessageDomainManager = new MtMessageDomainManager(appProperties, clock, dbOperationsMetricReporter,
        messageTransactionParametersRepository, tgwMtMessageProcessor, vcmMtMessageProcessor);
    mtMessageDomainManager.processMessage(processableMtMessage);

    Mockito.verify(appProperties).getTtlMargin();
    Mockito.verify(vcmMtMessageProcessor).process(processableMtMessage);
    Mockito.verify(dbOperationsMetricReporter).onMessageTransactionCrudDuration(Duration.ofSeconds(0), Crud.CREATE);
    Mockito.verify(messageTransactionParametersRepository).insert(ArgumentMatchers.any(MessageTransactionParametersEntity.class));
    Mockito.verifyNoMoreInteractions(appProperties, dbOperationsMetricReporter, vcmMtMessageProcessor, tgwMtMessageProcessor);
  }
}
