package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.Optional;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.dto.ReceivedMtMessage;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;

class InternalReceivedMtMessageConverterTest {
  @Test
  void convert() {
    InternalReceivedMtMessageConverter internalReceivedMtMessageConverter = new InternalReceivedMtMessageConverter();
    Optional<ReceivedMtMessage> receivedMtMessageOptional = internalReceivedMtMessageConverter.convert(TestUtils.createMtMessage());
    Assertions.assertThat(receivedMtMessageOptional).isPresent();

    Assertions.assertThat(internalReceivedMtMessageConverter.convert("someMessage")).isEmpty();
  }

  @Test
  void convertWithoutOptionals() {
    InternalReceivedMtMessageConverter internalReceivedMtMessageConverter = new InternalReceivedMtMessageConverter();
    MtMessage mtMessage = TestUtils.createMtMessageBase();

    Assertions.assertThat(internalReceivedMtMessageConverter.convert(mtMessage)).isPresent();

    mtMessage.withCorrelationId(TestUtils.CORRELATION_ID.toString());
    Assertions.assertThat(internalReceivedMtMessageConverter.convert(mtMessage)).isPresent();

    mtMessage.withPriority(TestUtils.PRIORITY);
    Assertions.assertThat(internalReceivedMtMessageConverter.convert(mtMessage)).isPresent();

    mtMessage.withOverride(true);
    Assertions.assertThat(internalReceivedMtMessageConverter.convert(mtMessage)).isPresent();

    mtMessage.withPayloadSignature(TestUtils.PAYLOAD);
    Assertions.assertThat(internalReceivedMtMessageConverter.convert(mtMessage).orElseThrow().payloadSignatureDetails()).isEmpty();

    mtMessage.withKeyId(TestUtils.KEY_ID);
    Assertions.assertThat(internalReceivedMtMessageConverter.convert(mtMessage).orElseThrow().payloadSignatureDetails()).isPresent();
  }
}
