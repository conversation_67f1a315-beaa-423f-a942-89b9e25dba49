package com.volvo.tisp.mtdisps.impl.reporter;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.utils.MetricsReporterTestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.Tags;

class MessagePublisherMetricReporterTest {
  @Test
  void onMessagePublishDurationTest() {
    MetricsReporterTestUtils.initReporterAndTest(MessagePublisherMetricReporter::new, (meterRegistry, messagePublisherMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> messagePublisherMetricReporter.onMessagePublishDuration(null, FlowType.TGW),
          "duration must not be null");
      AssertThrows.illegalArgumentException(() -> messagePublisherMetricReporter.onMessagePublishDuration(Duration.ofSeconds(1), null),
          "flowType must not be null");
      AssertThrows.illegalArgumentException(() -> messagePublisherMetricReporter.onMessagePublishDuration(Duration.ofMillis(-1), FlowType.TGW),
          "duration must not be negative: PT-0.001S");

      messagePublisherMetricReporter.onMessagePublishDuration(Duration.ofSeconds(1), FlowType.TGW);
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.publish", Tags.of("flow.type", FlowType.TGW.name()), Duration.ofSeconds(1), 1);

      messagePublisherMetricReporter.onMessagePublishDuration(Duration.ofSeconds(2), FlowType.TGW);
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.publish", Tags.of("flow.type", FlowType.TGW.name()), Duration.ofSeconds(3), 2);

      messagePublisherMetricReporter.onMessagePublishDuration(Duration.ofSeconds(1), FlowType.TGW);
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.publish", Tags.of("flow.type", FlowType.TGW.name()), Duration.ofSeconds(4), 3);
    });
  }

  @Test
  void onMtMessageFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(MessagePublisherMetricReporter::new, (meterRegistry, messagePublisherMetricReporter) -> {
      messagePublisherMetricReporter.onMessagePublishFailure(FlowType.VCM);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.publish.failed", Tags.of("flow.type", FlowType.VCM.name()), 1);

      messagePublisherMetricReporter.onMessagePublishFailure(FlowType.VCM);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.publish.failed", Tags.of("flow.type", FlowType.VCM.name()), 2);
    });
  }
}