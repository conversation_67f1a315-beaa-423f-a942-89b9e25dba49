package com.volvo.tisp.mtdisps.impl.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.utils.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

class SqsPersisterMetricReporterTest {
  private static final String COMPLETE = "complete";
  private static final String EXCEPTION = "exception";
  private static final String METRIC_NAME = "message-handler-sqs-published";
  private static final String STATE = "state";
  private static final String STATUS = "status";
  private static final String SUCCESS = "success";

  @Test
  void onExceptionalStatusTest() {
    Tags tags = Tags.of(Tag.of(STATE, COMPLETE), Tag.of(STATUS, EXCEPTION));

    MetricsReporterTestUtils.initReporterAndTest(SqsPersisterMetricReporter::new, (meterRegistry, sqsPersisterMetricReporter) -> {
      sqsPersisterMetricReporter.onExceptionalStatus();
      MetricsReporterTestUtils.checkCounter(meterRegistry, METRIC_NAME, tags, 1);
    });
  }

  @Test
  void onSuccessfulStatusTest() {
    Tags tags = Tags.of(Tag.of(STATE, COMPLETE), Tag.of(STATUS, SUCCESS));

    MetricsReporterTestUtils.initReporterAndTest(SqsPersisterMetricReporter::new, (meterRegistry, sqsPersisterMetricReporter) -> {
      sqsPersisterMetricReporter.onSuccessfulStatus();
      MetricsReporterTestUtils.checkCounter(meterRegistry, METRIC_NAME, tags, 1);
    });
  }
}
