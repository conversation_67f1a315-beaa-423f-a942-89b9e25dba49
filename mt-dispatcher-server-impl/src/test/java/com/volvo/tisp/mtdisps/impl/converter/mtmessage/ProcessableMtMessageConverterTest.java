package com.volvo.tisp.mtdisps.impl.converter.mtmessage;

import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.dto.AssetIdentifierType;
import com.volvo.tisp.mtdisps.impl.dto.ProcessableMtMessage;
import com.volvo.tisp.mtdisps.impl.model.MultipleAssetsFoundException;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.service.ValidationException;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;

class ProcessableMtMessageConverterTest {
  @Test
  void convert() throws ValidationException, MultipleAssetsFoundException {
    VehicleInformationFetcher vehicleInformationFetcher = Mockito.mock(VehicleInformationFetcher.class);
    Mockito.when(vehicleInformationFetcher.getSupportedAssetIdentifierType()).thenReturn(AssetIdentifierType.VPI);
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    Mockito.when(vehicleInformationFetcher.fetch(TestUtils.VPI.toString(), true)).thenReturn(Optional.of(VehicleInformation.fromEntity(vehicleInformationEntity)));
    ProcessableMtMessageConverter processableMtMessageConverter = new ProcessableMtMessageConverter(List.of(vehicleInformationFetcher));

    ProcessableMtMessage processableMtMessage = processableMtMessageConverter.convert(TestUtils.createReceivedMtMessage(TrackingIdentifier.create()));

    Assertions.assertEquals(processableMtMessage.vehicleInformation(), VehicleInformation.fromEntity(vehicleInformationEntity));
    Assertions.assertEquals(processableMtMessage.correlationId(), TestUtils.createReceivedMtMessage(TrackingIdentifier.create()).correlationId());
    Mockito.verify(vehicleInformationFetcher).getSupportedAssetIdentifierType();
    Mockito.verify(vehicleInformationFetcher).fetch(TestUtils.VPI.toString(), true);
    Mockito.verifyNoMoreInteractions(vehicleInformationFetcher);
  }

  @Test
  void convertFailureTest() throws MultipleAssetsFoundException {
    VehicleInformationFetcher vehicleInformationFetcher = Mockito.mock(VehicleInformationFetcher.class);
    Mockito.when(vehicleInformationFetcher.getSupportedAssetIdentifierType()).thenReturn(AssetIdentifierType.VPI);
    Mockito.when(vehicleInformationFetcher.fetch(TestUtils.VPI.toString(), true)).thenReturn(Optional.empty());
    ProcessableMtMessageConverter processableMtMessageConverter = new ProcessableMtMessageConverter(List.of(vehicleInformationFetcher));

    Assertions.assertThrows(ValidationException.class,
        () -> processableMtMessageConverter.convert(TestUtils.createReceivedMtMessage(TrackingIdentifier.create())));

    Mockito.verify(vehicleInformationFetcher).getSupportedAssetIdentifierType();
    Mockito.verify(vehicleInformationFetcher).fetch(TestUtils.VPI.toString(), true);
    Mockito.verifyNoMoreInteractions(vehicleInformationFetcher);
  }

  @Test
  void multipleAssetsForAGivenAssetIdentifierTest() throws MultipleAssetsFoundException {
    VehicleInformationFetcher vehicleInformationFetcher = Mockito.mock(VehicleInformationFetcher.class);
    Mockito.when(vehicleInformationFetcher.getSupportedAssetIdentifierType()).thenReturn(AssetIdentifierType.VPI);
    Mockito.when(vehicleInformationFetcher.fetch(TestUtils.VPI.toString(), true)).thenThrow(MultipleAssetsFoundException.class);
    ProcessableMtMessageConverter processableMtMessageConverter = new ProcessableMtMessageConverter(List.of(vehicleInformationFetcher));

    Assertions.assertThrows(ValidationException.class,
        () -> processableMtMessageConverter.convert(TestUtils.createReceivedMtMessage(TrackingIdentifier.create())));

    Mockito.verify(vehicleInformationFetcher).getSupportedAssetIdentifierType();
    Mockito.verify(vehicleInformationFetcher).fetch(TestUtils.VPI.toString(), true);
    Mockito.verifyNoMoreInteractions(vehicleInformationFetcher);
  }
}