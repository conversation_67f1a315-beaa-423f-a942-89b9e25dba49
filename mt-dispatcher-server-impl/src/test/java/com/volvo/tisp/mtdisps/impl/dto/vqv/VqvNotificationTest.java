package com.volvo.tisp.mtdisps.impl.dto.vqv;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.vqv.v1.api.NotificationType;

class VqvNotificationTest {
  @Test
  void builderTest() {
    VqvNotification.Builder builder = new VqvNotification.Builder();
    AssertThrows.illegalArgumentException(builder::build, "notificationType must not be null");

    builder.setNotificationType(NotificationType.UPDATED);
    AssertThrows.illegalArgumentException(builder::build, "vehicleInformation must not be null");

    builder.setVehicleInformation(TestUtils.createVehicleInformation());
    AssertThrows.illegalArgumentException(builder::build, "vpi must not be null");

    builder.setVpi(TestUtils.VPI);
    Assertions.assertEquals(TestUtils.createVqvNotification(), builder.build());
  }

  @Test
  void constructorTest() {
    VehicleInformation vehicleInformation = TestUtils.createVehicleInformation();

    AssertThrows.illegalArgumentException(() -> new VqvNotification(TestUtils.INSTANT, null, vehicleInformation, TestUtils.VPI),
        "notificationType must not be null");
    AssertThrows.illegalArgumentException(() -> new VqvNotification(TestUtils.INSTANT, NotificationType.UPDATED, null, TestUtils.VPI),
        "vehicleInformation must not be null");
    AssertThrows.illegalArgumentException(() -> new VqvNotification(TestUtils.INSTANT, NotificationType.UPDATED, vehicleInformation, null),
        "vpi must not be null");

    Assertions.assertDoesNotThrow(() -> new VqvNotification(null, NotificationType.UPDATED, vehicleInformation, TestUtils.VPI));
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals(
        "masterDataDeletedTimestamp={null}, notificationType={UPDATED}, vehicleInformation={chassisId={Optional[SCAR-205045]}, operationalStatus={OPERATIONAL}, assetHardwareId={456-123}, softCar={false}, telematicUnitSubType={Optional[TGW3]}, telematicUnitType={TGW}, version={1}, vpi={Optional[1FAC35BF1EDB1DFF91AAC0BEBC56F22B]}}, vpi={1FAC35BF1EDB1DFF91AAC0BEBC56F22B}",
        TestUtils.createVqvNotification().toString());
  }
}