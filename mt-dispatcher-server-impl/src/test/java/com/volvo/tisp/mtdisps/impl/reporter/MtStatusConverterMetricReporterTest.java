package com.volvo.tisp.mtdisps.impl.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.utils.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class MtStatusConverterMetricReporterTest {

  @Test
  void onMissingVpiTest() {
    MetricsReporterTestUtils.initReporterAndTest(InternalMtStatusConverterMetricReporter::new, (meterRegistry, mtStatusConverterMetricReporter) -> {
      mtStatusConverterMetricReporter.onMissingVpi();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "internal-mt-status-converter", Tags.of("missing", "vpi"), 1);

      mtStatusConverterMetricReporter.onMissingVpi();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "internal-mt-status-converter", Tags.of("missing", "vpi"), 2);
    });
  }
}
