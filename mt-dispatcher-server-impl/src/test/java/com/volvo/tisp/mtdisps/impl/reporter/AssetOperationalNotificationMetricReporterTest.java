package com.volvo.tisp.mtdisps.impl.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.impl.model.ValidationFailureReason;
import com.volvo.tisp.mtdisps.impl.utils.MetricsReporterTestUtils;

class AssetOperationalNotificationMetricReporterTest {
  private static final String NAME = "va.operational-status";

  @Test
  void onConversionFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(AssetOperationalNotificationMetricReporter::new,
        (meterRegistry, assetOperationalNotificationMetricReporter) -> {
          assetOperationalNotificationMetricReporter.onFailure(ValidationFailureReason.CONVERSION_FAILURE);
          MetricsReporterTestUtils.checkCounter(meterRegistry, NAME + ".failure", 1);

          assetOperationalNotificationMetricReporter.onFailure(ValidationFailureReason.CONVERSION_FAILURE);
          MetricsReporterTestUtils.checkCounter(meterRegistry, NAME + ".failure", 2);
        });
  }

  @Test
  void onFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(AssetOperationalNotificationMetricReporter::new,
        (meterRegistry, assetOperationalNotificationMetricReporter) -> {
          assetOperationalNotificationMetricReporter.onFailure(ValidationFailureReason.ASSET_NOT_FOUND);
          MetricsReporterTestUtils.checkCounter(meterRegistry, NAME + ".failure", 1);

          assetOperationalNotificationMetricReporter.onFailure(ValidationFailureReason.ASSET_NOT_FOUND);
          MetricsReporterTestUtils.checkCounter(meterRegistry, NAME + ".failure", 2);
        });
  }
}