<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.volvo.tisp.vehiclepingservice</groupId>
    <artifactId>vehicle-ping-service-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>vehicle-ping-service-server-api-openapi-impl</artifactId>
  <name>Vehicle Ping Service :: Server :: API</name>
  <description>Server REST API</description>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>
    <!-- Platform -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework</artifactId>
    </dependency>
    <!--OpenApi generator-->
    <dependency>
      <groupId>io.swagger.parser.v3</groupId>
      <artifactId>swagger-parser-v3</artifactId>
    </dependency>
    <dependency>
      <groupId>org.openapitools</groupId>
      <artifactId>jackson-databind-nullable</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.spotbugs</groupId>
      <artifactId>spotbugs-annotations</artifactId>
    </dependency>

  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.openapitools</groupId>
        <artifactId>openapi-generator-maven-plugin</artifactId>
        <!--<version>${openapi-generator-maven-plugin.version}</version>-->

        <dependencies>
          <dependency>
            <groupId>com.volvo.tisp.vehiclepingservice</groupId>
            <artifactId>vehicle-ping-service-client-api</artifactId>
            <version>${vehicle-ping-service-client.version}</version>
          </dependency>
        </dependencies>

        <executions>
          <execution>
            <goals>
              <goal>generate</goal>
            </goals>
            <configuration>
              <inputSpec>openapi/vehicle-ping-server-api.json</inputSpec>
              <generatorName>spring</generatorName>
              <library>spring-boot</library>
              <generateApiTests>false</generateApiTests>
              <generateModelTests>false</generateModelTests>
              <generateSupportingFiles>false</generateSupportingFiles>
              <configOptions>
                <async>true</async>
                <dateLibrary>java8</dateLibrary>
                <useSpringBoot3>true</useSpringBoot3>
                <interfaceOnly>true</interfaceOnly>
                <skipDefaultInterface>true</skipDefaultInterface>
                <basePackage>com.volvo.tisp.vehiclepingservice</basePackage>
                <modelPackage>com.volvo.tisp.vehiclepingservice.rest.model</modelPackage>
                <apiPackage>com.volvo.tisp.vehiclepingservice.rest.api</apiPackage>
                <invokerPackage>com.volvo.tisp.vehiclepingservice.rest.client</invokerPackage>
              </configOptions>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
