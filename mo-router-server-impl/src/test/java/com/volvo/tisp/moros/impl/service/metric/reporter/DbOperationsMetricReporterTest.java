package com.volvo.tisp.moros.impl.service.metric.reporter;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.utils.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class DbOperationsMetricReporterTest {
  private static final String CRUD = "CRUD";
  private static final String DB_VEHICLE_INFORMATION = "db.vehicle-information";
  private static final Duration DEFAULT_DURATION = Duration.ofSeconds(1);
  private static final String IDENTIFIER = "IDENTIFIER";
  private static final String STATUS = "STATUS";
  private static final Duration TOTAL_DURATION = Duration.ofSeconds(2);

  @Test
  void onCrudDurationTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbOperationsMetricReporter::new, (meterRegistry, dbOperationsMetricReporter) -> {
      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.READ);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.CREATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.CREATE.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.UPDATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.UPDATE.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.DELETE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.DELETE.name()), DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.READ);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name()), TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.CREATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.CREATE.name()), TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.UPDATE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.UPDATE.name()), TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.DELETE);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.DELETE.name()), TOTAL_DURATION, 2);
    });
  }

  @Test
  void onCrudDurationWithIdentifierTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbOperationsMetricReporter::new, (meterRegistry, dbOperationsMetricReporter) -> {
      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.READ, Identifier.ASSET_HARDWARE_ID);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name(), IDENTIFIER, Identifier.ASSET_HARDWARE_ID.name()),
          DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.READ, Identifier.VPI);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name(), IDENTIFIER, Identifier.ASSET_HARDWARE_ID.name()),
          DEFAULT_DURATION, 1);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.READ, Identifier.ASSET_HARDWARE_ID);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name(), IDENTIFIER, Identifier.ASSET_HARDWARE_ID.name()),
          TOTAL_DURATION, 2);

      dbOperationsMetricReporter.onCrudDuration(DEFAULT_DURATION, Crud.READ, Identifier.VPI);
      MetricsReporterTestUtils.checkTimer(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name(), IDENTIFIER, Identifier.ASSET_HARDWARE_ID.name()),
          TOTAL_DURATION, 2);
    });
  }

  @Test
  void onCrudFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbOperationsMetricReporter::new, (meterRegistry, dbOperationsMetricReporter) -> {
      dbOperationsMetricReporter.onCrudFailure(Crud.READ);
      MetricsReporterTestUtils.checkCounter(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name(), STATUS, "FAILURE"), 1);

      dbOperationsMetricReporter.onCrudFailure(Crud.READ);
      MetricsReporterTestUtils.checkCounter(meterRegistry, DB_VEHICLE_INFORMATION, Tags.of(CRUD, Crud.READ.name(), STATUS, "FAILURE"), 2);
    });
  }
}
