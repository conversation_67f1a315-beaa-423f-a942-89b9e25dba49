package com.volvo.tisp.moros.impl.conf;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.moros.impl.conf.properties.ConnectionPoolConfigProperties;

class AppConfigTest {

  @Test
  void createClockTest() {
    Assertions.assertNotNull(new AppConfig().createClock());
  }

  @Test
  void createMongoClientTest() {
    ConnectionPoolConfigProperties connectionPoolConfigProperties = new ConnectionPoolConfigProperties(10, 30, 100, 10, 10);

    Assertions.assertNotNull(new AppConfig().createMongoClient(connectionPoolConfigProperties));
  }

  @Test
  void createMongockConnectionTest() {
    Assertions.assertNotNull(new AppConfig().createMongockConnection(Mockito.mock(MongoTemplate.class)));
  }

  @Test
  void createObjectMapperTest() {
    Assertions.assertNotNull(new AppConfig().createObjectMapper());
  }

  @Test
  void createObjectWriterTest() {
    Assertions.assertNotNull(new AppConfig().createObjectWriter(new ObjectMapper()));
  }

  @Test
  void createViewRegistryTest() {
    Assertions.assertNotNull(new AppConfig().createViewRegistry(WebClient.builder()));
  }
}
