package com.volvo.tisp.moros.impl.model;

import java.util.Optional;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VehicleInformationTest {
  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(
        () -> new VehicleInformation(null, Optional.of(TestUtils.CHASSIS_ID), OperationalStatus.OPERATIONAL, TestUtils.VERSION, Optional.of(TestUtils.VPI)),
        "assetHardwareId must not be null");
    AssertThrows.illegalArgumentException(
        () -> new VehicleInformation(TestUtils.ASSET_HARDWARE_ID, null, OperationalStatus.OPERATIONAL, TestUtils.VERSION, Optional.of(TestUtils.VPI)),
        "chassisId must not be null");
    AssertThrows.illegalArgumentException(
        () -> new VehicleInformation(TestUtils.ASSET_HARDWARE_ID, Optional.of(TestUtils.CHASSIS_ID), null, TestUtils.VERSION, Optional.of(TestUtils.VPI)),
        "operationalStatus must not be null");
    AssertThrows.illegalArgumentException(
        () -> new VehicleInformation(TestUtils.ASSET_HARDWARE_ID, Optional.of(TestUtils.CHASSIS_ID), OperationalStatus.OPERATIONAL, null,
            Optional.of(TestUtils.VPI)),
        "version must not be null");
    AssertThrows.illegalArgumentException(
        () -> new VehicleInformation(TestUtils.ASSET_HARDWARE_ID, Optional.of(TestUtils.CHASSIS_ID), OperationalStatus.OPERATIONAL, TestUtils.VERSION,
            null), "vpi must not be null");
  }
}
