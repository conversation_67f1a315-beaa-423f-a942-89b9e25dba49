package com.volvo.tisp.moros.impl.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VersionTest {
  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new Version(-1), "value must not be negative: -1");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("0", new Version(0).toString());
  }
}
