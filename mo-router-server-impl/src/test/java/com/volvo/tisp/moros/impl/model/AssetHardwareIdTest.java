package com.volvo.tisp.moros.impl.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class AssetHardwareIdTest {
  private static final String PART_NUMBER_STRING = "000001";
  private static final String SERIAL_NUMBER_STRING = "000002";
  private static final AssetHardwareId ASSET_HARDWARE_ID = AssetHardwareId.of(PART_NUMBER_STRING, SERIAL_NUMBER_STRING);

  @Test
  void assetHardwareIdTest() {
    Assertions.assertEquals(ASSET_HARDWARE_ID, new AssetHardwareId(PART_NUMBER_STRING, SERIAL_NUMBER_STRING));
    AssertThrows.illegalArgumentException(() -> new AssetHardwareId(null, "serial"), "partNumber must not be null");
    AssertThrows.illegalArgumentException(() -> new AssetHardwareId("part", null), "serialNumber must not be null");
  }

  @Test
  void fromStringTest() {
    Assertions.assertEquals(ASSET_HARDWARE_ID, AssetHardwareId.fromString("000001-000002").orElseThrow());
    Assertions.assertTrue(AssetHardwareId.fromString("invalid").isEmpty());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("000001-000002", new AssetHardwareId(PART_NUMBER_STRING, SERIAL_NUMBER_STRING).toString());
  }
}
