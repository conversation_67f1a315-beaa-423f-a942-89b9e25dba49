package com.volvo.tisp.moros.impl.converter;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.utils.TestUtils;
import com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MoMessageOutputConverterTest {
  private static void verifyTimestamp(Instant expectedInstant, Long timestamp) {
    Assertions.assertEquals(expectedInstant.getEpochSecond(), timestamp);
  }

  @Test
  void applyInvalidTest() {
    AssertThrows.illegalArgumentException(() -> new MoMessageOutputConverter().apply(null), "processableMessage must not be null");
  }

  @Test
  void applyTest() {
    MoMessage moMessage = new MoMessageOutputConverter().apply(TestUtils.createProcessableMessage());
    Assertions.assertEquals(TestUtils.VPI.toString(), moMessage.getVehiclePlatformId());
    Assertions.assertEquals(TestUtils.SERVICE_ID.value(), moMessage.getServiceId());
    Assertions.assertEquals(TestUtils.SERVICE_VERSION.value(), moMessage.getServiceVersion());
    verifyTimestamp(TestUtils.ONBOARD_TIMESTAMP, moMessage.getOnboardTimestamp());
    verifyTimestamp(TestUtils.SERVER_TIMESTAMP, moMessage.getServerTimestamp());
    Assertions.assertEquals(TestUtils.SERVICE_ACCESS_TOKEN.toString(), moMessage.getServiceAccessToken());
    Assertions.assertEquals(TestUtils.TRACKING_IDENTIFIER.toString(), moMessage.getTrackingId());
    Assertions.assertArrayEquals(TestUtils.BASE64_PAYLOAD.getBytes(), moMessage.getPayload().getBytes());
    Assertions.assertEquals(TestUtils.KEY_ID, moMessage.getKeyId());
    Assertions.assertEquals(TestUtils.PAYLOAD_SIGNATURE, moMessage.getPayloadSignature());
  }
}
