package com.volvo.tisp.moros.impl.conf.properties;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class SqsListenerConfigPropertiesTest {
  @Test
  void validateTest() {
    AssertThrows.illegalArgumentException(() -> new SqsListenerConfigProperties(null, 50, 100, 10, 100, Duration.ofSeconds(30), Duration.ofSeconds(20)),
        "acknowledgementInterval must not be null");
    AssertThrows.illegalArgumentException(
        () -> new SqsListenerConfigProperties(Duration.ofSeconds(5), 0, 100, 10, 100, Duration.ofSeconds(30), Duration.ofSeconds(20)),
        "acknowledgementThreshold must be positive: 0");
    AssertThrows.illegalArgumentException(
        () -> new SqsListenerConfigProperties(Duration.ofSeconds(5), 50, -1, 10, 100, Duration.ofSeconds(30), Duration.ofSeconds(20)),
        "maxConcurrentMessages must be positive: -1");
    AssertThrows.illegalArgumentException(
        () -> new SqsListenerConfigProperties(Duration.ofSeconds(5), 50, 100, -1, 100, Duration.ofSeconds(30), Duration.ofSeconds(20)),
        "maxMessagePerPoll must be positive: -1");
    AssertThrows.illegalArgumentException(
        () -> new SqsListenerConfigProperties(Duration.ofSeconds(5), 50, 100, 10, 0, Duration.ofSeconds(30), Duration.ofSeconds(20)),
        "maxPoolSize must be positive: 0");
    AssertThrows.illegalArgumentException(() -> new SqsListenerConfigProperties(Duration.ofSeconds(5), 50, 100, 10, 100, null, Duration.ofSeconds(20)),
        "messageVisibility must not be null");
    AssertThrows.illegalArgumentException(() -> new SqsListenerConfigProperties(Duration.ofSeconds(5), 50, 100, 10, 100, Duration.ofSeconds(30), null),
        "pollTimeout must not be null");
  }
}
