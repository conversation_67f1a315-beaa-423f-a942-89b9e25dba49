package com.volvo.tisp.moros.impl.service.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.utils.MetricsReporterTestUtils;

class VqvNotificationMetricReporterTest {
  private static final String NAME = "vqv.conversion.failure";

  @Test
  void onConversionFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(VqvNotificationMetricReporter::new, (meterRegistry, vqvNotificationMetricReporter) -> {
      vqvNotificationMetricReporter.onConversionFailure();
      MetricsReporterTestUtils.checkCounter(meterRegistry, NAME, 1);

      vqvNotificationMetricReporter.onConversionFailure();
      MetricsReporterTestUtils.checkCounter(meterRegistry, NAME, 2);
    });
  }
}
