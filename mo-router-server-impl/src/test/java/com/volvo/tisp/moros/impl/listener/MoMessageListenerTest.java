package com.volvo.tisp.moros.impl.listener;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage;
import com.volvo.tisp.moros.impl.converter.ReceivedMoMessageConverter;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.moros.impl.service.IntegrationLogger;
import com.volvo.tisp.moros.impl.service.MoMessageDomainManager;
import com.volvo.tisp.moros.impl.service.metric.reporter.MoMessageMetricReporter;
import com.volvo.tisp.moros.impl.service.metric.reporter.Reason;
import com.volvo.tisp.moros.impl.utils.TestUtils;

import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import software.amazon.awssdk.services.sqs.model.Message;

class MoMessageListenerTest {
  private static Message mockMessage(MoMessage moMessage) {
    Message message = Mockito.mock(Message.class);
    Mockito.when(message.body()).thenReturn(moMessage.toString());
    return message;
  }

  @Test
  void onMessageJsonProcessingFailureTest() {
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    MoMessageDomainManager moMessageDomainManager = Mockito.mock(MoMessageDomainManager.class);
    MoMessageMetricReporter moMessageMetricReporter = Mockito.mock(MoMessageMetricReporter.class);
    ObjectMapper objectMapper = new ObjectMapper();
    ReceivedMoMessageConverter receivedMoMessageConverter = Mockito.mock(ReceivedMoMessageConverter.class);

    Acknowledgement acknowledgement = Mockito.mock(Acknowledgement.class);
    MoMessageListener moMessageListener = new MoMessageListener(integrationLogger, moMessageDomainManager,
        moMessageMetricReporter, objectMapper, receivedMoMessageConverter);

    MoMessage moMessage = TestUtils.createProtobufMoMessage();
    Message message = mockMessage(moMessage);

    moMessageListener.onMessage(message, acknowledgement);

    Awaitility.await().atMost(Duration.ofSeconds(3)).untilAsserted(() -> Mockito.verify(moMessageMetricReporter).onMoMessageFailure(Reason.CONVERSION_FAILURE));

    Mockito.verify(moMessageMetricReporter).onMoMessageV1Received();
    Mockito.verifyNoInteractions(receivedMoMessageConverter, acknowledgement, integrationLogger);
    Mockito.verifyNoMoreInteractions(moMessageMetricReporter, moMessageDomainManager);
  }

  @Test
  void onMessageTest() throws JsonProcessingException {
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    MoMessageDomainManager moMessageDomainManager = Mockito.mock(MoMessageDomainManager.class);
    MoMessageMetricReporter moMessageMetricReporter = Mockito.mock(MoMessageMetricReporter.class);
    ObjectMapper objectMapper = Mockito.mock(ObjectMapper.class);
    ReceivedMoMessageConverter receivedMoMessageConverter = Mockito.mock(ReceivedMoMessageConverter.class);
    Acknowledgement acknowledgement = Mockito.mock(Acknowledgement.class);

    MoMessageListener mtStatusListener = new MoMessageListener(integrationLogger, moMessageDomainManager,
        moMessageMetricReporter, objectMapper, receivedMoMessageConverter);

    MoMessage moMessage = TestUtils.createProtobufMoMessage();
    ReceivedMoMessage receivedMoMessage = TestUtils.createReceivedMoMessage();
    Message message = mockMessage(moMessage);

    Mockito.when(objectMapper.readValue(message.body(), MoMessage.class)).thenReturn(moMessage);
    Mockito.when(receivedMoMessageConverter.apply(moMessage)).thenReturn(receivedMoMessage);
    Mockito.doNothing().when(acknowledgement).acknowledge();
    Mockito.when(moMessageDomainManager.processMessage(receivedMoMessage)).thenReturn(CompletableFuture.completedFuture(null));

    mtStatusListener.onMessage(message, acknowledgement);

    Mockito.verify(moMessageMetricReporter).onMoMessageV1Received();
    Mockito.verify(integrationLogger).log(moMessage, "MoMessageReceived");
    Mockito.verify(integrationLogger).log(moMessage, "MoMessageProcessedSuccess");
    Mockito.verify(acknowledgement).acknowledgeAsync();
    Mockito.verify(moMessageDomainManager).processMessage(receivedMoMessage);
    Mockito.verify(receivedMoMessageConverter).apply(moMessage);
    Mockito.verifyNoMoreInteractions(integrationLogger, acknowledgement, moMessageDomainManager, receivedMoMessageConverter, moMessageMetricReporter);
  }
}
