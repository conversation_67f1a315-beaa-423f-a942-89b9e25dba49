package com.volvo.tisp.moros.impl.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.utils.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class ApplicationEventReporterTest {
  private static final String APPLICATION_EVENT = "app-event";
  private static final String TYPE = "type";

  @Test
  void onApplicationReadyEventTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onApplicationReadyEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "STARTED"), 1);

      mtStatusMetricReporter.onApplicationReadyEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "STARTED"), 2);
    });
  }

  @Test
  void onContextClosedTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onContextClosed();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "STOPPED"), 1);

      mtStatusMetricReporter.onContextClosed();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "STOPPED"), 2);
    });
  }
}
