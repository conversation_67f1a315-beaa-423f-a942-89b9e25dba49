package com.volvo.tisp.moros.impl.converter;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.utils.TestUtils;
import com.volvo.tisp.vc.mo.message.client.json.v1.MoMessageWithAssetHardwareId;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MoMessageWithAssetHardwareIdOutputConverterTest {
  private static void verifyTimestamp(Instant expectedInstant, Long timestamp) {
    Assertions.assertEquals(expectedInstant.getEpochSecond(), timestamp);
  }

  @Test
  void applyInvalidTest() {
    AssertThrows.illegalArgumentException(() -> new MoMessageWithAssetHardwareIdOutputConverter().apply(null), "processableMessage must not be null");
  }

  @Test
  void applyTest() {
    MoMessageWithAssetHardwareId moMessageWithAssetHardwareId = new MoMessageWithAssetHardwareIdOutputConverter().apply(TestUtils.createProcessableMessage());
    Assertions.assertEquals(TestUtils.ASSET_HARDWARE_ID.toString(), moMessageWithAssetHardwareId.getAssetHardwareId());
    Assertions.assertEquals(TestUtils.SERVICE_ID.value(), moMessageWithAssetHardwareId.getServiceId());
    Assertions.assertEquals(TestUtils.SERVICE_VERSION.value(), moMessageWithAssetHardwareId.getServiceVersion());
    verifyTimestamp(TestUtils.ONBOARD_TIMESTAMP, moMessageWithAssetHardwareId.getOnboardTimestamp());
    verifyTimestamp(TestUtils.SERVER_TIMESTAMP, moMessageWithAssetHardwareId.getServerTimestamp());
    Assertions.assertEquals(TestUtils.SERVICE_ACCESS_TOKEN.toString(), moMessageWithAssetHardwareId.getServiceAccessToken());
    Assertions.assertEquals(TestUtils.TRACKING_IDENTIFIER.toString(), moMessageWithAssetHardwareId.getTrackingId());
    Assertions.assertArrayEquals(TestUtils.BASE64_PAYLOAD.getBytes(), moMessageWithAssetHardwareId.getPayload().getBytes());
    Assertions.assertEquals(TestUtils.KEY_ID, moMessageWithAssetHardwareId.getKeyId());
    Assertions.assertEquals(TestUtils.PAYLOAD_SIGNATURE, moMessageWithAssetHardwareId.getPayloadSignature());
  }
}