package com.volvo.tisp.moros.impl.service.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.utils.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class MoMessageMetricReporterTest {

  @Test
  void onMoMessageFailure() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageMetricReporter::new, (meterRegistry, moMessageMetricReporter) -> {
      moMessageMetricReporter.onMoMessageFailure(Reason.PROCESS_FAILURE);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mo.message.failed", Tags.of("reason", "PROCESS_FAILURE"), 1);

      moMessageMetricReporter.onMoMessageFailure(Reason.PROCESS_FAILURE);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mo.message.failed", Tags.of("reason", "PROCESS_FAILURE"), 2);
    });
  }

  @Test
  void onMoMessageV1ReceivedTest() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageMetricReporter::new, (meterRegistry, moMessageMetricReporter) -> {
      moMessageMetricReporter.onMoMessageV1Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "sqs.received", Tags.of("type", "MO_MESSAGE-1.0"), 1);

      moMessageMetricReporter.onMoMessageV1Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "sqs.received", Tags.of("type", "MO_MESSAGE-1.0"), 2);
    });
  }
}