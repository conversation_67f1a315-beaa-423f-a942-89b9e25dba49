package com.volvo.tisp.moros.impl.service.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.model.ServiceId;
import com.volvo.tisp.moros.impl.utils.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class ServiceIdMetricReporterTest {

  @Test
  void onMoMessageSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceIdMetricReporter::new, (meterRegistry, serviceIdMetricReporter) -> {
      serviceIdMetricReporter.onMoMessageSuccess(new ServiceId(1));
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service", Tags.of("id", new ServiceId(1).toString()), 1);

      serviceIdMetricReporter.onMoMessageSuccess(new ServiceId(1));
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service", Tags.of("id", new ServiceId(1).toString()), 2);

      serviceIdMetricReporter.onMoMessageSuccess(new ServiceId(3));
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service", Tags.of("id", new ServiceId(3).toString()), 1);

    });
  }
}