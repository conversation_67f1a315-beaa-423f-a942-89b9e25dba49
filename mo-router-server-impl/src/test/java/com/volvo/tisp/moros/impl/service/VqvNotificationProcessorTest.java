package com.volvo.tisp.moros.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.moros.database.api.VehicleInformationRepository;
import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;
import com.volvo.tisp.moros.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.moros.impl.service.metric.reporter.Crud;
import com.volvo.tisp.moros.impl.service.metric.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.moros.impl.service.metric.reporter.Identifier;
import com.volvo.tisp.moros.impl.service.metric.reporter.VqvNotificationMetricReporter;
import com.volvo.tisp.moros.impl.utils.TestUtils;
import com.wirelesscar.vqv.v1.api.NotificationType;

class VqvNotificationProcessorTest {
  @Test
  void processDeleteExceptionTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotificationBuilder().setMasterDataDeletedTimestamp(TestUtils.INSTANT).build();
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.doThrow(new IllegalArgumentException("TEST")).when(vehicleInformationRepository).delete(Mockito.any(VehicleInformationEntity.class));
    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString()))
        .thenReturn(Optional.of(TestUtils.createVehicleInformationEntity(true)));

    VqvNotificationProcessor vqvNotificationProcessor = new VqvNotificationProcessor(clock, dbOperationsMetricReporter, vehicleInformationRepository,
        vqvNotificationMetricReporter);
    Assertions.assertThrows(RuntimeException.class, () -> vqvNotificationProcessor.process(vqvNotification), "TEST");

    Mockito.verify(vehicleInformationRepository).delete(Mockito.any(VehicleInformationEntity.class));
    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(clock).instant();
    Mockito.verify(dbOperationsMetricReporter).onCrudFailure(Crud.DELETE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter);
  }

  @Test
  void processDeleteTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotificationBuilder().setMasterDataDeletedTimestamp(TestUtils.INSTANT).build();
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString()))
        .thenReturn(Optional.of(TestUtils.createVehicleInformationEntity(true)));

    VqvNotificationProcessor vqvNotificationProcessor = new VqvNotificationProcessor(clock, dbOperationsMetricReporter, vehicleInformationRepository,
        vqvNotificationMetricReporter);
    vqvNotificationProcessor.process(vqvNotification);

    Mockito.verify(vehicleInformationRepository).delete(Mockito.any(VehicleInformationEntity.class));
    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.DELETE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter);
  }

  @Test
  void processInsertExceptionTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString())).thenReturn(Optional.empty());
    Mockito.when(vehicleInformationRepository.save(Mockito.any(VehicleInformationEntity.class))).thenThrow(new RuntimeException("TEST"));

    VqvNotificationProcessor vqvNotificationProcessor = new VqvNotificationProcessor(clock, dbOperationsMetricReporter, vehicleInformationRepository,
        vqvNotificationMetricReporter);

    Assertions.assertThrows(RuntimeException.class, () -> vqvNotificationProcessor.process(vqvNotification), "TEST");

    Mockito.verify(clock, Mockito.times(3)).instant();
    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository).save(Mockito.any(VehicleInformationEntity.class));
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.READ, Identifier.VPI);
    Mockito.verify(dbOperationsMetricReporter).onCrudFailure(Crud.CREATE);
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, vehicleInformationRepository);
  }

  @Test
  void processInsertTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString())).thenReturn(Optional.empty());
    Mockito.when(vehicleInformationRepository.save(Mockito.any(VehicleInformationEntity.class))).thenReturn(vehicleInformationEntity);

    VqvNotificationProcessor vqvNotificationProcessor = new VqvNotificationProcessor(clock, dbOperationsMetricReporter, vehicleInformationRepository,
        vqvNotificationMetricReporter);
    vqvNotificationProcessor.process(vqvNotification);

    Mockito.verify(clock, Mockito.times(4)).instant();
    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository)
        .findAllByPartNumberAndSerialNumber(vqvNotification.vehicleInformation().assetHardwareId().partNumber().toString(),
            vqvNotification.vehicleInformation().assetHardwareId().serialNumber().toString());
    Mockito.verify(vehicleInformationRepository).save(Mockito.any(VehicleInformationEntity.class));
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.READ, Identifier.VPI);
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(0), Crud.CREATE);
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, vehicleInformationRepository);
  }

  @Test
  void processRemovedTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotificationBuilder().setNotificationType(NotificationType.REMOVED).build();
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString()))
        .thenReturn(Optional.of(TestUtils.createVehicleInformationEntity(true)));

    VqvNotificationProcessor vqvNotificationProcessor = new VqvNotificationProcessor(clock, dbOperationsMetricReporter, vehicleInformationRepository,
        vqvNotificationMetricReporter);
    vqvNotificationProcessor.process(vqvNotification);

    Mockito.verify(vehicleInformationRepository).delete(Mockito.any(VehicleInformationEntity.class));
    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.DELETE);
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter);
  }

  @Test
  void processStaleUpdateTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    vehicleInformationEntity.setVersion(vqvNotification.vehicleInformation().version().value() + 1);

    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString())).thenReturn(Optional.of(vehicleInformationEntity));

    VqvNotificationProcessor vqvNotificationProcessor = new VqvNotificationProcessor(clock, dbOperationsMetricReporter, vehicleInformationRepository,
        vqvNotificationMetricReporter);
    vqvNotificationProcessor.process(vqvNotification);

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.READ, Identifier.VPI);
    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository)
        .findAllByPartNumberAndSerialNumber(vqvNotification.vehicleInformation().assetHardwareId().partNumber().toString(),
            vqvNotification.vehicleInformation().assetHardwareId().serialNumber().toString());
    Mockito.verifyNoMoreInteractions(clock, vehicleInformationRepository, dbOperationsMetricReporter);
  }

  @Test
  void processUpdateExceptionTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    vehicleInformationEntity.setVersion(vqvNotification.vehicleInformation().version().value() - 1);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString())).thenReturn(Optional.of(vehicleInformationEntity));
    Mockito.when(vehicleInformationRepository.save(vehicleInformationEntity)).thenThrow(new RuntimeException("TEST"));

    VqvNotificationProcessor vqvNotificationProcessor = new VqvNotificationProcessor(clock, dbOperationsMetricReporter, vehicleInformationRepository,
        vqvNotificationMetricReporter);
    Assertions.assertThrows(RuntimeException.class, () -> vqvNotificationProcessor.process(vqvNotification), "TEST");

    Mockito.verify(clock, Mockito.times(3)).instant();
    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository).save(vehicleInformationEntity);
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.READ, Identifier.VPI);
    Mockito.verify(dbOperationsMetricReporter).onCrudFailure(Crud.UPDATE);
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, vehicleInformationRepository);
  }

  @Test
  void processUpdateTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);
    vehicleInformationEntity.setVersion(vqvNotification.vehicleInformation().version().value());
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);

    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);

    Mockito.when(vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString())).thenReturn(Optional.of(vehicleInformationEntity));

    VqvNotificationProcessor vqvNotificationProcessor = new VqvNotificationProcessor(clock, dbOperationsMetricReporter, vehicleInformationRepository,
        vqvNotificationMetricReporter);
    vqvNotificationProcessor.process(vqvNotification);

    Mockito.verify(clock, Mockito.times(4)).instant();
    Mockito.verify(vehicleInformationRepository).findByVpi(vqvNotification.vpi().toString());
    Mockito.verify(vehicleInformationRepository)
        .findAllByPartNumberAndSerialNumber(vqvNotification.vehicleInformation().assetHardwareId().partNumber().toString(),
            vqvNotification.vehicleInformation().assetHardwareId().serialNumber().toString());
    Mockito.verify(vehicleInformationRepository).save(vehicleInformationEntity);
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.READ, Identifier.VPI);
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(0), Crud.UPDATE);
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, vehicleInformationRepository);
  }
}
