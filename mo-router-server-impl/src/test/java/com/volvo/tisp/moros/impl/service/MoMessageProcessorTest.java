package com.volvo.tisp.moros.impl.service;

import java.util.concurrent.CompletableFuture;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.mockito.Mockito;

import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.service.metric.reporter.MessagePublisherMetricReporter;
import com.volvo.tisp.moros.impl.service.publisher.ExternalMoMessagePublisher;
import com.volvo.tisp.moros.impl.service.publisher.InternalMoMessagePublisher;
import com.volvo.tisp.moros.impl.utils.TestUtils;

public class MoMessageProcessorTest {
  @Test
  public void publishToMultiplePublishersTest() {
    ProcessableMessage processableMessage = TestUtils.createProcessableMessage();
    InternalMoMessagePublisher internalMoMessagePublisher = Mockito.mock(InternalMoMessagePublisher.class);
    ExternalMoMessagePublisher externalMoMessagePublisher = Mockito.mock(ExternalMoMessagePublisher.class);
    MessagePublisherMetricReporter messagePublisherMetricReporter = Mockito.mock(MessagePublisherMetricReporter.class);
    MoMessageProcessor moMessageProcessor = new MoMessageProcessor(internalMoMessagePublisher, externalMoMessagePublisher, messagePublisherMetricReporter);

    Mockito.when(internalMoMessagePublisher.publish(processableMessage)).thenReturn(CompletableFuture.completedFuture(1));
    Mockito.when(externalMoMessagePublisher.publish(processableMessage)).thenReturn(CompletableFuture.completedFuture(1));

    CompletableFuture<Void> result = moMessageProcessor.process(processableMessage);

    Assertions.assertThat(result).isCompleted();
    Mockito.verify(internalMoMessagePublisher).publish(processableMessage);
    Mockito.verify(externalMoMessagePublisher).publish(processableMessage);
    Mockito.verifyNoMoreInteractions(externalMoMessagePublisher, internalMoMessagePublisher);
    Mockito.verifyNoInteractions(messagePublisherMetricReporter);
  }

  @Test
  public void publishWithNoSubscribersTest() {
    ProcessableMessage processableMessage = TestUtils.createProcessableMessage();
    InternalMoMessagePublisher internalMoMessagePublisher = Mockito.mock(InternalMoMessagePublisher.class);
    ExternalMoMessagePublisher externalMoMessagePublisher = Mockito.mock(ExternalMoMessagePublisher.class);
    MessagePublisherMetricReporter messagePublisherMetricReporter = Mockito.mock(MessagePublisherMetricReporter.class);
    MoMessageProcessor moMessageProcessor = new MoMessageProcessor(internalMoMessagePublisher, externalMoMessagePublisher, messagePublisherMetricReporter);

    Mockito.when(internalMoMessagePublisher.publish(processableMessage)).thenReturn(CompletableFuture.completedFuture(0));
    Mockito.when(externalMoMessagePublisher.publish(processableMessage)).thenReturn(CompletableFuture.completedFuture(0));

    CompletableFuture<Void> result = moMessageProcessor.process(processableMessage);

    Assertions.assertThat(result).isCompletedExceptionally();
    Mockito.verify(internalMoMessagePublisher).publish(processableMessage);
    Mockito.verify(externalMoMessagePublisher).publish(processableMessage);
    Mockito.verify(messagePublisherMetricReporter).onMissingSubscriber();
    Mockito.verifyNoMoreInteractions(externalMoMessagePublisher, internalMoMessagePublisher, messagePublisherMetricReporter);
  }
}
