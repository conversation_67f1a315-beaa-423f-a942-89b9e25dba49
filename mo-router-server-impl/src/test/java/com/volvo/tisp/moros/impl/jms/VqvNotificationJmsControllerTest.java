package com.volvo.tisp.moros.impl.jms;

import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.messaging.support.GenericMessage;

import com.volvo.tisp.moros.impl.converter.vqv.VqvNotificationConverter;
import com.volvo.tisp.moros.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.moros.impl.service.VqvNotificationProcessor;
import com.volvo.tisp.moros.impl.service.metric.reporter.VqvNotificationMetricReporter;
import com.volvo.tisp.moros.impl.utils.TestUtils;

import tisp.framework.internal.jms.JmsMessageImpl;

class VqvNotificationJmsControllerTest {
  private static final String BODY_STRING = "body string";

  @Test
  void handleNotificationNotFoundTest() {
    VqvNotificationConverter vqvNotificationConverter = Mockito.mock(VqvNotificationConverter.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);
    VqvNotificationProcessor vqvNotificationProcessor = Mockito.mock(VqvNotificationProcessor.class);

    JmsMessageImpl<String> jmsMessage = new JmsMessageImpl<>(new GenericMessage<>(BODY_STRING));
    VqvNotificationJmsController vqvNotificationJmsController = new VqvNotificationJmsController(vqvNotificationConverter, vqvNotificationProcessor,
        vqvNotificationMetricReporter);
    Mockito.when(vqvNotificationConverter.apply(BODY_STRING)).thenReturn(Optional.empty());

    vqvNotificationJmsController.handleNotification(jmsMessage);

    Mockito.verify(vqvNotificationConverter).apply(BODY_STRING);
    Mockito.verify(vqvNotificationMetricReporter).onConversionFailure();
    Mockito.verifyNoMoreInteractions(vqvNotificationConverter, vqvNotificationMetricReporter, vqvNotificationProcessor);
  }

  @Test
  void handleNotificationTest() {
    VqvNotification vqvNotification = TestUtils.createVqvNotification();
    VqvNotificationConverter vqvNotificationConverter = Mockito.mock(VqvNotificationConverter.class);
    VqvNotificationMetricReporter vqvNotificationMetricReporter = Mockito.mock(VqvNotificationMetricReporter.class);
    VqvNotificationProcessor vqvNotificationProcessor = Mockito.mock(VqvNotificationProcessor.class);

    JmsMessageImpl<String> jmsMessage = new JmsMessageImpl<>(new GenericMessage<>(BODY_STRING));
    VqvNotificationJmsController vqvNotificationJmsController = new VqvNotificationJmsController(vqvNotificationConverter, vqvNotificationProcessor,
        vqvNotificationMetricReporter);
    Mockito.when(vqvNotificationConverter.apply(BODY_STRING)).thenReturn(Optional.of(vqvNotification));

    vqvNotificationJmsController.handleNotification(jmsMessage);

    Mockito.verify(vqvNotificationConverter).apply(BODY_STRING);
    Mockito.verify(vqvNotificationProcessor).process(vqvNotification);
    Mockito.verifyNoMoreInteractions(vqvNotificationConverter, vqvNotificationMetricReporter, vqvNotificationProcessor);
  }
}