package com.volvo.tisp.moros.impl.converter;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.external.mo.message.client.json.v1.MoMessage;
import com.volvo.tisp.external.mo.message.client.json.v1.momessage.AssetIdentifierDetails;
import com.volvo.tisp.moros.impl.utils.TestUtils;

class ExternalMoMessageOutputConverterTest {
  private static void verifyTimestamp(Instant expectedInstant, Long timestamp) {
    Assertions.assertEquals(expectedInstant.getEpochSecond(), timestamp);
  }

  @Test
  void applyTest() {
    MoMessage moMessage = new ExternalMoMessageOutputConverter().apply(TestUtils.createProcessableMessage());
    Assertions.assertEquals(new AssetIdentifierDetails().withAssetIdentifier(TestUtils.CHASSIS_ID.value()).withAssetIdentifierType(
        AssetIdentifierDetails.AssetIdentifierType.CHASSIS_ID), moMessage.getAssetIdentifiers().get(0));
    Assertions.assertEquals(new AssetIdentifierDetails().withAssetIdentifier(TestUtils.ASSET_HARDWARE_ID.toString()).withAssetIdentifierType(
        AssetIdentifierDetails.AssetIdentifierType.ASSET_HARDWARE_ID), moMessage.getAssetIdentifiers().get(1));
    Assertions.assertEquals(TestUtils.SERVICE_ID.value(), moMessage.getServiceId());
    Assertions.assertEquals(TestUtils.SERVICE_VERSION.value(), moMessage.getServiceVersion());
    verifyTimestamp(TestUtils.ONBOARD_TIMESTAMP, moMessage.getOnboardTimestamp());
    verifyTimestamp(TestUtils.SERVER_TIMESTAMP, moMessage.getServerTimestamp());
    Assertions.assertEquals(TestUtils.SERVICE_ACCESS_TOKEN.toString(), moMessage.getServiceAccessToken());
    Assertions.assertEquals(TestUtils.TRACKING_IDENTIFIER.toString(), moMessage.getTrackingId());
    Assertions.assertArrayEquals(TestUtils.BASE64_PAYLOAD.getBytes(), moMessage.getPayload().getBytes());
    Assertions.assertEquals(TestUtils.KEY_ID, moMessage.getKeyId());
    Assertions.assertEquals(TestUtils.PAYLOAD_SIGNATURE, moMessage.getPayloadSignature());
  }

  @Test
  void invalidParameterTest() {
    Assertions.assertThrows(IllegalArgumentException.class, () -> new ExternalMoMessageOutputConverter().apply(null));
  }
}