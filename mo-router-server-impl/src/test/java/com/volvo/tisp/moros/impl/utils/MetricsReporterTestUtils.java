package com.volvo.tisp.moros.impl.utils;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

public final class MetricsReporterTestUtils {
  private MetricsReporterTestUtils() {
    throw new IllegalStateException();
  }

  public static void checkCounter(MeterRegistry meterRegistry, String metricName, double expectedCount) {
    checkCounter(meterRegistry, metricName, Tags.empty(), expectedCount);
  }

  public static void checkCounter(MeterRegistry meterRegistry, String metricName, Tags tags, double expectedCount) {
    final Counter counter = meterRegistry.find(metricName).tags(tags).counter();
    Assertions.assertEquals(expectedCount, counter.count());
  }

  public static void checkTimer(MeterRegistry meterRegistry, String metricName, Duration expectedDuration, long expectedCount) {
    Timer timer = meterRegistry.find(metricName).timer();
    Assertions.assertEquals(expectedCount, timer.count());
    Assertions.assertEquals(expectedDuration.toMillis(), timer.totalTime(TimeUnit.MILLISECONDS), 1);
  }

  public static void checkTimer(MeterRegistry meterRegistry, String metricName, Tags tags, Duration expectedDuration, long expectedCount) {
    Timer timer = meterRegistry.find(metricName).tags(tags).timer();
    Assertions.assertEquals(expectedCount, timer.count());
    Assertions.assertEquals(expectedDuration.toMillis(), timer.totalTime(TimeUnit.MILLISECONDS));
  }

  public static <T> void initReporterAndTest(Function<MeterRegistry, T> initMetricsReporter, BiConsumer<MeterRegistry, T> test) {
    MeterRegistry meterRegistry = new SimpleMeterRegistry();
    T metricsReporter = initMetricsReporter.apply(meterRegistry);

    test.accept(meterRegistry, metricsReporter);
  }
}
