package com.volvo.tisp.moros.impl.converter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.converter.vqv.VqvNotificationConverter;
import com.volvo.tisp.moros.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.moros.impl.model.OperationalStatus;
import com.volvo.tisp.moros.impl.model.VehicleInformation;
import com.volvo.tisp.moros.impl.utils.TestUtils;
import com.wirelesscar.vqv.v1.api.NotificationType;

class VqvNotificationConverterTest {
  private static final Path REMOVE_PATH = Path.of("src/test/resources/vqv/vqv-remove.json");
  private static final Path UPDATE_NULL_STATUS_PATH = Path.of("src/test/resources/vqv/vqv-update-null-status.json");
  private static final Path UPDATE_PATH = Path.of("src/test/resources/vqv/vqv-update.json");

  private static String getInputString(Path path) throws IOException {
    return Files.readString(path, StandardCharsets.UTF_8);
  }

  private static void verifyVehicleInformation(VehicleInformation vehicleInformation, OperationalStatus operationalStatus) {
    Assertions.assertSame(operationalStatus, vehicleInformation.operationalStatus());
    Assertions.assertEquals(TestUtils.ASSET_HARDWARE_ID, vehicleInformation.assetHardwareId());
    Assertions.assertEquals(TestUtils.CHASSIS_ID, vehicleInformation.chassisId().get());
    Assertions.assertEquals(TestUtils.VPI, vehicleInformation.vpi().get());
  }

  private static void verifyVehicleInformation(VehicleInformation vehicleInformation) {
    verifyVehicleInformation(vehicleInformation, OperationalStatus.OPERATIONAL);
  }

  @Test
  void applyRemoveTest() throws IOException {
    VqvNotificationConverter vqvNotificationConverter = new VqvNotificationConverter();
    Optional<VqvNotification> optional = vqvNotificationConverter.apply(getInputString(REMOVE_PATH));

    VqvNotification vqvNotification = optional.orElseThrow();
    Assertions.assertEquals(TestUtils.INSTANT, vqvNotification.masterDataDeletedTimestamp());
    Assertions.assertEquals(TestUtils.VPI, vqvNotification.vpi());
    Assertions.assertSame(NotificationType.REMOVED, vqvNotification.notificationType());

    verifyVehicleInformation(vqvNotification.vehicleInformation());
  }

  @Test
  void applyUpdateTest() throws IOException {
    VqvNotificationConverter vqvNotificationConverter = new VqvNotificationConverter();
    Optional<VqvNotification> optional = vqvNotificationConverter.apply(getInputString(UPDATE_PATH));

    VqvNotification vqvNotification = optional.orElseThrow();
    Assertions.assertNull(vqvNotification.masterDataDeletedTimestamp());
    Assertions.assertEquals(TestUtils.VPI, vqvNotification.vpi());
    Assertions.assertSame(NotificationType.UPDATED, vqvNotification.notificationType());

    verifyVehicleInformation(vqvNotification.vehicleInformation());
  }

  @Test
  void applyUpdateWithNullOperationStatusTest() throws IOException {
    VqvNotificationConverter vqvNotificationConverter = new VqvNotificationConverter();
    Optional<VqvNotification> optional = vqvNotificationConverter.apply(getInputString(UPDATE_NULL_STATUS_PATH));

    VqvNotification vqvNotification = optional.orElseThrow();
    Assertions.assertNull(vqvNotification.masterDataDeletedTimestamp());
    Assertions.assertEquals(TestUtils.VPI, vqvNotification.vpi());
    Assertions.assertSame(NotificationType.UPDATED, vqvNotification.notificationType());

    verifyVehicleInformation(vqvNotification.vehicleInformation(), OperationalStatus.NON_OPERATIONAL);
  }
}
