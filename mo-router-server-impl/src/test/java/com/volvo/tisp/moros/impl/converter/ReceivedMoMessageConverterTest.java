package com.volvo.tisp.moros.impl.converter;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.moros.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ReceivedMoMessageConverterTest {
  private static void verifyReceivedMoMessage(ReceivedMoMessage expected, ReceivedMoMessage actual) {
    Assertions.assertEquals(expected.assetHardwareId(), actual.assetHardwareId());
    Assertions.assertEquals(expected.onboardTimestamp(), actual.onboardTimestamp());
    Assertions.assertEquals(expected.payload(), actual.payload());
    Assertions.assertEquals(expected.priority(), actual.priority());
    Assertions.assertEquals(expected.serviceAccessToken(), actual.serviceAccessToken());
    Assertions.assertEquals(expected.serviceId(), actual.serviceId());
    Assertions.assertEquals(expected.serviceVersion(), actual.serviceVersion());
    Assertions.assertEquals(expected.serverTimestamp(), actual.serverTimestamp());
    Assertions.assertEquals(expected.trackingIdentifier(), actual.trackingIdentifier());
    Assertions.assertEquals(expected.keyId(), actual.keyId());
    Assertions.assertEquals(expected.payloadSignature(), actual.payloadSignature());
  }

  @Test
  void applyInvalidAssetIdTest() {
    MoMessage moMessage = TestUtils.createProtobufMoMessage();
    moMessage.setAssetId("invalid");

    ReceivedMoMessageConverter receivedMoMessageConverter = new ReceivedMoMessageConverter();
    Assertions.assertThrows(IllegalStateException.class, () -> receivedMoMessageConverter.apply(moMessage));
  }

  @Test
  void applyInvalidTest() {
    ReceivedMoMessageConverter receivedMoMessageConverter = new ReceivedMoMessageConverter();

    AssertThrows.illegalArgumentException(() -> receivedMoMessageConverter.apply(null), "moMessage must not be null");
  }

  @Test
  void applyTest() {
    ReceivedMoMessageConverter receivedMoMessageConverter = new ReceivedMoMessageConverter();

    ReceivedMoMessage receivedMoMessage = receivedMoMessageConverter.apply(TestUtils.createProtobufMoMessage());
    verifyReceivedMoMessage(TestUtils.createReceivedMoMessage(), receivedMoMessage);
  }
}
