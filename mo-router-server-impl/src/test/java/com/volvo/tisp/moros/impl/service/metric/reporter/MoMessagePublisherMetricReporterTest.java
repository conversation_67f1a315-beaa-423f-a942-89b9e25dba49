package com.volvo.tisp.moros.impl.service.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.moros.impl.utils.MetricsReporterTestUtils;

class MoMessagePublisherMetricReporterTest {
  @Test
  void onMissingSubscriberTest() {
    MetricsReporterTestUtils.initReporterAndTest(MessagePublisherMetricReporter::new, (meterRegistry, moMessagePublisherMetricReporter) -> {
      moMessagePublisherMetricReporter.onMissingSubscriber();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "subscription-router.missing-subscriber", 1);

      moMessagePublisherMetricReporter.onMissingSubscriber();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "subscription-router.missing-subscriber", 2);
    });
  }
}
