package com.volvo.tisp.moros.impl.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ChassisIdTest {
  @Test
  void fromComponents() {
    Assertions.assertEquals(new ChassisId("SERIES-NUMBER"), ChassisId.fromComponents("SERIES", "NUMBER"));
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("SERIES-NUMBER", new ChassisId("SERIES-NUMBER").toString());
  }
}