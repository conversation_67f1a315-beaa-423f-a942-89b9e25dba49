package com.volvo.tisp.moros.impl.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ServiceIdTest {
  private static final int SERVICE_ID = 1234;

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new ServiceId(-1), "value must not be negative: -1");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals(Integer.toString(SERVICE_ID), new ServiceId(SERVICE_ID).toString());
  }

  @Test
  void validConstructorTest() {
    Assertions.assertEquals(SERVICE_ID, new ServiceId(SERVICE_ID).value());
  }
}
