package com.volvo.tisp.moros.impl.service.publisher;

import java.util.concurrent.CompletableFuture;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.external.mo.message.client.json.v1.MessageTypes;
import com.volvo.tisp.moros.impl.converter.ExternalMoMessageOutputConverter;
import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.service.IntegrationLogger;
import com.volvo.tisp.moros.impl.service.metric.reporter.ServiceIdMetricReporter;
import com.volvo.tisp.moros.impl.utils.TestUtils;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;

class ExternalMoMessagePublisherTest {
  private static MessagePublisher<ProcessableMessage> mockToBuildMessagePublisher(MessagePublisher.Builder builder,
      ExternalMoMessageOutputConverter externalMoMessageOutputConverter, MessagePublisher<ProcessableMessage> mockedMessagePublisher) {
    MessagePublisher.Builder.Step3 step3Mock = Mockito.mock(MessagePublisher.Builder.Step3.class);
    Mockito.when(step3Mock.build()).thenReturn(mockedMessagePublisher);
    MessagePublisher.Builder.Step2<ProcessableMessage> step2Mock = Mockito.mock(MessagePublisher.Builder.Step2.class);
    Mockito.when(step2Mock.version(MessageTypes.VERSION_1_0, externalMoMessageOutputConverter)).thenReturn(step3Mock);
    Mockito.when(builder.messageType(MessageTypes.EXTERNAL_MO_MESSAGE, ProcessableMessage.class)).thenReturn(step2Mock);
    return mockedMessagePublisher;
  }

  @Test
  void publishFailureTest() {
    MessagePublisher.Builder builder = Mockito.mock(MessagePublisher.Builder.class);
    ExternalMoMessageOutputConverter externalMoMessageOutputConverter = Mockito.mock(ExternalMoMessageOutputConverter.class);
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    ServiceIdMetricReporter serviceIdMetricReporter = Mockito.mock(ServiceIdMetricReporter.class);
    ProcessableMessage processableMessage = TestUtils.createProcessableMessage();
    MessagePublisher<ProcessableMessage> mockedMessagePublisher = mockToBuildMessagePublisher(builder, externalMoMessageOutputConverter,
        TestUtils.mockedMessagePublisher(new RuntimeException("Failed"), processableMessage));
    ExternalMoMessagePublisher externalMoMessagePublisher = new ExternalMoMessagePublisher(builder, externalMoMessageOutputConverter, integrationLogger,
        serviceIdMetricReporter);

    CompletableFuture<Integer> result = externalMoMessagePublisher.publish(processableMessage);

    Assertions.assertThat(result).isCompletedExceptionally();

    Mockito.verify(mockedMessagePublisher).newMessage();
    Mockito.verify(integrationLogger).log(processableMessage.receivedMoMessage(), "PublishMoMessageJmsFailure");
    Mockito.verifyNoMoreInteractions(mockedMessagePublisher, integrationLogger);
    Mockito.verifyNoInteractions(serviceIdMetricReporter);
  }

  @Test
  void publishSuccessfulTest() {
    MessagePublisher.Builder builder = Mockito.mock(MessagePublisher.Builder.class);
    ExternalMoMessageOutputConverter externalMoMessageOutputConverter = Mockito.mock(ExternalMoMessageOutputConverter.class);
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    ServiceIdMetricReporter serviceIdMetricReporter = Mockito.mock(ServiceIdMetricReporter.class);
    ProcessableMessage processableMessage = TestUtils.createProcessableMessage();
    MessagePublisher<ProcessableMessage> mockedMessagePublisher = mockToBuildMessagePublisher(builder, externalMoMessageOutputConverter,
        TestUtils.mockedMessagePublisher(1, processableMessage));
    ExternalMoMessagePublisher externalMoMessagePublisher = new ExternalMoMessagePublisher(builder, externalMoMessageOutputConverter, integrationLogger,
        serviceIdMetricReporter);

    CompletableFuture<Integer> result = externalMoMessagePublisher.publish(processableMessage);

    Assertions.assertThat(result).isCompletedWithValue(1);

    Mockito.verify(mockedMessagePublisher).newMessage();
    Mockito.verify(integrationLogger).log(processableMessage.receivedMoMessage(), "PublishMoMessageJmsSuccess");
    Mockito.verify(serviceIdMetricReporter).onMoMessageSuccess(processableMessage.receivedMoMessage().serviceId());
    Mockito.verifyNoMoreInteractions(mockedMessagePublisher, serviceIdMetricReporter, integrationLogger);
  }
}