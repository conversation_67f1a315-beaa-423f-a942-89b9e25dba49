package com.volvo.tisp.moros.impl.service;

import java.time.Clock;
import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage;
import com.volvo.tisp.integration.log.IntegrationLogEventPublisher;
import com.volvo.tisp.integration.log.v2.IntegrationLogEvent;
import com.volvo.tisp.moros.impl.conf.Constant;
import com.volvo.tisp.moros.impl.conf.properties.AppProperties;
import com.volvo.tisp.moros.impl.utils.TestUtils;

class IntegrationLoggerTest {
  private static final String DETAIL_TYPE = "some-detail-type";

  public static MoMessage createMoMessage() {
    MoMessage moMessage = new MoMessage();
    moMessage.setAssetId(TestUtils.ASSET_HARDWARE_ID.toString());
    moMessage.setTrackingId(TestUtils.TRACKING_IDENTIFIER.toString());
    moMessage.setIsSoftcar(true);
    moMessage.setServiceId(TestUtils.SERVICE_ID.value());
    moMessage.setServiceVersion(TestUtils.SERVICE_VERSION.value());
    return moMessage;
  }

  private static void verifyEvent(IntegrationLogEvent integrationLogEvent) {
    Assertions.assertEquals(TestUtils.ASSET_HARDWARE_ID.toString(), integrationLogEvent.getAssetIdentifiers().stream().findFirst().orElseThrow().getAssetIdentifier());
    Assertions.assertEquals(TestUtils.TRACKING_IDENTIFIER.toString(), integrationLogEvent.getTrackingId());
    Assertions.assertSame(IntegrationLogEvent.Version._2, integrationLogEvent.getVersion());
    Assertions.assertEquals(Constant.MOROS, integrationLogEvent.getSource());
    Assertions.assertEquals(DETAIL_TYPE, integrationLogEvent.getDetailType());
    Assertions.assertEquals(Instant.ofEpochSecond(3).toEpochMilli(), integrationLogEvent.getTimestamp());
    Assertions.assertNotNull(integrationLogEvent.getId());
  }

  @Test
  void disabledTest() {
    IntegrationLogEventPublisher integrationLogEventPublisher = Mockito.mock(IntegrationLogEventPublisher.class);

    IntegrationLogger integrationLogger = new IntegrationLogger(new AppProperties(false), TestUtils.mockClock(), integrationLogEventPublisher);
    integrationLogger.log(createMoMessage(), DETAIL_TYPE);
    integrationLogger.log(TestUtils.createReceivedMoMessage(), DETAIL_TYPE);

    Mockito.verifyNoInteractions(integrationLogEventPublisher);
  }

  @Test
  void logMoMessageTest() {
    Clock clock = TestUtils.mockClock();
    IntegrationLogEventPublisher integrationLogEventPublisher = Mockito.mock(IntegrationLogEventPublisher.class);

    IntegrationLogger integrationLogger = new IntegrationLogger(new AppProperties(true), clock, integrationLogEventPublisher);
    integrationLogger.log(createMoMessage(), DETAIL_TYPE);

    ArgumentCaptor<IntegrationLogEvent> captor = ArgumentCaptor.forClass(IntegrationLogEvent.class);
    Mockito.verify(integrationLogEventPublisher).publish(captor.capture());

    verifyEvent(captor.getValue());
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(integrationLogEventPublisher, clock);
  }

  @Test
  void logReceivedMoMessageTest() {
    Clock clock = TestUtils.mockClock();
    IntegrationLogEventPublisher integrationLogEventPublisher = Mockito.mock(IntegrationLogEventPublisher.class);

    IntegrationLogger integrationLogger = new IntegrationLogger(new AppProperties(true), clock, integrationLogEventPublisher);
    integrationLogger.log(TestUtils.createReceivedMoMessage(), DETAIL_TYPE);

    ArgumentCaptor<IntegrationLogEvent> captor = ArgumentCaptor.forClass(IntegrationLogEvent.class);
    Mockito.verify(integrationLogEventPublisher).publish(captor.capture());

    verifyEvent(captor.getValue());
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(integrationLogEventPublisher, clock);
  }
}