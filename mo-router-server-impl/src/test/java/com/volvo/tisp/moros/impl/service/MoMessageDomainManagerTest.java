package com.volvo.tisp.moros.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.dao.IncorrectResultSizeDataAccessException;

import com.volvo.tisp.moros.database.api.VehicleInformationRepository;
import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;
import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.moros.impl.model.AssetHardwareId;
import com.volvo.tisp.moros.impl.model.OperationalStatus;
import com.volvo.tisp.moros.impl.model.VehicleInformation;
import com.volvo.tisp.moros.impl.model.Version;
import com.volvo.tisp.moros.impl.service.metric.reporter.Crud;
import com.volvo.tisp.moros.impl.service.metric.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.moros.impl.service.metric.reporter.Identifier;
import com.volvo.tisp.moros.impl.service.metric.reporter.MoMessageMetricReporter;
import com.volvo.tisp.moros.impl.service.metric.reporter.Reason;
import com.volvo.tisp.moros.impl.utils.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MoMessageDomainManagerTest {
  @Test
  void processMessageInvalidTest() {
    AssertThrows.illegalArgumentException(
        () -> new MoMessageDomainManager(TestUtils.mockClock(), Mockito.mock(DbOperationsMetricReporter.class), Mockito.mock(MoMessageMetricReporter.class),
            Mockito.mock(MoMessageProcessor.class), Mockito.mock(VehicleInformationRepository.class), Collections.emptyList()).processMessage(null),
        "receivedMoMessage must not be null");
  }

  @Test
  void processMessagePublisherFailureTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MoMessageMetricReporter moMessageMetricReporter = Mockito.mock(MoMessageMetricReporter.class);
    MoMessageProcessor moMessageProcessor = Mockito.mock(MoMessageProcessor.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);

    ReceivedMoMessage receivedMoMessage = TestUtils.createReceivedMoMessage();
    AssetHardwareId assetHardwareId = receivedMoMessage.assetHardwareId();
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);

    Mockito.when(vehicleInformationRepository.findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber()))
        .thenReturn(Optional.of(vehicleInformationEntity));
    Mockito.doThrow(new RuntimeException("simulated test"))
        .when(moMessageProcessor)
        .process(new ProcessableMessage(receivedMoMessage, VehicleInformation.fromEntity(vehicleInformationEntity)));

    MoMessageDomainManager moMessageDomainManager = new MoMessageDomainManager(clock, dbOperationsMetricReporter, moMessageMetricReporter, moMessageProcessor,
        vehicleInformationRepository, List.of(receivedMoMessage.serviceId().value()));
    AssertThrows.exception(() -> moMessageDomainManager.processMessage(receivedMoMessage), "simulated test", RuntimeException.class);

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.READ, Identifier.ASSET_HARDWARE_ID);
    Mockito.verify(vehicleInformationRepository).findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber());
    Mockito.verifyNoInteractions(moMessageMetricReporter);
    Mockito.verify(moMessageProcessor).process(new ProcessableMessage(receivedMoMessage, VehicleInformation.fromEntity(vehicleInformationEntity)));
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, vehicleInformationRepository, moMessageMetricReporter, moMessageProcessor);
  }

  @Test
  void processMessageTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MoMessageMetricReporter moMessageMetricReporter = Mockito.mock(MoMessageMetricReporter.class);
    MoMessageProcessor moMessageProcessor = Mockito.mock(MoMessageProcessor.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);

    ReceivedMoMessage receivedMoMessage = TestUtils.createReceivedMoMessage();
    AssetHardwareId assetHardwareId = receivedMoMessage.assetHardwareId();
    VehicleInformationEntity vehicleInformationEntity = TestUtils.createVehicleInformationEntity(true);

    Mockito.when(vehicleInformationRepository.findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber()))
        .thenReturn(Optional.of(vehicleInformationEntity));
    Mockito.when(moMessageProcessor.process(new ProcessableMessage(receivedMoMessage, VehicleInformation.fromEntity(vehicleInformationEntity))))
        .thenReturn(CompletableFuture.completedFuture(null));

    MoMessageDomainManager moMessageDomainManager = new MoMessageDomainManager(clock, dbOperationsMetricReporter, moMessageMetricReporter, moMessageProcessor,
        vehicleInformationRepository, List.of(receivedMoMessage.serviceId().value()));
    moMessageDomainManager.processMessage(receivedMoMessage);

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.READ, Identifier.ASSET_HARDWARE_ID);
    Mockito.verify(vehicleInformationRepository).findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber());
    Mockito.verifyNoInteractions(moMessageMetricReporter);
    Mockito.verify(moMessageProcessor).process(new ProcessableMessage(receivedMoMessage, VehicleInformation.fromEntity(vehicleInformationEntity)));
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, vehicleInformationRepository, moMessageMetricReporter, moMessageProcessor);
  }

  @Test
  void processMessageWithMultipleAssetsTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MoMessageMetricReporter moMessageMetricReporter = Mockito.mock(MoMessageMetricReporter.class);
    MoMessageProcessor moMessageProcessor = Mockito.mock(MoMessageProcessor.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);

    ReceivedMoMessage receivedMoMessage = TestUtils.createReceivedMoMessage();
    AssetHardwareId assetHardwareId = receivedMoMessage.assetHardwareId();

    Mockito.when(vehicleInformationRepository.findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber()))
        .thenThrow(IncorrectResultSizeDataAccessException.class);

    MoMessageDomainManager moMessageDomainManager = new MoMessageDomainManager(clock, dbOperationsMetricReporter, moMessageMetricReporter, moMessageProcessor,
        vehicleInformationRepository, List.of(receivedMoMessage.serviceId().value()));
    CompletableFuture<Void> result = moMessageDomainManager.processMessage(receivedMoMessage);

    Assertions.assertTrue(result.isCompletedExceptionally());

    Mockito.verify(clock, Mockito.times(1)).instant();
    Mockito.verify(vehicleInformationRepository).findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber());
    Mockito.verify(moMessageMetricReporter).onMoMessageFailure(Reason.MULTIPLE_ASSETS_FOUND);
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, vehicleInformationRepository, moMessageMetricReporter, moMessageProcessor);
  }

  @Test
  void processMessageWithUnIdentifiedNotWhitelistedAssetTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MoMessageMetricReporter moMessageMetricReporter = Mockito.mock(MoMessageMetricReporter.class);
    MoMessageProcessor moMessageProcessor = Mockito.mock(MoMessageProcessor.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);

    ReceivedMoMessage receivedMoMessage = TestUtils.createReceivedMoMessage();
    AssetHardwareId assetHardwareId = receivedMoMessage.assetHardwareId();

    Mockito.when(vehicleInformationRepository.findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber()))
        .thenReturn(Optional.empty());

    VehicleInformation madeUpVehicleInformation = new VehicleInformation(assetHardwareId, Optional.empty(), OperationalStatus.OPERATIONAL, new Version(1),
        Optional.empty());

    Mockito.when(moMessageProcessor.process(new ProcessableMessage(receivedMoMessage, madeUpVehicleInformation)))
        .thenReturn(CompletableFuture.completedFuture(null));

    MoMessageDomainManager moMessageDomainManager = new MoMessageDomainManager(clock, dbOperationsMetricReporter, moMessageMetricReporter, moMessageProcessor,
        vehicleInformationRepository, List.of());
    CompletableFuture<Void> future = moMessageDomainManager.processMessage(receivedMoMessage);
    Assertions.assertTrue(future.isCompletedExceptionally());

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.READ, Identifier.ASSET_HARDWARE_ID);
    Mockito.verify(vehicleInformationRepository).findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber());
    Mockito.verify(moMessageMetricReporter).onMoMessageFailure(Reason.ASSET_NOT_FOUND);
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, vehicleInformationRepository, moMessageMetricReporter, moMessageProcessor);
  }

  @Test
  void processMessageWithUnIdentifiedWhitelistedAssetTest() {
    Clock clock = TestUtils.mockClock();
    DbOperationsMetricReporter dbOperationsMetricReporter = Mockito.mock(DbOperationsMetricReporter.class);
    MoMessageMetricReporter moMessageMetricReporter = Mockito.mock(MoMessageMetricReporter.class);
    MoMessageProcessor moMessageProcessor = Mockito.mock(MoMessageProcessor.class);
    VehicleInformationRepository vehicleInformationRepository = Mockito.mock(VehicleInformationRepository.class);

    ReceivedMoMessage receivedMoMessage = TestUtils.createReceivedMoMessage();
    AssetHardwareId assetHardwareId = receivedMoMessage.assetHardwareId();

    Mockito.when(vehicleInformationRepository.findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber()))
        .thenReturn(Optional.empty());

    VehicleInformation madeUpVehicleInformation = new VehicleInformation(assetHardwareId, Optional.empty(), OperationalStatus.OPERATIONAL, new Version(1),
        Optional.empty());

    Mockito.when(moMessageProcessor.process(new ProcessableMessage(receivedMoMessage, madeUpVehicleInformation)))
        .thenReturn(CompletableFuture.completedFuture(null));

    MoMessageDomainManager moMessageDomainManager = new MoMessageDomainManager(clock, dbOperationsMetricReporter, moMessageMetricReporter, moMessageProcessor,
        vehicleInformationRepository, List.of(receivedMoMessage.serviceId().value()));
    moMessageDomainManager.processMessage(receivedMoMessage);

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(dbOperationsMetricReporter).onCrudDuration(Duration.ofSeconds(2), Crud.READ, Identifier.ASSET_HARDWARE_ID);
    Mockito.verify(vehicleInformationRepository).findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber());
    Mockito.verifyNoInteractions(moMessageMetricReporter);
    Mockito.verify(moMessageProcessor).process(new ProcessableMessage(receivedMoMessage, madeUpVehicleInformation));
    Mockito.verifyNoMoreInteractions(clock, dbOperationsMetricReporter, vehicleInformationRepository, moMessageMetricReporter, moMessageProcessor);
  }
}
