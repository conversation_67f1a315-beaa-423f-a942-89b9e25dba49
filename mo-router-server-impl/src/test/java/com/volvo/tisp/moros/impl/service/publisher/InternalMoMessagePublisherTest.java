package com.volvo.tisp.moros.impl.service.publisher;

import java.util.concurrent.CompletableFuture;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.moros.impl.converter.MoMessageOutputConverter;
import com.volvo.tisp.moros.impl.converter.MoMessageWithAssetHardwareIdOutputConverter;
import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.service.IntegrationLogger;
import com.volvo.tisp.moros.impl.service.metric.reporter.ServiceIdMetricReporter;
import com.volvo.tisp.moros.impl.utils.TestUtils;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vc.mo.message.client.json.v1.MessageTypes;

class InternalMoMessagePublisherTest {
  @Test
  void publishWhenMoMessageHasSubscribersTest() {
    MessagePublisher.Builder builder = Mockito.mock(MessagePublisher.Builder.class);
    MoMessageOutputConverter moMessageOutputConverter = Mockito.mock(MoMessageOutputConverter.class);
    MoMessageWithAssetHardwareIdOutputConverter moMessageWithAssetHardwareIdOutputConverter = Mockito.mock(MoMessageWithAssetHardwareIdOutputConverter.class);
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    ServiceIdMetricReporter serviceIdMetricReporter = Mockito.mock(ServiceIdMetricReporter.class);
    ProcessableMessage processableMessage = TestUtils.createProcessableMessage();
    MessagePublisher<ProcessableMessage> mockedMoMessagePublisher = mockToBuildMoMessagePublisher(builder, moMessageOutputConverter,
        TestUtils.mockedMessagePublisher(1, processableMessage));
    MessagePublisher<ProcessableMessage> mockedMoMessageWithAssetHardwareIdMessagePublisher = mockToBuildInternalMoMessageWithAssetHardwareIdPublisher(builder, moMessageWithAssetHardwareIdOutputConverter,
        TestUtils.mockedMessagePublisher(2, processableMessage));

    InternalMoMessagePublisher internalMoMessagePublisher = new InternalMoMessagePublisher(builder, moMessageOutputConverter, moMessageWithAssetHardwareIdOutputConverter, integrationLogger,
        serviceIdMetricReporter);

    CompletableFuture<Integer> result = internalMoMessagePublisher.publish(processableMessage);

    Assertions.assertThat(result).isCompletedWithValue(1);

    Mockito.verify(mockedMoMessagePublisher).newMessage();
    Mockito.verify(integrationLogger).log(processableMessage.receivedMoMessage(), "PublishMoMessageJmsSuccess");
    Mockito.verify(serviceIdMetricReporter).onMoMessageSuccess(processableMessage.receivedMoMessage().serviceId());
    Mockito.verifyNoMoreInteractions(mockedMoMessagePublisher, integrationLogger, serviceIdMetricReporter);
    Mockito.verifyNoInteractions(mockedMoMessageWithAssetHardwareIdMessagePublisher);
  }

  @Test
  void publishWhenMoMessageHasNoSubscribersAndMoMessageWithAssetHardwareIdHasSubscribersTest() {
    MessagePublisher.Builder builder = Mockito.mock(MessagePublisher.Builder.class);
    MoMessageOutputConverter moMessageOutputConverter = Mockito.mock(MoMessageOutputConverter.class);
    MoMessageWithAssetHardwareIdOutputConverter moMessageWithAssetHardwareIdOutputConverter = Mockito.mock(MoMessageWithAssetHardwareIdOutputConverter.class);
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    ServiceIdMetricReporter serviceIdMetricReporter = Mockito.mock(ServiceIdMetricReporter.class);
    ProcessableMessage processableMessage = TestUtils.createProcessableMessage();
    MessagePublisher<ProcessableMessage> mockedMoMessagePublisher = mockToBuildMoMessagePublisher(builder, moMessageOutputConverter,
        TestUtils.mockedMessagePublisher(0, processableMessage));
    MessagePublisher<ProcessableMessage> mockedMoMessageWithAssetHardwareIdMessagePublisher = mockToBuildInternalMoMessageWithAssetHardwareIdPublisher(builder, moMessageWithAssetHardwareIdOutputConverter,
        TestUtils.mockedMessagePublisher(1, processableMessage));

    InternalMoMessagePublisher internalMoMessagePublisher = new InternalMoMessagePublisher(builder, moMessageOutputConverter, moMessageWithAssetHardwareIdOutputConverter, integrationLogger,
        serviceIdMetricReporter);

    CompletableFuture<Integer> result = internalMoMessagePublisher.publish(processableMessage);

    Assertions.assertThat(result).isCompletedWithValue(1);

    Mockito.verify(mockedMoMessagePublisher).newMessage();
    Mockito.verify(mockedMoMessageWithAssetHardwareIdMessagePublisher).newMessage();
    Mockito.verify(integrationLogger).log(processableMessage.receivedMoMessage(), "PublishMoMessageJmsSuccess");
    Mockito.verify(serviceIdMetricReporter).onMoMessageSuccess(processableMessage.receivedMoMessage().serviceId());
    Mockito.verifyNoMoreInteractions(mockedMoMessagePublisher, integrationLogger, serviceIdMetricReporter);
  }

  @Test
  void publishWhenMoMessagePublishingThrowsExceptionTest() {
    MessagePublisher.Builder builder = Mockito.mock(MessagePublisher.Builder.class);
    MoMessageOutputConverter moMessageOutputConverter = Mockito.mock(MoMessageOutputConverter.class);
    MoMessageWithAssetHardwareIdOutputConverter moMessageWithAssetHardwareIdOutputConverter = Mockito.mock(MoMessageWithAssetHardwareIdOutputConverter.class);
    IntegrationLogger integrationLogger = Mockito.mock(IntegrationLogger.class);
    ServiceIdMetricReporter serviceIdMetricReporter = Mockito.mock(ServiceIdMetricReporter.class);
    ProcessableMessage processableMessage = TestUtils.createProcessableMessage();
    MessagePublisher<ProcessableMessage> mockedMoMessagePublisher = mockToBuildMoMessagePublisher(builder, moMessageOutputConverter,
        TestUtils.mockedMessagePublisher(new RuntimeException("Failed"), processableMessage));
    MessagePublisher<ProcessableMessage> mockedMoMessageWithAssetHardwareIdMessagePublisher = mockToBuildInternalMoMessageWithAssetHardwareIdPublisher(builder, moMessageWithAssetHardwareIdOutputConverter,
        TestUtils.mockedMessagePublisher(1, processableMessage));

    InternalMoMessagePublisher internalMoMessagePublisher = new InternalMoMessagePublisher(builder, moMessageOutputConverter, moMessageWithAssetHardwareIdOutputConverter, integrationLogger,
        serviceIdMetricReporter);

    CompletableFuture<Integer> result = internalMoMessagePublisher.publish(processableMessage);

    Assertions.assertThat(result).isCompletedExceptionally();

    Mockito.verify(mockedMoMessagePublisher).newMessage();
    Mockito.verify(integrationLogger).log(processableMessage.receivedMoMessage(), "PublishMoMessageJmsFailure");
    Mockito.verifyNoMoreInteractions(mockedMoMessagePublisher, integrationLogger);
    Mockito.verifyNoInteractions(mockedMoMessageWithAssetHardwareIdMessagePublisher, serviceIdMetricReporter);
  }

  private static MessagePublisher<ProcessableMessage> mockToBuildMoMessagePublisher(MessagePublisher.Builder builder,
      MoMessageOutputConverter moMessageOutputConverter, MessagePublisher<ProcessableMessage> mockedMessagePublisher) {
    MessagePublisher.Builder.Step3 step3Mock = Mockito.mock(MessagePublisher.Builder.Step3.class);
    Mockito.when(step3Mock.build()).thenReturn(mockedMessagePublisher);
    MessagePublisher.Builder.Step2<ProcessableMessage> step2Mock = Mockito.mock(MessagePublisher.Builder.Step2.class);
    Mockito.when(step2Mock.version(MessageTypes.VERSION_1_0, moMessageOutputConverter)).thenReturn(step3Mock);
    Mockito.when(builder.messageType(MessageTypes.MO_MESSAGE, ProcessableMessage.class)).thenReturn(step2Mock);
    return mockedMessagePublisher;
  }

  private static MessagePublisher<ProcessableMessage> mockToBuildInternalMoMessageWithAssetHardwareIdPublisher(MessagePublisher.Builder builder,
      MoMessageWithAssetHardwareIdOutputConverter moMessageWithAssetHardwareIdOutputConverter, MessagePublisher<ProcessableMessage> mockedMessagePublisher) {
    MessagePublisher.Builder.Step3 step3Mock = Mockito.mock(MessagePublisher.Builder.Step3.class);
    Mockito.when(step3Mock.build()).thenReturn(mockedMessagePublisher);
    MessagePublisher.Builder.Step2<ProcessableMessage> step2Mock = Mockito.mock(MessagePublisher.Builder.Step2.class);
    Mockito.when(step2Mock.version(MessageTypes.VERSION_1_0, moMessageWithAssetHardwareIdOutputConverter)).thenReturn(step3Mock);
    Mockito.when(builder.messageType(MessageTypes.MO_MESSAGE_WITH_ASSET_HARDWARE_ID, ProcessableMessage.class)).thenReturn(step2Mock);
    return mockedMessagePublisher;
  }
}
