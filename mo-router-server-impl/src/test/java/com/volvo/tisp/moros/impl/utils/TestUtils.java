package com.volvo.tisp.moros.impl.utils;

import java.time.Clock;
import java.time.Instant;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

import org.jetbrains.annotations.NotNull;
import org.mockito.Mockito;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.utility.DockerImageName;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;
import com.volvo.tisp.moros.impl.conf.properties.SqsClientConfigProperties;
import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.moros.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.moros.impl.model.AssetHardwareId;
import com.volvo.tisp.moros.impl.model.ChassisId;
import com.volvo.tisp.moros.impl.model.OperationalStatus;
import com.volvo.tisp.moros.impl.model.Priority;
import com.volvo.tisp.moros.impl.model.ServiceAccessToken;
import com.volvo.tisp.moros.impl.model.ServiceId;
import com.volvo.tisp.moros.impl.model.ServiceVersion;
import com.volvo.tisp.moros.impl.model.VehicleInformation;
import com.volvo.tisp.moros.impl.model.Version;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.wirelesscar.vqv.v1.api.NotificationType;

public final class TestUtils {
  public static final AssetHardwareId ASSET_HARDWARE_ID = AssetHardwareId.of("000001", "000002");
  public static final ChassisId CHASSIS_ID = new ChassisId("SERIES-NUMBER");
  public static final Instant INSTANT = Instant.ofEpochSecond(1664878057, 971000000);
  public static final Boolean IS_SOFTCAR = Boolean.TRUE;
  public static final String KEY_ID = "testKeyId";
  public static final Instant ONBOARD_TIMESTAMP = Instant.ofEpochSecond(5);
  public static final ImmutableByteArray PAYLOAD = ImmutableByteArray.of(new byte[] {1, 2, 3, 4, 5});
  public static final String BASE64_PAYLOAD = Base64.getEncoder().encodeToString(PAYLOAD.toByteArray());
  public static final String PAYLOAD_SIGNATURE = "hdsjksdah";
  public static final Instant SERVER_TIMESTAMP = Instant.ofEpochSecond(10);
  public static final ServiceAccessToken SERVICE_ACCESS_TOKEN = new ServiceAccessToken("AQIDBAU=");
  public static final ServiceId SERVICE_ID = new ServiceId(1337);
  public static final ServiceVersion SERVICE_VERSION = new ServiceVersion(42);
  public static final TrackingIdentifier TRACKING_IDENTIFIER = TrackingIdentifier.fromString("00000000000000000000000000000000");
  public static final Version VERSION = new Version(2L);
  public static final Vpi VPI = Vpi.ofString("1FAC35BF1EDB1DFF91AAC0BEBC56F22B");

  private TestUtils() {
    throw new IllegalStateException();
  }

  public static ProcessableMessage createProcessableMessage() {
    ReceivedMoMessage receivedMoMessage = createReceivedMoMessage();

    return new ProcessableMessage(receivedMoMessage, createVehicleInformation());
  }

  public static com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage createProtobufMoMessage() {
    com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage moMessage = new com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage();
    moMessage.setAssetId(ASSET_HARDWARE_ID.toString());
    moMessage.setOnboardTimestamp(ONBOARD_TIMESTAMP.getEpochSecond());
    moMessage.setPayload(BASE64_PAYLOAD);
    moMessage.setPriority(com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage.Priority.HIGH);
    moMessage.setServerTimestamp(SERVER_TIMESTAMP.getEpochSecond());
    moMessage.setServiceAccessToken(SERVICE_ACCESS_TOKEN.toString());
    moMessage.setServiceId(SERVICE_ID.value());
    moMessage.setServiceVersion(SERVICE_VERSION.value());
    moMessage.setTrackingId(TRACKING_IDENTIFIER.toString());
    moMessage.setIsSoftcar(IS_SOFTCAR);
    moMessage.setKeyId(KEY_ID);
    moMessage.setPayloadSignature(PAYLOAD_SIGNATURE);
    return moMessage;
  }

  public static ReceivedMoMessage createReceivedMoMessage() {
    return new ReceivedMoMessage(ASSET_HARDWARE_ID, ONBOARD_TIMESTAMP.getEpochSecond(), BASE64_PAYLOAD, Priority.HIGH, SERVER_TIMESTAMP.getEpochSecond(),
        SERVICE_ACCESS_TOKEN, SERVICE_ID, SERVICE_VERSION, TRACKING_IDENTIFIER, IS_SOFTCAR, Optional.of(KEY_ID), Optional.of(PAYLOAD_SIGNATURE));
  }

  public static VehicleInformation createVehicleInformation() {
    return new VehicleInformation(ASSET_HARDWARE_ID, Optional.of(CHASSIS_ID), OperationalStatus.OPERATIONAL, VERSION, Optional.of(VPI));
  }

  public static VehicleInformationEntity createVehicleInformationEntity(boolean operationalStatus) {
    VehicleInformationEntity vehicleInformationEntity = new VehicleInformationEntity();
    Instant now = Clock.systemUTC().instant();

    vehicleInformationEntity.setChassisId(CHASSIS_ID.value());
    vehicleInformationEntity.setCreatedAt(now);
    vehicleInformationEntity.setOperational(operationalStatus);
    vehicleInformationEntity.setPartNumber(ASSET_HARDWARE_ID.partNumber());
    vehicleInformationEntity.setSerialNumber(ASSET_HARDWARE_ID.serialNumber());
    vehicleInformationEntity.setVpi(VPI.toString());
    vehicleInformationEntity.setUpdatedAt(now);
    vehicleInformationEntity.setVersion(VERSION.value());

    return vehicleInformationEntity;
  }

  public static VqvNotification createVqvNotification() {
    return createVqvNotificationBuilder().build();
  }

  public static VqvNotification.Builder createVqvNotificationBuilder() {
    return new VqvNotification.Builder().setNotificationType(NotificationType.UPDATED).setVehicleInformation(createVehicleInformation()).setVpi(VPI);
  }

  public static LocalStackContainer getSQSLocalStackContainer() {
    LocalStackContainer sqsLocalStackContainer = new LocalStackContainer(DockerImageName.parse("localstack/localstack:3.0.2")).withServices(
        LocalStackContainer.Service.SQS);
    System.setProperty("aws.accessKeyId", "FAKE");
    System.setProperty("aws.secretAccessKey", "FAKE");
    System.setProperty("aws.region", "us-east-1");
    sqsLocalStackContainer.start();
    return sqsLocalStackContainer;
  }

  public static SqsClientConfigProperties getSqsClientConfigProperties(LocalStackContainer sqsLocalStackContainer) {
    return new SqsClientConfigProperties(sqsLocalStackContainer.getRegion(),
        sqsLocalStackContainer.getEndpointOverride(LocalStackContainer.Service.SQS).toString());
  }

  public static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(3), Instant.ofEpochSecond(5));
    return clock;
  }

  public static MessagePublisher<ProcessableMessage> mockedMessagePublisher(int subscriberCount, ProcessableMessage processableMessage) {
    MessagePublisher<ProcessableMessage> messagePublisher = Mockito.mock(MessagePublisher.class);
    MessagePublisher.Message<ProcessableMessage> message = partiallyMockMessagePublisher(messagePublisher);
    Mockito.when(message.publish(processableMessage)).thenReturn(CompletableFuture.completedFuture(subscriberCount));
    return messagePublisher;
  }

  public static MessagePublisher<ProcessableMessage> mockedMessagePublisher(Throwable throwable, ProcessableMessage processableMessage) {
    MessagePublisher<ProcessableMessage> messagePublisher = Mockito.mock(MessagePublisher.class);
    MessagePublisher.Message<ProcessableMessage> message = partiallyMockMessagePublisher(messagePublisher);
    Mockito.when(message.publish(processableMessage)).thenReturn(CompletableFuture.failedFuture(throwable));
    return messagePublisher;
  }

  private static MessagePublisher.@NotNull Message<ProcessableMessage> partiallyMockMessagePublisher(MessagePublisher<ProcessableMessage> messagePublisher) {
    MessagePublisher.Message<ProcessableMessage> message = Mockito.mock(MessagePublisher.Message.class);
    Mockito.when(messagePublisher.newMessage()).thenReturn(message);
    Mockito.when(message.option(Mockito.any(), Mockito.any(List.class))).thenReturn(message);
    Mockito.when(message.option(Mockito.any(), Mockito.any(Long.class))).thenReturn(message);
    return message;
  }
}
