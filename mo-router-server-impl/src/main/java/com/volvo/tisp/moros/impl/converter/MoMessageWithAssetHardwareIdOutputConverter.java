package com.volvo.tisp.moros.impl.converter;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.mo.message.client.json.v1.MoMessageWithAssetHardwareId;

@Component
public class MoMessageWithAssetHardwareIdOutputConverter implements Function<ProcessableMessage, MoMessageWithAssetHardwareId> {
  @Override
  public MoMessageWithAssetHardwareId apply(ProcessableMessage processableMessage) {
    Validate.notNull(processableMessage, "processableMessage");

    ReceivedMoMessage receivedMoMessage = processableMessage.receivedMoMessage();
    return new MoMessageWithAssetHardwareId()
        .withAssetHardwareId(processableMessage.receivedMoMessage().assetHardwareId().toString())
        .withServiceId(receivedMoMessage.serviceId().value())
        .withServiceVersion(receivedMoMessage.serviceVersion().value())
        .withOnboardTimestamp(receivedMoMessage.onboardTimestamp())
        .withServerTimestamp(receivedMoMessage.serverTimestamp())
        .withServiceAccessToken(receivedMoMessage.serviceAccessToken().toString())
        .withTrackingId(receivedMoMessage.trackingIdentifier().toString())
        .withPayload(receivedMoMessage.payload())
        .withKeyId(receivedMoMessage.keyId().orElse(null))
        .withPayloadSignature(receivedMoMessage.payloadSignature().orElse(null));
  }
}