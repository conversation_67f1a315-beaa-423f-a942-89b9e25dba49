package com.volvo.tisp.moros.impl.converter;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.volvo.tisp.external.mo.message.client.json.v1.MoMessage;
import com.volvo.tisp.external.mo.message.client.json.v1.momessage.AssetIdentifierDetails;
import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public final class ExternalMoMessageOutputConverter implements Function<ProcessableMessage, MoMessage> {
  @Override
  public MoMessage apply(ProcessableMessage processableMessage) {
    Validate.notNull(processableMessage, "processableMessage");

    ReceivedMoMessage receivedMoMessage = processableMessage.receivedMoMessage();
    return new MoMessage()
        .withAssetIdentifiers(
            buildAssetIdentifiers(processableMessage))
        .withServiceId(receivedMoMessage.serviceId().value())
        .withServiceVersion(receivedMoMessage.serviceVersion().value())
        .withOnboardTimestamp(receivedMoMessage.onboardTimestamp())
        .withServerTimestamp(receivedMoMessage.serverTimestamp())
        .withServiceAccessToken(receivedMoMessage.serviceAccessToken().toString())
        .withTrackingId(receivedMoMessage.trackingIdentifier().toString())
        .withPayload(receivedMoMessage.payload())
        .withKeyId(receivedMoMessage.keyId().orElse(null))
        .withPayloadSignature(receivedMoMessage.payloadSignature().orElse(null));
  }

  private static List<AssetIdentifierDetails> buildAssetIdentifiers(ProcessableMessage processableMessage) {
    List<AssetIdentifierDetails> assetIdentifierDetails = new ArrayList<>(2);
    processableMessage.vehicleInformation().chassisId().ifPresent(chassisId -> {
      assetIdentifierDetails.add(new AssetIdentifierDetails().withAssetIdentifier(chassisId.value()).withAssetIdentifierType(
          AssetIdentifierDetails.AssetIdentifierType.CHASSIS_ID));
    });
    assetIdentifierDetails.add(new AssetIdentifierDetails().withAssetIdentifier(processableMessage.vehicleInformation().assetHardwareId().toString())
        .withAssetIdentifierType(AssetIdentifierDetails.AssetIdentifierType.ASSET_HARDWARE_ID));
    return assetIdentifierDetails;
  }
}
