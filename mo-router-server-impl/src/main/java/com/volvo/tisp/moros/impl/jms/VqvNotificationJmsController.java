package com.volvo.tisp.moros.impl.jms;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.JmsException;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.moros.impl.converter.vqv.VqvNotificationConverter;
import com.volvo.tisp.moros.impl.service.VqvNotificationProcessor;
import com.volvo.tisp.moros.impl.service.metric.reporter.VqvNotificationMetricReporter;
import com.wirelesscar.vqv.v1.api.UpdateNotificationMessageType;

@JmsController(destination = VqvNotificationJmsController.VQV_NOTIFICATION_DESTINATION)
public class VqvNotificationJmsController {
  public static final String VQV_NOTIFICATION_DESTINATION = "VQV.IN";
  private static final Logger logger = LoggerFactory.getLogger(VqvNotificationJmsController.class);

  private final VqvNotificationConverter vqvNotificationConverter;
  private final VqvNotificationMetricReporter vqvNotificationMetricReporter;
  private final VqvNotificationProcessor vqvNotificationProcessor;

  public VqvNotificationJmsController(VqvNotificationConverter vqvNotificationConverter, VqvNotificationProcessor vqvNotificationProcessor,
      VqvNotificationMetricReporter vqvNotificationMetricReporter) {
    this.vqvNotificationConverter = vqvNotificationConverter;
    this.vqvNotificationProcessor = vqvNotificationProcessor;
    this.vqvNotificationMetricReporter = vqvNotificationMetricReporter;
  }

  @JmsMessageMapping(consumesType = UpdateNotificationMessageType.VIEW_NOTIFICATION_MESSAGE_TYPE, consumesVersion = UpdateNotificationMessageType.VIEW_NOTIFICATION_MESSAGE_TYPE_VERSION)
  public void handleNotification(final JmsMessage<String> jmsMessage) throws JmsException {
    logger.debug("received jmsMessage: {}", jmsMessage);

    vqvNotificationConverter.apply(jmsMessage.payload()).ifPresentOrElse(vqvNotificationProcessor::process, this::onEmpty);
  }

  private void onEmpty() {
    vqvNotificationMetricReporter.onConversionFailure();
  }
}
