package com.volvo.tisp.moros.impl.service;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.service.metric.reporter.MessagePublisherMetricReporter;
import com.volvo.tisp.moros.impl.service.publisher.ExternalMoMessagePublisher;
import com.volvo.tisp.moros.impl.service.publisher.InternalMoMessagePublisher;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class MoMessageProcessor {
  public static final Logger logger = LoggerFactory.getLogger(MoMessageProcessor.class);

  private final MessagePublisherMetricReporter messagePublisherMetricReporter;
  private final InternalMoMessagePublisher internalMoMessagePublisher;
  private final ExternalMoMessagePublisher externalMoMessagePublisher;

  public MoMessageProcessor(InternalMoMessagePublisher internalMoMessagePublisher, ExternalMoMessagePublisher externalMoMessagePublisher,
      MessagePublisherMetricReporter messagePublisherMetricReporter) {
    this.internalMoMessagePublisher = internalMoMessagePublisher;
    this.externalMoMessagePublisher = externalMoMessagePublisher;
    this.messagePublisherMetricReporter = messagePublisherMetricReporter;
  }

  public CompletableFuture<Void> process(ProcessableMessage processableMessage) {
    Validate.notNull(processableMessage, "processableMessage");

    CompletableFuture<Integer> internalMoMessageSubscribers = internalMoMessagePublisher.publish(processableMessage);
    CompletableFuture<Integer> externalMoMessageSubscribers = externalMoMessagePublisher.publish(processableMessage);

    return CompletableFuture.allOf(internalMoMessageSubscribers, externalMoMessageSubscribers).thenAccept(unused -> {
      int subscriberCount = internalMoMessageSubscribers.join() + externalMoMessageSubscribers.join();
      if (subscriberCount == 0) {
        messagePublisherMetricReporter.onMissingSubscriber();
        throw new RuntimeException("No subscribers found for the message");
      }
    });
  }
}
