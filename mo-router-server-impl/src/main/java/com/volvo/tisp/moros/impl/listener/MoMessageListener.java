package com.volvo.tisp.moros.impl.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.moros.impl.converter.ReceivedMoMessageConverter;
import com.volvo.tisp.moros.impl.service.IntegrationLogger;
import com.volvo.tisp.moros.impl.service.MoMessageDomainManager;
import com.volvo.tisp.moros.impl.service.metric.reporter.MoMessageMetricReporter;
import com.volvo.tisp.moros.impl.service.metric.reporter.Reason;

import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import software.amazon.awssdk.services.sqs.model.Message;

@Component
public class MoMessageListener {
  private static final Logger logger = LoggerFactory.getLogger(MoMessageListener.class);

  private final IntegrationLogger integrationLogger;
  private final MoMessageDomainManager moMessageDomainManager;
  private final MoMessageMetricReporter moMessageMetricReporter;
  private final ObjectMapper objectMapper;
  private final ReceivedMoMessageConverter receivedMoMessageConverter;

  public MoMessageListener(
      IntegrationLogger integrationLogger,
      MoMessageDomainManager moMessageDomainManager,
      MoMessageMetricReporter moMessageMetricReporter,
      ObjectMapper objectMapper,
      ReceivedMoMessageConverter receivedMoMessageConverter) {

    this.integrationLogger = integrationLogger;
    this.moMessageDomainManager = moMessageDomainManager;
    this.moMessageMetricReporter = moMessageMetricReporter;
    this.objectMapper = objectMapper;
    this.receivedMoMessageConverter = receivedMoMessageConverter;
  }

  @SqsListener(value = "${sqs.mo-message-queue.url}", factory = "sqsMessageListenerContainerFactory")
  @SuppressWarnings("FutureReturnValueIgnored")
  public void onMessage(Message message, Acknowledgement acknowledgement) {
    try {
      moMessageMetricReporter.onMoMessageV1Received();
      logger.debug("received message {}", message.body());

      MoMessage moMessage = objectMapper.readValue(message.body(), MoMessage.class);
      integrationLogger.log(moMessage, "MoMessageReceived");

      TispContext.runInContext(() -> moMessageDomainManager.processMessage(receivedMoMessageConverter.apply(moMessage)).thenAccept(unused -> {
        acknowledgement.acknowledgeAsync();
        integrationLogger.log(moMessage, "MoMessageProcessedSuccess");
      }).whenComplete((unused, throwable) -> {
        if (throwable != null) {
          integrationLogger.log(moMessage, "MoMessageProcessedFailure");
          logger.warn("unable to process message {}", moMessage, throwable);
          moMessageMetricReporter.onMoMessageFailure(Reason.PROCESS_FAILURE);
        }
      }), context -> context.tid(TrackingIdentifier.fromString(moMessage.getTrackingId())));

    } catch (JsonProcessingException e) {
      logger.warn("unable to deserialize message {}", message.body(), e);
      moMessageMetricReporter.onMoMessageFailure(Reason.CONVERSION_FAILURE);
    } catch (RuntimeException e) {
      logger.warn("exception occurred while processing message {}", message.body(), e);
      moMessageMetricReporter.onMoMessageFailure(Reason.PROCESS_FAILURE);
    }
  }
}
