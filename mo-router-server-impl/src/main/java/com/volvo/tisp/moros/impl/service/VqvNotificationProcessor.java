package com.volvo.tisp.moros.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.moros.database.api.VehicleInformationRepository;
import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;
import com.volvo.tisp.moros.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.moros.impl.model.AssetHardwareId;
import com.volvo.tisp.moros.impl.model.ChassisId;
import com.volvo.tisp.moros.impl.model.VehicleInformation;
import com.volvo.tisp.moros.impl.service.metric.reporter.Crud;
import com.volvo.tisp.moros.impl.service.metric.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.moros.impl.service.metric.reporter.Identifier;
import com.volvo.tisp.moros.impl.service.metric.reporter.VqvNotificationMetricReporter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.vqv.v1.api.NotificationType;

@Component
public class VqvNotificationProcessor {
  private static final Logger logger = LoggerFactory.getLogger(VqvNotificationProcessor.class);

  private final Clock clock;
  private final DbOperationsMetricReporter dbOperationsMetricReporter;
  private final VehicleInformationRepository vehicleInformationRepository;
  private final VqvNotificationMetricReporter vqvNotificationMetricReporter;

  public VqvNotificationProcessor(Clock clock, DbOperationsMetricReporter dbOperationsMetricReporter,
      VehicleInformationRepository vehicleInformationRepository, VqvNotificationMetricReporter vqvNotificationMetricReporter) {
    this.clock = clock;
    this.dbOperationsMetricReporter = dbOperationsMetricReporter;
    this.vehicleInformationRepository = vehicleInformationRepository;
    this.vqvNotificationMetricReporter = vqvNotificationMetricReporter;
  }

  private static VehicleInformationEntity createVehicleInformationEntity(VehicleInformation vehicleInformation) {
    VehicleInformationEntity vehicleInformationEntity = new VehicleInformationEntity();
    setVehicleInformationEntityAttributes(vehicleInformationEntity, vehicleInformation);
    vehicleInformationEntity.setVpi(vehicleInformation.vpi().map(Vpi::toString).orElse(null));

    return vehicleInformationEntity;
  }

  private static void setVehicleInformationEntityAttributes(VehicleInformationEntity vehicleInformationEntity, VehicleInformation vehicleInformation) {
    vehicleInformationEntity.setChassisId(vehicleInformation.chassisId().map(ChassisId::value).orElse(null));
    vehicleInformationEntity.setOperational(vehicleInformation.operationalStatus().getValue());
    vehicleInformationEntity.setPartNumber(vehicleInformation.assetHardwareId().partNumber());
    vehicleInformationEntity.setSerialNumber(vehicleInformation.assetHardwareId().serialNumber());
    vehicleInformationEntity.setVersion(vehicleInformation.version().value());
  }

  public void process(VqvNotification vqvNotification) {
    logger.debug("begin processing of vqvNotification: {}", vqvNotification);

    // markedForDeletion means vehicle was deleted from VDA, NotificationType.REMOVED means the vehicle is no longer part of our filter
    if (vqvNotification.isMarkedForDeletion() || vqvNotification.notificationType() == NotificationType.REMOVED) {
      logger.debug("deleting vehicleInformation entry: {}", vqvNotification.vehicleInformation());
      try {
        Instant start = clock.instant();
        vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString())
            .ifPresent(vehicleInformationRepository::delete);
        dbOperationsMetricReporter.onCrudDuration(Duration.between(start, clock.instant()), Crud.DELETE);
      } catch (Exception e) {
        dbOperationsMetricReporter.onCrudFailure(Crud.DELETE);
        throw e;
      }
      return;
    }
    Instant start = clock.instant();
    Optional<VehicleInformationEntity> vehicleInformationEntityOptional = vehicleInformationRepository.findByVpi(vqvNotification.vpi().toString());
    dbOperationsMetricReporter.onCrudDuration(Duration.between(start, clock.instant()), Crud.READ, Identifier.VPI);

    vehicleInformationEntityOptional
        .ifPresentOrElse(vehicleInformation -> update(vehicleInformation, vqvNotification.vehicleInformation()),
            () -> insert(vqvNotification.vehicleInformation()));
    logDuplicateAssetMetrics(vqvNotification);
  }

  private void insert(VehicleInformation vehicleInformation) {
    VehicleInformationEntity vehicleInformationEntity = createVehicleInformationEntity(vehicleInformation);

    try {
      Instant start = clock.instant();
      vehicleInformationRepository.save(vehicleInformationEntity);
      dbOperationsMetricReporter.onCrudDuration(Duration.between(start, clock.instant()), Crud.CREATE);
    } catch (Exception e) {
      dbOperationsMetricReporter.onCrudFailure(Crud.CREATE);
      throw e;
    }
  }

  private void logDuplicateAssetMetrics(VqvNotification vqvNotification) {
    AssetHardwareId assetHardwareId = vqvNotification.vehicleInformation().assetHardwareId();

    vehicleInformationRepository.findAllByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber()).stream()
        .filter(vehicleInformationEntity -> !vehicleInformationEntity.getVpi().equals(vqvNotification.vpi().toString()))
        .findAny()
        .ifPresent(vehicleInformationEntity -> vqvNotificationMetricReporter.onDuplicateAssetHardwareId(assetHardwareId.toString()));
  }

  private void update(VehicleInformationEntity vehicleInformationEntity, VehicleInformation vehicleInformation) {
    if (vehicleInformation.version().value() < vehicleInformationEntity.getVersion()) {
      logger.warn("Received a stale message for vpi {}. Not processing it. Current version {}. Received version {}", vehicleInformation.vpi(),
          vehicleInformationEntity.getVersion(), vehicleInformation.version());
      return;
    }
    setVehicleInformationEntityAttributes(vehicleInformationEntity, vehicleInformation);

    try {
      Instant start = clock.instant();
      vehicleInformationRepository.save(vehicleInformationEntity);
      dbOperationsMetricReporter.onCrudDuration(Duration.between(start, clock.instant()), Crud.UPDATE);
      logger.debug("updated vehicleInformation: {}", vehicleInformationEntity);
    } catch (Exception e) {
      dbOperationsMetricReporter.onCrudFailure(Crud.UPDATE);
      throw e;
    }
  }
}
