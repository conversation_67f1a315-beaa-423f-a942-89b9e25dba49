package com.volvo.tisp.moros.impl.service.publisher;

import java.util.concurrent.CompletableFuture;

import org.springframework.stereotype.Component;

import com.volvo.tisp.moros.impl.converter.MoMessageOutputConverter;
import com.volvo.tisp.moros.impl.converter.MoMessageWithAssetHardwareIdOutputConverter;
import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.service.IntegrationLogger;
import com.volvo.tisp.moros.impl.service.metric.reporter.ServiceIdMetricReporter;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.mo.message.client.json.v1.MessageTypes;

/**
 * Messages are published to JMS according to the following logic
 * If the vehicleInformation contains a Vpi, Message is published to MoMessage Queue. If there are no subscribers to that queue, It is published on MoMessageWithAssetHardwareId queue
 * Else, If the vehicleInformation doesn't contain a vpi, It is sent only on MoMessageWithAssetHardwareIdQueue
 */
@Component
public class InternalMoMessagePublisher extends MoMessagePublisher {
  private final MessagePublisher<ProcessableMessage> moMessagePublisher;
  private final MessagePublisher<ProcessableMessage> moMessageWithAssetHardwareIdPublisher;

  public InternalMoMessagePublisher(MessagePublisher.Builder builder, MoMessageOutputConverter moMessageOutputConverter,
      MoMessageWithAssetHardwareIdOutputConverter moMessageWithAssetHardwareIdOutputConverter, IntegrationLogger integrationLogger,
      ServiceIdMetricReporter serviceIdMetricReporter) {
    super(integrationLogger, serviceIdMetricReporter);
    this.moMessagePublisher = createInternalMoMessagePublisher(builder, moMessageOutputConverter);
    this.moMessageWithAssetHardwareIdPublisher = createInternalMoMessageWithAssetHardwareIdPublisher(builder, moMessageWithAssetHardwareIdOutputConverter);
  }

  private static MessagePublisher<ProcessableMessage> createInternalMoMessagePublisher(MessagePublisher.Builder builder,
      MoMessageOutputConverter moMessageOutputConverter) {
    return builder.messageType(MessageTypes.MO_MESSAGE, ProcessableMessage.class)
        .version(MessageTypes.VERSION_1_0, moMessageOutputConverter)
        .build();
  }

  private static MessagePublisher<ProcessableMessage> createInternalMoMessageWithAssetHardwareIdPublisher(MessagePublisher.Builder builder,
      MoMessageWithAssetHardwareIdOutputConverter moMessageWithAssetHardwareIdOutputConverter) {
    return builder.messageType(MessageTypes.MO_MESSAGE_WITH_ASSET_HARDWARE_ID, ProcessableMessage.class)
        .version(MessageTypes.VERSION_1_0, moMessageWithAssetHardwareIdOutputConverter)
        .build();
  }

  public CompletableFuture<Integer> publish(ProcessableMessage processableMessage) {
    Validate.notNull(processableMessage, "processableMessage");

    if (processableMessage.vehicleInformation().vpi().isPresent()) {
      return publishMessage(moMessagePublisher, processableMessage).thenCompose(subscribers -> {
        if (subscribers == 0) {
          return publishMessage(moMessageWithAssetHardwareIdPublisher, processableMessage);
        } else {
          return CompletableFuture.completedFuture(subscribers);
        }
      });
    } else {
      return publishMessage(moMessageWithAssetHardwareIdPublisher, processableMessage);
    }
  }
}
