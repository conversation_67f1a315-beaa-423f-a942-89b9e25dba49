package com.volvo.tisp.moros.impl.dto;

import java.util.Optional;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.moros.impl.model.AssetHardwareId;
import com.volvo.tisp.moros.impl.model.Priority;
import com.volvo.tisp.moros.impl.model.ServiceAccessToken;
import com.volvo.tisp.moros.impl.model.ServiceId;
import com.volvo.tisp.moros.impl.model.ServiceVersion;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record ReceivedMoMessage(
    AssetHardwareId assetHardwareId,
    Long onboardTimestamp,
    String payload,
    Priority priority,
    Long serverTimestamp,
    ServiceAccessToken serviceAccessToken,
    ServiceId serviceId,
    ServiceVersion serviceVersion,
    TrackingIdentifier trackingIdentifier,
    boolean isSoftcar,
    Optional<String> keyId,
    Optional<String> payloadSignature) {

  public ReceivedMoMessage {
    Validate.notNull(assetHardwareId, "assetHardwareId");
    Validate.notNull(onboardTimestamp, "onboardTimestamp");
    Validate.notNull(payload, "payload");
    Validate.notNull(priority, "priority");
    Validate.notNull(serverTimestamp, "serverTimestamp");
    Validate.notNull(serviceAccessToken, "serviceAccessToken");
    Validate.notNull(serviceId, "serviceId");
    Validate.notNull(serviceVersion, "serviceVersion");
    Validate.notNull(trackingIdentifier, "trackingIdentifier");
    Validate.notNull(keyId, "keyId");
    Validate.notNull(payloadSignature, "payloadSignature");
  }
}
