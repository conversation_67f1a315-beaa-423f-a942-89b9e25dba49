package com.volvo.tisp.moros.impl.service;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Component;

import com.volvo.tisp.moros.database.api.VehicleInformationRepository;
import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.moros.impl.model.AssetHardwareId;
import com.volvo.tisp.moros.impl.model.OperationalStatus;
import com.volvo.tisp.moros.impl.model.VehicleInformation;
import com.volvo.tisp.moros.impl.model.Version;
import com.volvo.tisp.moros.impl.service.metric.reporter.Crud;
import com.volvo.tisp.moros.impl.service.metric.reporter.DbOperationsMetricReporter;
import com.volvo.tisp.moros.impl.service.metric.reporter.Identifier;
import com.volvo.tisp.moros.impl.service.metric.reporter.MoMessageMetricReporter;
import com.volvo.tisp.moros.impl.service.metric.reporter.Reason;
import com.volvo.tisp.vc.main.utils.lib.Validate;

/**
 * Inorder to allow all the services in the whitelist, let the whitelist contain a single entry 0.
 */
@Component
public class MoMessageDomainManager {
  public static final Logger logger = LoggerFactory.getLogger(MoMessageDomainManager.class);

  private final Clock clock;
  private final DbOperationsMetricReporter dbOperationsMetricReporter;
  private final MoMessageMetricReporter moMessageMetricReporter;
  private final MoMessageProcessor moMessageProcessor;
  private final VehicleInformationRepository vehicleInformationRepository;
  private final boolean unIdentifiedAssetsAllowed;
  private final Collection<Integer> whitelistedServiceIds;

  public MoMessageDomainManager(Clock clock, DbOperationsMetricReporter dbOperationsMetricReporter, MoMessageMetricReporter moMessageMetricReporter,
      MoMessageProcessor moMessageProcessor, VehicleInformationRepository vehicleInformationRepository,
      @Value("${unidentified-assets.allowed:false}") boolean unIdentifiedAssetsAllowed,
      @Value("${whitelisted.service-ids:0}") Collection<Integer> whitelistedServiceIds) {
    this.clock = clock;
    this.dbOperationsMetricReporter = dbOperationsMetricReporter;
    this.moMessageMetricReporter = moMessageMetricReporter;
    this.moMessageProcessor = moMessageProcessor;
    this.vehicleInformationRepository = vehicleInformationRepository;
    this.unIdentifiedAssetsAllowed = unIdentifiedAssetsAllowed;
    this.whitelistedServiceIds = whitelistedServiceIds;
  }

  public CompletableFuture<Void> processMessage(ReceivedMoMessage receivedMoMessage) {
    Validate.notNull(receivedMoMessage, "receivedMoMessage");

    Instant start = clock.instant();
    AssetHardwareId assetHardwareId = receivedMoMessage.assetHardwareId();

    try {
      if (!isServiceWhitelisted(receivedMoMessage)) {
        logger.warn("Service id {} not allowed because it is not in whitelist", receivedMoMessage.serviceId().value());
        return CompletableFuture.completedFuture(null);
      }
      Optional<VehicleInformation> vehicleInformationOptional = vehicleInformationRepository
          .findByPartNumberAndSerialNumber(assetHardwareId.partNumber(), assetHardwareId.serialNumber())
          .map(VehicleInformation::fromEntity);
      dbOperationsMetricReporter.onCrudDuration(Duration.between(start, clock.instant()), Crud.READ, Identifier.ASSET_HARDWARE_ID);

      // When unIdentifiedAssetsAllowed is true and vehicleInformation is not found, we use a made up Vehicle Information with the provided assetHardwareId
      if (vehicleInformationOptional.isEmpty() && unIdentifiedAssetsAllowed) {
        vehicleInformationOptional = Optional.of(
            new VehicleInformation(assetHardwareId, Optional.empty(), OperationalStatus.OPERATIONAL, new Version(1), Optional.empty()));
      }

      return vehicleInformationOptional
          .map(vehicleInformation -> moMessageProcessor.process(new ProcessableMessage(receivedMoMessage, vehicleInformation)))
          .orElseGet(() -> {
            moMessageMetricReporter.onMoMessageFailure(Reason.ASSET_NOT_FOUND);
            return CompletableFuture.failedFuture(new RuntimeException("Asset not found"));
          });
    } catch (IncorrectResultSizeDataAccessException e) {
      moMessageMetricReporter.onMoMessageFailure(Reason.MULTIPLE_ASSETS_FOUND);
      return CompletableFuture.failedFuture(e);
    }
  }

  private boolean isServiceWhitelisted(ReceivedMoMessage receivedMoMessage) {
    return whitelistedServiceIds.contains(receivedMoMessage.serviceId().value()) || whitelistedServiceIds.size() == 1 && whitelistedServiceIds.contains(0);
  }
}
