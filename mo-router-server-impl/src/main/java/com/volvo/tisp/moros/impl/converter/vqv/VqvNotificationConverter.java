package com.volvo.tisp.moros.impl.converter.vqv;

import java.io.IOException;
import java.util.Locale;
import java.util.Optional;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.moros.impl.dto.vqv.VqvNotification;
import com.volvo.tisp.moros.impl.model.AssetHardwareId;
import com.volvo.tisp.moros.impl.model.ChassisId;
import com.volvo.tisp.moros.impl.model.OperationalStatus;
import com.volvo.tisp.moros.impl.model.VehicleInformation;
import com.volvo.tisp.moros.impl.model.Version;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.vqv.v1.api.UpdateNotification;

@Component
public class VqvNotificationConverter implements Function<String, Optional<VqvNotification>> {
  private static final Logger logger = LoggerFactory.getLogger(VqvNotificationConverter.class);

  private static VehicleInformation createVehicleInformation(UpdateNotification<VqvVehicle> updateNotification) {
    VqvVehicle vqvVehicle = updateNotification.getVehicle();
    return new VehicleInformation(
        AssetHardwareId.of(vqvVehicle.telematicUnit().partNumber(), vqvVehicle.telematicUnit().serialNumber()),
        getChassisId(vqvVehicle),
        getOperationalStatus(vqvVehicle.operationalStatus()),
        new Version(updateNotification.getVehicleVersion()),
        Optional.ofNullable(updateNotification.getVehiclePlatformId()).map(Vpi::ofString));
  }

  private static Optional<ChassisId> getChassisId(VqvVehicle vqvVehicle) {
    return Optional.ofNullable(vqvVehicle.chassisNumber())
        .flatMap(chassisNumber -> Optional.ofNullable(vqvVehicle.chassisSeries()))
        .map(chassisSeries -> ChassisId.fromComponents(vqvVehicle.chassisSeries(), vqvVehicle.chassisNumber()));
  }

  private static VqvNotification createVqvNotification(UpdateNotification<VqvVehicle> updateNotification) {
    return new VqvNotification.Builder()
        .setMasterDataDeletedTimestamp(updateNotification.getMasterDataDeletedTimestamp())
        .setNotificationType(updateNotification.getNotificationType())
        .setVehicleInformation(createVehicleInformation(updateNotification))
        .setVpi(Vpi.ofString(updateNotification.getVehiclePlatformId()))
        .build();
  }

  private static OperationalStatus getOperationalStatus(String operationalStatusString) {
    return Optional.ofNullable(operationalStatusString)
        .map(s -> s.toUpperCase(Locale.ROOT))
        .map(OperationalStatus::valueOf)
        .orElse(OperationalStatus.NON_OPERATIONAL);
  }

  @Override
  public Optional<VqvNotification> apply(String string) {
    try {
      UpdateNotification<VqvVehicle> updateNotification = UpdateNotification.fromMessage(VqvVehicle.class, string);
      return Optional.of(createVqvNotification(updateNotification));
    } catch (IOException e) {
      logger.warn("unable to convert string: {}", string, e);
      return Optional.empty();
    } catch (RuntimeException e) {
      logger.warn("input validation failed: {}", string, e);
      return Optional.empty();
    }
  }
}
