package com.volvo.tisp.moros.impl.conf.properties;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Configuration
public class SqsListenerConfigProperties {
  private static final Logger logger = LoggerFactory.getLogger(SqsListenerConfigProperties.class);

  private final Duration acknowledgeInterval;
  private final int acknowledgementThreshold;
  private final int maxConcurrentMessages;
  private final int maxMessagePerPoll;
  private final int maxPoolSize;
  private final Duration messageVisibility;
  private final Duration pollTimeout;

  public SqsListenerConfigProperties(
      @Value("${sqs.mo-message.acknowledgement.interval:5S}") Duration acknowledgementInterval,
      @Value("${sqs.mo-message.acknowledgement.threshold:50}") int acknowledgementThreshold,
      @Value("${sqs.mo-message.max.concurrent.message:100}") int maxConcurrentMessages,
      @Value("${sqs.mo-message.max.message.per.poll:10}") int maxMessagePerPoll,
      @Value("${sqs.mo-message.max.pool.size:100}") int maxPoolSize,
      @Value("${sqs.mo-message.message.visibility:30S}") Duration messageVisibility,
      @Value("${sqs.mo-message.poll.timeout:20S}") Duration pollTimeout) {
    Validate.notNull(acknowledgementInterval, "acknowledgementInterval");
    Validate.isPositive(acknowledgementThreshold, "acknowledgementThreshold");
    Validate.isPositive(maxConcurrentMessages, "maxConcurrentMessages");
    Validate.isPositiveAndNotGreaterThan(maxMessagePerPoll, 10, "maxMessagePerPoll");
    Validate.isPositive(maxPoolSize, "maxPoolSize");
    Validate.notNull(messageVisibility, "messageVisibility");
    Validate.notNull(pollTimeout, "pollTimeout");

    logger.info("SqsListenerConfiguration created with sqs.mo-message.acknowledgement.interval: {}, sqs.mo-message.acknowledgement.threshold:{},"
            + " sqs.mo-message.max.concurrent.message:{}, sqs.mo-message.max.message.per.poll:{},  sqs.mo-message.max.poll.size:{},"
            + " sqs.mo-message.message.visibility:{}, sqs.mo-message.poll.timeout:{}", acknowledgementInterval, acknowledgementThreshold, maxConcurrentMessages,
        maxMessagePerPoll, maxPoolSize, messageVisibility, pollTimeout);

    this.acknowledgeInterval = acknowledgementInterval;
    this.acknowledgementThreshold = acknowledgementThreshold;
    this.maxConcurrentMessages = maxConcurrentMessages;
    this.maxMessagePerPoll = maxMessagePerPoll;
    this.maxPoolSize = maxPoolSize;
    this.messageVisibility = messageVisibility;
    this.pollTimeout = pollTimeout;
  }

  public Duration getAcknowledgeInterval() {
    return acknowledgeInterval;
  }

  public int getAcknowledgementThreshold() {
    return acknowledgementThreshold;
  }

  public int getMaxConcurrentMessages() {
    return maxConcurrentMessages;
  }

  public int getMaxMessagePerPoll() {
    return maxMessagePerPoll;
  }

  public int getMaxPoolSize() {
    return maxPoolSize;
  }

  public Duration getMessageVisibility() {
    return messageVisibility;
  }

  public Duration getPollTimeout() {
    return pollTimeout;
  }
}
