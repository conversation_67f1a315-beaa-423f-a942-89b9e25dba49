package com.volvo.tisp.moros.impl.service.metric.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

@Component
public class MessagePublisherMetricReporter {
  private final Counter missingSubscriberCounter;

  public MessagePublisherMetricReporter(MeterRegistry meterRegistry) {
    missingSubscriberCounter = meterRegistry.counter("subscription-router.missing-subscriber");
  }

  public void onMissingSubscriber() {
    missingSubscriberCounter.increment();
  }
}
