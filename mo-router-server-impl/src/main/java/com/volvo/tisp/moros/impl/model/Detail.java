package com.volvo.tisp.moros.impl.model;

import java.util.Map;

import com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage;
import com.volvo.tisp.moros.impl.conf.Constant;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;

public record Detail(int serviceId, boolean isSoftcar) {
  public Detail(ReceivedMoMessage receivedMoMessage) {
    this(receivedMoMessage.serviceId().value(), receivedMoMessage.isSoftcar());
  }

  public Detail(MoMessage moMessage) {
    this(moMessage.getServiceId(), moMessage.getIsSoftcar());
  }

  public Map<String, Object> toMap() {
    return Map.of(Constant.SERVICE_ID, String.valueOf(serviceId), Constant.IS_SOFTCAR, isSoftcar);
  }
}
