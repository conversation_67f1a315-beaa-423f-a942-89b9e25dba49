package com.volvo.tisp.moros.impl.service.metric.reporter;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class MoMessageMetricReporter {
  private static final String MO_STATUS_FAILED = "mo.message.failed";
  private static final String REASON = "reason";

  private final MeterRegistry meterRegistry;
  private final Counter moMessageV1ReceivedCounter;

  public MoMessageMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
    this.moMessageV1ReceivedCounter = meterRegistry.counter("sqs.received", Tags.of("type", "MO_MESSAGE-1.0"));
  }

  public void onMoMessageFailure(Reason reason) {
    Validate.notNull(reason, REASON);

    meterRegistry.counter(MO_STATUS_FAILED, Tags.of(REASON, reason.name())).increment();
  }

  public void onMoMessageV1Received() {
    moMessageV1ReceivedCounter.increment();
  }
}
