package com.volvo.tisp.moros.impl.conf;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.volvo.tisp.moros.impl.conf.properties.SqsClientConfigProperties;
import com.volvo.tisp.moros.impl.conf.properties.SqsListenerConfigProperties;

import io.awspring.cloud.sqs.MessageExecutionThreadFactory;
import io.awspring.cloud.sqs.config.SqsMessageListenerContainerFactory;
import io.awspring.cloud.sqs.listener.BackPressureMode;
import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementOrdering;
import io.awspring.cloud.sqs.listener.acknowledgement.handler.AcknowledgementMode;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.SqsAsyncClientBuilder;

@Configuration
public class SqsConfig {
  @Bean
  public SqsAsyncClient createSqsAsyncClient(SqsClientConfigProperties sqsClientConfigProperties) {
    SqsAsyncClientBuilder sqsAsyncClientBuilder = SqsAsyncClient.builder();
    sqsAsyncClientBuilder.credentialsProvider(DefaultCredentialsProvider.create());
    sqsAsyncClientBuilder.region(Region.of(sqsClientConfigProperties.getRegion()));
    sqsClientConfigProperties.getEndpointOverride().ifPresent(sqsAsyncClientBuilder::endpointOverride);

    return sqsAsyncClientBuilder.build();
  }

  @Bean
  public SqsMessageListenerContainerFactory<Object> sqsMessageListenerContainerFactory(SqsAsyncClient sqsAsyncClient,
      SqsListenerConfigProperties sqsListenerConfigProperties) {
    return SqsMessageListenerContainerFactory.builder()
        .sqsAsyncClient(sqsAsyncClient)
        .configure(sqsContainerOptionsBuilder -> sqsContainerOptionsBuilder.acknowledgementMode(AcknowledgementMode.MANUAL)
            .acknowledgementInterval(sqsListenerConfigProperties.getAcknowledgeInterval())
            .acknowledgementThreshold(sqsListenerConfigProperties.getAcknowledgementThreshold())
            .acknowledgementOrdering(AcknowledgementOrdering.PARALLEL)
            .maxMessagesPerPoll(sqsListenerConfigProperties.getMaxMessagePerPoll())
            .pollTimeout(sqsListenerConfigProperties.getPollTimeout())
            .maxConcurrentMessages(sqsListenerConfigProperties.getMaxConcurrentMessages())
            .messageVisibility(sqsListenerConfigProperties.getMessageVisibility())
            .componentsTaskExecutor(moMessageListenerExecutor(sqsListenerConfigProperties))
            .backPressureMode(BackPressureMode.AUTO))
        .build();
  }

  private ThreadPoolTaskExecutor moMessageListenerExecutor(SqsListenerConfigProperties sqsListenerConfigProperties) {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(sqsListenerConfigProperties.getMaxPoolSize());
    executor.setMaxPoolSize(sqsListenerConfigProperties.getMaxPoolSize());
    executor.setPrestartAllCoreThreads(true);
    executor.setThreadFactory(new MessageExecutionThreadFactory("mo-message-listener-executor-"));
    executor.initialize();
    return executor;
  }
}
