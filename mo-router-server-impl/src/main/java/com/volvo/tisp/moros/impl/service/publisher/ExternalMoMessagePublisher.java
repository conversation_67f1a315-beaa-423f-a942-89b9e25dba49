package com.volvo.tisp.moros.impl.service.publisher;

import java.util.concurrent.CompletableFuture;

import org.springframework.stereotype.Component;

import com.volvo.tisp.external.mo.message.client.json.v1.MessageTypes;
import com.volvo.tisp.moros.impl.converter.ExternalMoMessageOutputConverter;
import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.service.IntegrationLogger;
import com.volvo.tisp.moros.impl.service.metric.reporter.ServiceIdMetricReporter;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class ExternalMoMessagePublisher extends MoMessagePublisher {
  private final MessagePublisher<ProcessableMessage> externalMoMessagePublisher;

  public ExternalMoMessagePublisher(MessagePublisher.Builder builder, ExternalMoMessageOutputConverter externalMoMessageOutputConverter,
      IntegrationLogger integrationLogger, ServiceIdMetricReporter serviceIdMetricReporter) {
    super(integrationLogger, serviceIdMetricReporter);
    this.externalMoMessagePublisher = createExternalMoMessageWithAssetHardwareIdPublisher(builder, externalMoMessageOutputConverter);
  }

  private static MessagePublisher<ProcessableMessage> createExternalMoMessageWithAssetHardwareIdPublisher(MessagePublisher.Builder builder,
      ExternalMoMessageOutputConverter externalMoMessageOutputConverter) {
    return builder.messageType(MessageTypes.EXTERNAL_MO_MESSAGE, ProcessableMessage.class)
        .version(MessageTypes.VERSION_1_0, externalMoMessageOutputConverter)
        .build();
  }

  @Override
  public CompletableFuture<Integer> publish(ProcessableMessage processableMessage) {
    Validate.notNull(processableMessage, "processableMessage");

    return publishMessage(externalMoMessagePublisher, processableMessage);
  }
}
