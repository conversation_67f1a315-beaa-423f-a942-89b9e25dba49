package com.volvo.tisp.moros.impl.conf.properties;

import java.net.URI;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SqsClientConfigProperties {
  private static final Logger logger = LoggerFactory.getLogger(SqsClientConfigProperties.class);

  private final URI endpointOverride;
  private final String region;

  public SqsClientConfigProperties(@Value("${sqs.region:${site}}") String region, @Value("${sqs.endpoint-override:}") String endpointOverride) {
    this.region = region;
    this.endpointOverride = StringUtils.isBlank(endpointOverride) ? null : URI.create(endpointOverride);

    logger.info("SqsClientConfigProperties created with sqs.region:{}, sqs.endpoint-override:{}", region, endpointOverride);
  }

  public Optional<URI> getEndpointOverride() {
    return Optional.ofNullable(endpointOverride);
  }

  public String getRegion() {
    return region;
  }
}
