package com.volvo.tisp.moros.impl.conf;

import java.time.Clock;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoClientSettingsBuilderCustomizer;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.volvo.tisp.moros.impl.conf.properties.ConnectionPoolConfigProperties;
import com.wirelesscar.vqv.v1.client.subscription.ViewRegistry;
import com.wirelesscar.vqv.v1.client.subscription.ViewRegistryImpl;

import io.mongock.driver.api.driver.ConnectionDriver;
import io.mongock.driver.mongodb.springdata.v4.SpringDataMongoV4Driver;
import io.mongock.runner.springboot.EnableMongock;

@SpringBootApplication
@ComponentScan(basePackages = {"com.volvo.tisp.moros", "com.volvo.tisp.vc.uncaught.exception.handler"})
@EnableMongoRepositories("com.volvo.tisp.moros.database")
@ConfigurationPropertiesScan("com.volvo.tisp.moros.impl.conf.properties")
@EnableMongoAuditing
@EnableMongock
@EnableCaching
public class AppConfig {
  public static void main(String[] args) {
    SpringApplication.run(AppConfig.class);
  }

  @Bean
  public CacheManager cacheManager(
      @Value("${cache.ttl:PT5S}") Duration cacheTtl,
      @Value("${cache.initial-size:1000}") int initialSize,
      @Value("${cache.max-size:100000}") int maxSize) {
    CaffeineCacheManager cacheManager = new CaffeineCacheManager();

    cacheManager.setCaffeine(Caffeine.newBuilder()
        .expireAfterWrite(cacheTtl)
        .initialCapacity(initialSize)
        .maximumSize(maxSize));

    return cacheManager;
  }

  @Bean
  public MongoClientSettingsBuilderCustomizer createMongoClient(ConnectionPoolConfigProperties connectionPoolConfigProperties) {
    return builder -> builder.applyToConnectionPoolSettings(connectionPool -> {
      connectionPool.maxSize(connectionPoolConfigProperties.getMaxPoolSize());
      connectionPool.minSize(connectionPoolConfigProperties.getMinPoolSize());
      connectionPool.maxConnectionIdleTime(connectionPoolConfigProperties.getMaxConnectionIdleTime(), TimeUnit.MINUTES);
      connectionPool.maxWaitTime(connectionPoolConfigProperties.getMaxWaitTime(), TimeUnit.MINUTES);
      connectionPool.maxConnectionLifeTime(connectionPoolConfigProperties.getMaxConnectionLifeTime(), TimeUnit.MINUTES);
    });
  }

  @Bean
  @Primary
  public ConnectionDriver createMongockConnection(MongoTemplate mongoTemplate) {
    return SpringDataMongoV4Driver.withDefaultLock(mongoTemplate);
  }

  @Bean
  public ViewRegistry createViewRegistry(WebClient.Builder builder) {
    return new ViewRegistryImpl(builder);
  }

  @Bean
  Clock createClock() {
    return Clock.systemUTC();
  }

  @Bean
  @Primary
  ObjectMapper createObjectMapper() {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    objectMapper.registerModule(new Jdk8Module());
    return objectMapper;
  }

  @Bean
  ObjectWriter createObjectWriter(ObjectMapper objectMapper) {
    return objectMapper.writer();
  }
}
