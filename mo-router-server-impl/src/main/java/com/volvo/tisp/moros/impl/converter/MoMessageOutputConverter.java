package com.volvo.tisp.moros.impl.converter;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage;

@Component
public final class MoMessageOutputConverter implements Function<ProcessableMessage, MoMessage> {
  @Override
  public MoMessage apply(ProcessableMessage processableMessage) {
    Validate.notNull(processableMessage, "processableMessage");

    ReceivedMoMessage receivedMoMessage = processableMessage.receivedMoMessage();
    return new MoMessage()
        .withVehiclePlatformId(processableMessage.vehicleInformation().vpi().map(Vpi::toString).orElseThrow(() -> new IllegalArgumentException("vpi can not be null")))
        .withServiceId(receivedMoMessage.serviceId().value())
        .withServiceVersion(receivedMoMessage.serviceVersion().value())
        .withOnboardTimestamp(receivedMoMessage.onboardTimestamp())
        .withServerTimestamp(receivedMoMessage.serverTimestamp())
        .withServiceAccessToken(receivedMoMessage.serviceAccessToken().toString())
        .withTrackingId(receivedMoMessage.trackingIdentifier().toString())
        .withPayload(receivedMoMessage.payload())
        .withKeyId(receivedMoMessage.keyId().orElse(null))
        .withPayloadSignature(receivedMoMessage.payloadSignature().orElse(null));
  }
}
