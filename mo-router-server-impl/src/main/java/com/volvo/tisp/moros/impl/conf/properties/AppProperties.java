package com.volvo.tisp.moros.impl.conf.properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AppProperties {
  private static final Logger logger = LoggerFactory.getLogger(AppProperties.class);

  private final boolean integrationLoggingEnabled;

  public AppProperties(@Value("${integration.logging.enabled:true}") boolean integrationLoggingEnabled) {
    logger.info("integration.logging.enabled = {}", integrationLoggingEnabled);
    this.integrationLoggingEnabled = integrationLoggingEnabled;
  }

  public boolean isIntegrationLoggingEnabled() {
    return integrationLoggingEnabled;
  }
}
