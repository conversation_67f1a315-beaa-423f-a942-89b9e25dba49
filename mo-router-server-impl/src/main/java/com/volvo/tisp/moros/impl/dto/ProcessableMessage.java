package com.volvo.tisp.moros.impl.dto;

import com.volvo.tisp.moros.impl.model.VehicleInformation;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record ProcessableMessage(ReceivedMoMessage receivedMoMessage, VehicleInformation vehicleInformation) {
  public ProcessableMessage {
    Validate.notNull(receivedMoMessage, "receivedMoMessage");
    Validate.notNull(vehicleInformation, "vehicleInformation");
  }
}
