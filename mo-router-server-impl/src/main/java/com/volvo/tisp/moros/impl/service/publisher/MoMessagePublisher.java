package com.volvo.tisp.moros.impl.service.publisher;

import java.util.concurrent.CompletableFuture;

import com.volvo.tisp.moros.impl.dto.ProcessableMessage;
import com.volvo.tisp.moros.impl.service.IntegrationLogger;
import com.volvo.tisp.moros.impl.service.metric.reporter.ServiceIdMetricReporter;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;

public abstract class MoMessagePublisher {
  private static final String SRP_DST_SERVICE = "SRP_DST_SERVICE";
  private static final String SRP_DST_VERSION = "SRP_DST_VERSION";
  private final IntegrationLogger integrationLogger;
  private final ServiceIdMetricReporter serviceIdMetricReporter;

  protected MoMessagePublisher(IntegrationLogger integrationLogger, ServiceIdMetricReporter serviceIdMetricReporter) {
    this.integrationLogger = integrationLogger;
    this.serviceIdMetricReporter = serviceIdMetricReporter;
  }

  abstract public CompletableFuture<Integer> publish(ProcessableMessage processableMessage);

  protected CompletableFuture<Integer> publishMessage(MessagePublisher<ProcessableMessage> messagePublisher, ProcessableMessage processableMessage) {
    return messagePublisher.newMessage()
        .option(SRP_DST_SERVICE, processableMessage.receivedMoMessage().serviceId().value())
        .option(SRP_DST_VERSION, processableMessage.receivedMoMessage().serviceVersion().value())
        .publish(processableMessage)
        .whenComplete((subscriberCount, throwable) -> {
          if (throwable != null) {
            integrationLogger.log(processableMessage.receivedMoMessage(), "PublishMoMessageJmsFailure");
          } else if(subscriberCount > 0) {
            serviceIdMetricReporter.onMoMessageSuccess(processableMessage.receivedMoMessage().serviceId());
            integrationLogger.log(processableMessage.receivedMoMessage(), "PublishMoMessageJmsSuccess");
          }
        });
  }
}
