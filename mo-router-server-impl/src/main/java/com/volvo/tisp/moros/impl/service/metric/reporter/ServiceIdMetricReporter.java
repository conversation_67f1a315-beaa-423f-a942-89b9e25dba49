package com.volvo.tisp.moros.impl.service.metric.reporter;

import org.springframework.stereotype.Component;

import com.volvo.tisp.moros.impl.model.ServiceId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class ServiceIdMetricReporter {
  private static final String SERVICE = "service";
  private static final String SERVICE_ID = "id";

  private final MeterRegistry meterRegistry;

  public ServiceIdMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onMoMessageSuccess(ServiceId serviceId) {
    Validate.notNull(serviceId, SERVICE_ID);
    meterRegistry.counter(SERVICE, Tags.of(SERVICE_ID, serviceId.toString())).increment();
  }
}
