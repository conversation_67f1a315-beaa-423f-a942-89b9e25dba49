package com.volvo.tisp.moros.impl.model;

import java.util.Optional;

import com.volvo.tisp.vc.main.utils.lib.Validate;

// TODO more stringent validation of fields can be done as project matures
public record AssetHardwareId(String partNumber, String serialNumber) {
  public static final String DELIMITER = "-";

  public AssetHardwareId {
    Validate.notEmpty(partNumber, "partNumber");
    Validate.notEmpty(serialNumber, "serialNumber");
  }

  public static Optional<AssetHardwareId> fromString(String assetHardwareIdString) {
    String[] parts = assetHardwareIdString.split(DELIMITER);

    if (parts.length != 2) {
      return Optional.empty();
    }

    return Optional.of(new AssetHardwareId(parts[0], parts[1]));
  }

  public static AssetHardwareId of(String partNumber, String serialNumber) {
    return new AssetHardwareId(partNumber, serialNumber);
  }

  @Override
  public String toString() {
    return String.join(DELIMITER, partNumber, serialNumber);
  }
}
