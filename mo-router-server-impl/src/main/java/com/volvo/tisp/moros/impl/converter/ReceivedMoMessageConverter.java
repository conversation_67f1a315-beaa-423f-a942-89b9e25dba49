package com.volvo.tisp.moros.impl.converter;

import java.util.Optional;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.moros.impl.model.AssetHardwareId;
import com.volvo.tisp.moros.impl.model.Priority;
import com.volvo.tisp.moros.impl.model.ServiceAccessToken;
import com.volvo.tisp.moros.impl.model.ServiceId;
import com.volvo.tisp.moros.impl.model.ServiceVersion;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public final class ReceivedMoMessageConverter implements Function<MoMessage, ReceivedMoMessage> {
  private static Priority createPriority(MoMessage.Priority priority) {
    return switch (priority) {
      case LOW -> Priority.LOW;
      case MID -> Priority.MID;
      case HIGH -> Priority.HIGH;
    };
  }

  @Override
  public ReceivedMoMessage apply(MoMessage moMessage) {
    Validate.notNull(moMessage, "moMessage");

    return new ReceivedMoMessage(
        AssetHardwareId.fromString(moMessage.getAssetId()).orElseThrow(() -> new IllegalStateException("received assetHardwareId malformed: " + moMessage)),
        moMessage.getOnboardTimestamp(),
        moMessage.getPayload(),
        createPriority(moMessage.getPriority()),
        moMessage.getServerTimestamp(),
        new ServiceAccessToken(moMessage.getServiceAccessToken()),
        new ServiceId(moMessage.getServiceId()),
        new ServiceVersion(moMessage.getServiceVersion()),
        TrackingIdentifier.fromString(moMessage.getTrackingId()),
        moMessage.getIsSoftcar(),
        Optional.ofNullable(moMessage.getKeyId()),
        Optional.ofNullable(moMessage.getPayloadSignature()));
  }
}
