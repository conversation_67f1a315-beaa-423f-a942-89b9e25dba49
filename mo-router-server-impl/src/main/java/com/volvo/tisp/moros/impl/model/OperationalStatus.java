package com.volvo.tisp.moros.impl.model;

public enum OperationalStatus {
  OPERATIONAL(true),
  NON_OPERATIONAL(false);
  private final boolean value;

  OperationalStatus(boolean value) {
    this.value = value;
  }

  public boolean getValue() {
    return value;
  }

  public static OperationalStatus fromValue(boolean value) {
    if (value) {
      return OPERATIONAL;
    } else {
      return NON_OPERATIONAL;
    }
  }
}
