package com.volvo.tisp.moros.impl.conf;

import com.wirelesscar.vqv.v1.api.fqv.view.FullView;
import com.wirelesscar.vqv.v1.client.subscription.ViewRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

/**
 * VQV Views and Filters are immutable. If either the query or the filter is updated, the version number for the
 * corresponding query and/or filter must be incremented.
 */
@Configuration
public class VqvConfig {
    private static final String FILTER = "telematicUnitType('VCM')";
    private static final String QUERY =
            "query Vehicle($vehiclePlatformId: String!) {"
                    + "  vehicle(vehiclePlatformId: $vehiclePlatformId) {"
                    + "    vehiclePlatformId"
                    + "    chassisSeries"
                    + "    chassisNumber"
                    + "    operationalStatus"
                    + "    telematicUnit {"
                    + "      serialNumber"
                    + "      partNumber"
                    + "    }"
                    + "  }"
                    + "}";
    private static final long VEHICLE_INFORMATION_FILTER_VERSION = 3;
    private static final long VEHICLE_INFORMATION_QUERY_VERSION = 3;
    private static final String VEHICLE_INFORMATION_VIEW = "vehicleInformation";
    private static final long VEHICLE_INFORMATION_VIEW_VERSION = 3;
    private static final Logger logger = LoggerFactory.getLogger(VqvConfig.class);

    private final ViewRegistry viewRegistry;

    public VqvConfig(ViewRegistry viewRegistry) {
        this.viewRegistry = viewRegistry;
    }

    private static String getFullViewInfo(FullView fullView) {
        final String name = ", name=";
        final String version = ", version=";

        return new StringBuilder()
                .append("[id=")
                .append(fullView.getId())
                .append(name)
                .append(fullView.getName())
                .append(version)
                .append(fullView.getVersion())
                .append(", active=")
                .append(fullView.getActive())
                .append(", query= ")
                .append(fullView.getQuery())
                .append(", filter= ")
                .append(fullView.getFilter())
                .append("]")
                .toString();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void configureVqvView() {
        logger.info("start to register view {}, version {}", VEHICLE_INFORMATION_VIEW, VEHICLE_INFORMATION_VIEW_VERSION);

        FullView fullView = viewRegistry
                .view(VEHICLE_INFORMATION_VIEW, VEHICLE_INFORMATION_VIEW_VERSION)
                .query("VehicleInformationQuery", VEHICLE_INFORMATION_QUERY_VERSION, QUERY)
                .filter("VehicleInformationFilter", VEHICLE_INFORMATION_FILTER_VERSION, FILTER)
                .register();

        logger.info("view info: {}", getFullViewInfo(fullView));
    }
}
