package com.volvo.tisp.moros.impl.conf.properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class ConnectionPoolConfigProperties {
  private static final Logger logger = LoggerFactory.getLogger(ConnectionPoolConfigProperties.class);

  private final long maxConnectionIdleTime;
  private final long maxConnectionLifeTime;
  private final int maxPoolSize;
  private final long maxWaitTime;
  private final int minPoolSize;

  public ConnectionPoolConfigProperties(
      @Value("${mongo.max.idle.time:15}") long maxConnectionIdleTime,
      @Value("${mongo.max.connection.lifetime:30}") long maxConnectionLifeTime,
      @Value("${mongo.max.pool.size:50}") int maxPoolSize,
      @Value("${mongo.max.wait.time:2}") long maxWaitTime,
      @Value("${mongo.min.pool.size:15}") int minPoolSize
  ) {
    Validate.isPositive(maxConnectionIdleTime, "maxConnectionIdleTime");
    Validate.isPositive(maxConnectionLifeTime, "maxConnectionLifeTime");
    Validate.isPositive(maxPoolSize, "maxPoolSize");
    Validate.isPositive(maxWaitTime, "maxWaitTime");
    Validate.isPositive(minPoolSize, "minPoolSize");

    logger.info("ConnectionPoolConfigProperties created with mongo.max.idle.time: {}, mongo.max.connection.lifetime:{},"
            + " mongo.max.pool.size:{}, mongo.max.wait.time:{},  mongo.min.pool.size:{}",
        maxConnectionIdleTime, maxConnectionLifeTime, maxPoolSize, maxWaitTime, minPoolSize);

    this.maxConnectionIdleTime = maxConnectionIdleTime;
    this.maxConnectionLifeTime = maxConnectionLifeTime;
    this.maxPoolSize = maxPoolSize;
    this.maxWaitTime = maxWaitTime;
    this.minPoolSize = minPoolSize;
  }

  public long getMaxConnectionIdleTime() {
    return maxConnectionIdleTime;
  }

  public long getMaxConnectionLifeTime() {
    return maxConnectionLifeTime;
  }

  public int getMaxPoolSize() {
    return maxPoolSize;
  }

  public long getMaxWaitTime() {
    return maxWaitTime;
  }

  public int getMinPoolSize() {
    return minPoolSize;
  }
}
