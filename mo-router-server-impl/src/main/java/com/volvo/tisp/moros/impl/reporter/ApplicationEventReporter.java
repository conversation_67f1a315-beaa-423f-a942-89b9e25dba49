package com.volvo.tisp.moros.impl.reporter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Service
public class ApplicationEventReporter {
  private static final String APP_EVENT = "app-event";
  private static final Logger logger = LoggerFactory.getLogger(ApplicationEventReporter.class);
  private final Counter startedCounter;
  private final Counter stoppedCounter;

  public ApplicationEventReporter(MeterRegistry meterRegistry) {
    this.startedCounter = meterRegistry.counter(APP_EVENT, Tags.of("type", "STARTED"));
    this.stoppedCounter = meterRegistry.counter(APP_EVENT, Tags.of("type", "STOPPED"));
  }

  @EventListener(ApplicationReadyEvent.class)
  public void onApplicationReadyEvent() {
    logger.info("Received ApplicationReadyEvent");

    startedCounter.increment();
  }

  @EventListener(ContextClosedEvent.class)
  public void onContextClosed() {
    logger.info("Received ContextClosedEvent");

    stoppedCounter.increment();
  }
}
