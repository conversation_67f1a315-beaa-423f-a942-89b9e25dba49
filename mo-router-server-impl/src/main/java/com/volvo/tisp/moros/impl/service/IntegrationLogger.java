package com.volvo.tisp.moros.impl.service;

import java.time.Clock;
import java.util.Set;
import java.util.UUID;

import org.springframework.stereotype.Component;

import com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage;
import com.volvo.tisp.integration.log.IntegrationLogEventPublisher;
import com.volvo.tisp.integration.log.v2.AssetIdentifier;
import com.volvo.tisp.integration.log.v2.IntegrationLogEvent;
import com.volvo.tisp.moros.impl.conf.Constant;
import com.volvo.tisp.moros.impl.conf.properties.AppProperties;
import com.volvo.tisp.moros.impl.dto.ReceivedMoMessage;
import com.volvo.tisp.moros.impl.model.Detail;

@Component
public class IntegrationLogger {
  private final AppProperties appProperties;
  private final Clock clock;
  private final IntegrationLogEventPublisher integrationLogEventPublisher;

  public IntegrationLogger(AppProperties appProperties, Clock clock, IntegrationLogEventPublisher integrationLogEventPublisher) {
    this.clock = clock;
    this.appProperties = appProperties;
    this.integrationLogEventPublisher = integrationLogEventPublisher;
  }

  private static Set<AssetIdentifier> getAssetIdentifiers(String assetHardwareId) {
    return Set.of(
        new AssetIdentifier()
            .withAssetIdentifier(assetHardwareId)
            .withType(AssetIdentifier.Type.ASSET_ID));
  }

  public void log(ReceivedMoMessage receivedMoMessage, String detailType) {
    if (appProperties.isIntegrationLoggingEnabled()) {
      log(createIntegrationLogEvent(receivedMoMessage.assetHardwareId().toString(), receivedMoMessage.trackingIdentifier().toString(), new Detail(receivedMoMessage),
          detailType));
    }
  }

  public void log(MoMessage moMessage, String detailType) {
    if (appProperties.isIntegrationLoggingEnabled()) {
      log(createIntegrationLogEvent(moMessage.getAssetId(), moMessage.getTrackingId(), new Detail(moMessage), detailType));
    }
  }

  private IntegrationLogEvent createIntegrationLogEvent(String assetHardwareId, String trackingId, Detail detail, String detailType) {
    return new IntegrationLogEvent()
        .withTrackingId(trackingId)
        .withAssetIdentifiers(getAssetIdentifiers(assetHardwareId))
        .withId(UUID.randomUUID())
        .withSource(Constant.MOROS)
        .withTimestamp(clock.instant().toEpochMilli())
        .withDetail(detail.toMap())
        .withDetailType(detailType)
        .withVersion(IntegrationLogEvent.Version._2);
  }

  @SuppressWarnings("FutureReturnValueIgnored")
  private void log(IntegrationLogEvent integrationLogEvent) {
    integrationLogEventPublisher.publish(integrationLogEvent);
  }
}
