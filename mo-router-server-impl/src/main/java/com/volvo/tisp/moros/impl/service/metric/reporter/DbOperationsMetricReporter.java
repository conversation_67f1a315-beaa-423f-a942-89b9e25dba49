package com.volvo.tisp.moros.impl.service.metric.reporter;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.external.mo.message.client.json.v1.momessage.AssetIdentifierDetails;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class DbOperationsMetricReporter {
  private static final String CRUD = "CRUD";
  private static final String IDENTIFIER = "IDENTIFIER";
  private static final String NAME = "db.vehicle-information";
  private static final String STATUS = "STATUS";
  private final MeterRegistry meterRegistry;

  public DbOperationsMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onCrudDuration(Duration duration, Crud crud) {
    Validate.notNegative(duration, "duration");
    Validate.notNull(crud, "crud");

    meterRegistry.timer(NAME, Tags.of(CRUD, crud.name())).record(duration);
  }

  public void onCrudDuration(Duration duration, Crud crud, Identifier identifier) {
    Validate.notNegative(duration, "duration");
    Validate.notNull(crud, "crud");
    Validate.notNull(identifier, "identifier");

    meterRegistry.timer(NAME, Tags.of(CRUD, crud.name(), IDENTIFIER, identifier.name())).record(duration);
  }

  public void onCrudFailure(Crud crud) {
    Validate.notNull(crud, "crud");

    meterRegistry.counter(NAME, Tags.of(CRUD, crud.name(), STATUS, "FAILURE")).increment();
  }
}
