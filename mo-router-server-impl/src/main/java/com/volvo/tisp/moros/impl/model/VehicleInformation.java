package com.volvo.tisp.moros.impl.model;

import java.util.Optional;

import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record VehicleInformation(
    AssetHardwareId assetHardwareId,
    Optional<ChassisId> chassisId,
    OperationalStatus operationalStatus,
    Version version,
    Optional<Vpi> vpi
) {

  public VehicleInformation {
    Validate.notNull(assetHardwareId, "assetHardwareId");
    Validate.notNull(chassisId, "chassisId");
    Validate.notNull(operationalStatus, "operationalStatus");
    Validate.notNull(version, "version");
    Validate.notNull(vpi, "vpi");
  }

  public static VehicleInformation fromEntity(VehicleInformationEntity vehicleInformationEntity) {
    return new VehicleInformation(AssetHardwareId.of(vehicleInformationEntity.getPartNumber(), vehicleInformationEntity.getSerialNumber()),
        Optional.ofNullable(vehicleInformationEntity.getChassisId()).map(ChassisId::new), OperationalStatus.fromValue(
        vehicleInformationEntity.isOperational()), new Version(vehicleInformationEntity.getVersion()),
        Optional.ofNullable(vehicleInformationEntity.getVpi()).map(Vpi::ofString));
  }
}
