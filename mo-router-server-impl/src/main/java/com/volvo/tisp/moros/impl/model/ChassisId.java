package com.volvo.tisp.moros.impl.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public record ChassisId(String value) {
  public static final String SEPARATOR = "-";

  public ChassisId {
    Validate.notNull(value, "value");
  }

  public static ChassisId fromComponents(String chassisSeries, String chassisNumber) {
    Validate.notNull(chassisSeries, "chassisSeries");
    Validate.notNull(chassisNumber, "chassisNumber");

    return new ChassisId(String.join(SEPARATOR, chassisSeries, chassisNumber));
  }

  @Override
  public String toString() {
    return value;
  }
}
