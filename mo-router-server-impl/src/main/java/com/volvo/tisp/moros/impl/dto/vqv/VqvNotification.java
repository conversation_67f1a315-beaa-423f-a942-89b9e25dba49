package com.volvo.tisp.moros.impl.dto.vqv;

import java.time.Instant;

import com.volvo.tisp.moros.impl.model.VehicleInformation;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.vqv.v1.api.NotificationType;

public record VqvNotification(
    Instant masterDataDeletedTimestamp,
    NotificationType notificationType,
    VehicleInformation vehicleInformation,
    Vpi vpi) {

  public VqvNotification {
    Validate.notNull(notificationType, "notificationType");
    Validate.notNull(vehicleInformation, "vehicleInformation");
    Validate.notNull(vpi, "vpi");
  }

  public boolean isMarkedForDeletion() {
    return masterDataDeletedTimestamp != null;
  }

  public static final class Builder {
    private Instant masterDataDeletedTimestamp;
    private NotificationType notificationType;
    private VehicleInformation vehicleInformation;
    private Vpi vpi;

    public VqvNotification build() {
      return new VqvNotification(masterDataDeletedTimestamp, notificationType, vehicleInformation, vpi);
    }

    /**
     * masterDataDeletedTimestamp is non-null only when vehicle is deleted from VR
     */
    public Builder setMasterDataDeletedTimestamp(Instant masterDataDeletedTimestamp) {
      this.masterDataDeletedTimestamp = masterDataDeletedTimestamp;
      return this;
    }

    public Builder setNotificationType(NotificationType notificationType) {
      Validate.notNull(notificationType, "notificationType");

      this.notificationType = notificationType;
      return this;
    }

    public Builder setVehicleInformation(VehicleInformation vehicleInformation) {
      Validate.notNull(vehicleInformation, "vehicleInformation");

      this.vehicleInformation = vehicleInformation;
      return this;
    }

    public Builder setVpi(Vpi vpi) {
      Validate.notNull(vpi, "vpi");

      this.vpi = vpi;
      return this;
    }
  }
}
