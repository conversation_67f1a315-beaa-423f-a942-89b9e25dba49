package com.volvo.tisp.moros.impl.conf.properties;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

@ConfigurationProperties("external.jms.publisher")
@ConfigurationPropertiesScan
public class ExternalJmsServiceRoutingMapper {
  private final Map<Integer, String> serviceIdToQueueNameMap;

  public ExternalJmsServiceRoutingMapper(Map<Integer, String> serviceIdToQueueNameMap) {
    this.serviceIdToQueueNameMap = Optional.ofNullable(serviceIdToQueueNameMap).orElse(Map.of());
  }

  public Collection<Integer> getAllowedServiceIds() {
    return serviceIdToQueueNameMap.keySet();
  }

  public Optional<String> getQueueName(int serviceId) {
    return Optional.ofNullable(serviceIdToQueueNameMap.get(serviceId));
  }

  public Map<Integer, String> getServiceIdToQueueNameMap() {
    return serviceIdToQueueNameMap;
  }
}
