# Global properties
mongock:
  migration-scan-package: com.volvo.tisp.moros.database.migration
  transaction-enabled: false

server.port: 48090

spring:
  flyway.enabled: false
  jms.listener.max-concurrency: 5
  liquibase.enabled: false
---
#LOCAL
spring:
  config.activate.on-profile: local_local_local
  cloud.aws.region.static: us-east-1
  data.mongodb.uri: **********************************************************************

sqs:
  region: us-east-1
  endpoint-override: http://localhost:4566
  mo-message-queue.url: http://localhost:4566/000000000000/mo-message-queue

kinesis.data.stream.arn: arn:aws:kinesis:default000000000000:stream/integration-log-stream

management.influx.metrics.export.enabled: false

integration.logging.enabled: false
