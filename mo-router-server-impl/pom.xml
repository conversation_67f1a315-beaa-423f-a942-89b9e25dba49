<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>mo-router-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>mo-router-server-impl</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>mo-router-server-database</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-jms</artifactId>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-influx</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>mo-router-client-json</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.vqv</groupId>
      <artifactId>vehiclequeryview-client-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.vqv</groupId>
      <artifactId>vehiclequeryview-client-impl-http</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.subscriptionrepository</groupId>
      <artifactId>subscriptionrepository-client-impl</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>common-dto-lib</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.connectivity</groupId>
      <artifactId>asset-messaging-gateway-client-json</artifactId>
    </dependency>
    <dependency>
      <groupId>io.awspring.cloud</groupId>
      <artifactId>spring-cloud-aws-starter-sqs</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
      <groupId>software.amazon.awssdk</groupId>
      <artifactId>sqs</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>integration-log-event-repository-client-json</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>external-mo-router-client-json</artifactId>
    </dependency>
    <!-- Spring Cloud starters -->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-zookeeper-config</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>test-utils-lib</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>testcontainers</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>localstack</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
