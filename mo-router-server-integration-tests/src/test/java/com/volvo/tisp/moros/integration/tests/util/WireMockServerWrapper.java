package com.volvo.tisp.moros.integration.tests.util;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import java.io.Closeable;

public record WireMockServerWrapper(WireMockServer wireMockServer) implements Closeable {
  public WireMockServerWrapper {
    Validate.notNull(wireMockServer, "wireMockServer");

  }

  @Override
  public void close() {
    wireMockServer.stop();
  }
}
