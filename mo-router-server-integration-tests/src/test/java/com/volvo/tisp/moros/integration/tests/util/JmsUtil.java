package com.volvo.tisp.moros.integration.tests.util;

import java.io.IOException;
import java.time.Duration;
import java.util.Optional;

import jakarta.jms.Connection;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.DeliveryMode;
import jakarta.jms.Destination;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.MessageConsumer;
import jakarta.jms.MessageProducer;
import jakarta.jms.Session;
import jakarta.jms.TextMessage;

import org.apache.activemq.artemis.jms.client.ActiveMQConnectionFactory;
import org.apache.activemq.artemis.jms.client.ActiveMQTextMessage;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.moros.impl.jms.VqvNotificationJmsController;
import com.volvo.tisp.vc.mo.message.client.json.v1.InternalMoMessage;
import com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage;
import com.wirelesscar.vqv.v1.api.UpdateNotificationMessageType;

public final class JmsUtil {
  public static final String EXT_MO_QUEUE_NAME = "LOCAL.LOCAL.LOCAL.COMPSHRT.EXT-MO-MESSAGE.IN";
  public static final String MO_QUEUE_NAME = "LOCAL.LOCAL.LOCAL.COMPSHRT.MO-MESSAGE.IN";
  public static final String MO__WITH_ASSET_HARDWARE_ID_QUEUE_NAME = "LOCAL.LOCAL.LOCAL.COMPSHRT.MO-MESSAGE-WITH-ASSET-HARDWARE-ID.IN";
  static final String AMQ_URL = "tcp://localhost:64567";
  private static final String QUEUE_PREFIX = "LOCAL.LOCAL.LOCAL.COMPSHRT.";

  private JmsUtil() {
    throw new IllegalStateException();
  }

  public static <T> Optional<T> consumeAndParseMoMessage(String fullQueueName, Duration timeout, ObjectMapper objectMapper, Class<T> clazz) throws Exception {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    Optional<Message> optionalMessage = consumeOneMessage(connectionFactory, fullQueueName, timeout);

    if (optionalMessage.isEmpty()) {
      return Optional.empty();
    }
    return Optional.of(parseMoMessage(optionalMessage.get(), objectMapper, clazz));
  }

  public static void publishVqvNotification(String body) throws Exception {
    sendRequest(body, QUEUE_PREFIX + VqvNotificationJmsController.VQV_NOTIFICATION_DESTINATION, UpdateNotificationMessageType.VIEW_NOTIFICATION_MESSAGE_TYPE,
        UpdateNotificationMessageType.VIEW_NOTIFICATION_MESSAGE_TYPE_VERSION, Optional.empty());
  }

  private static <T> Optional<T> consumeAndUnmarshalMessage(String fullQueueName, Duration timeout, Class<T> clazz) throws Exception {
    Optional<Message> optionalMessage = consumeOneMessage(fullQueueName, timeout);

    if (optionalMessage.isEmpty()) {
      return Optional.empty();
    }

    return Optional.of(IntegrationTestHelper.unmarshal(optionalMessage.get(), clazz));
  }

  private static Optional<Message> consumeOneMessage(String fullQueueName, Duration timeout) throws JMSException {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    return consumeOneMessage(connectionFactory, fullQueueName, timeout);
  }

  private static Optional<Message> consumeOneMessage(ConnectionFactory connectionFactory, String fullQueueName, Duration timeout) throws JMSException {
    try (Connection connection = connectionFactory.createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        Destination destination = session.createQueue(fullQueueName);

        try (MessageConsumer messageConsumer = session.createConsumer(destination)) {
          Message message = messageConsumer.receive(timeout.toMillis());

          return Optional.ofNullable(message);
        }
      }
    }
  }

  private static ActiveMQConnectionFactory createActiveMQConnectionFactory() {
    return new ActiveMQConnectionFactory(AMQ_URL);
  }

  private static <T> T parseMoMessage(Message message, ObjectMapper objectMapper, Class<T> clazz) throws IOException, JMSException {
    TextMessage textMessage = (ActiveMQTextMessage) message;
    return objectMapper.readValue(textMessage.getText(), clazz);
  }

  private static void publishMessage(ConnectionFactory connectionFactory, String fullQueueName, String body, String messageType, String version,
      Optional<String> replyToHeader) throws JMSException {
    try (Connection connection = connectionFactory.createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        TextMessage textMessage = session.createTextMessage(body);

        textMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), messageType);
        textMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), version);

        if (replyToHeader.isPresent()) {
          textMessage.setJMSReplyTo(session.createQueue(replyToHeader.get()));
        }

        Destination destination = session.createQueue(fullQueueName);

        try (MessageProducer messageProducer = session.createProducer(destination)) {
          messageProducer.setDeliveryMode(DeliveryMode.NON_PERSISTENT);
          messageProducer.send(textMessage);
        }
      }
    }
  }

  private static void sendRequest(String body, String fullQueueName, String messageType, String messageVersion, Optional<String> replyToHeader)
      throws Exception {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    publishMessage(connectionFactory, fullQueueName, body, messageType, messageVersion, replyToHeader);
  }
}
