package com.volvo.tisp.moros.integration.tests.util;

import org.junit.jupiter.api.Assertions;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public final class RestUtil {
  private RestUtil() {
    throw new IllegalArgumentException();
  }

  public static RestTemplate createRestTemplate() {
    return new RestTemplate();
  }

  public static void verifyOkHttpResponse(String urlString) {
    ResponseEntity<String> responseEntity =
        RestUtil.createRestTemplate().exchange(urlString, HttpMethod.GET, null, String.class);
    Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode(), responseEntity::getBody);
  }
}
