package com.volvo.tisp.moros.integration.tests.util;

import java.io.IOException;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import jakarta.jms.JMSException;
import jakarta.jms.TextMessage;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;

import javax.xml.transform.stream.StreamSource;

import org.apache.activemq.artemis.core.config.Configuration;
import org.apache.activemq.artemis.core.config.impl.ConfigurationImpl;
import org.apache.activemq.artemis.core.server.embedded.EmbeddedActiveMQ;
import org.apache.activemq.artemis.jms.client.ActiveMQTextMessage;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.Slf4jNotifier;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.google.common.collect.ArrayListMultimap;
import com.volvo.tisp.moros.database.api.VehicleInformationRepository;
import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;
import com.volvo.tisp.moros.impl.model.AssetHardwareId;
import com.volvo.tisp.moros.impl.model.ChassisId;
import com.volvo.tisp.moros.impl.model.ServiceId;
import com.volvo.tisp.moros.impl.model.ServiceVersion;
import com.volvo.tisp.moros.impl.model.Version;
import com.volvo.tisp.subscriptionrepository.client.Destination;
import com.volvo.tisp.subscriptionrepository.client.SubscriptionStubber;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.mo.message.client.json.v1.MessageTypes;

import io.opentelemetry.api.GlobalOpenTelemetry;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kinesis.KinesisAsyncClient;
import software.amazon.awssdk.services.kinesis.model.CreateStreamRequest;
import software.amazon.awssdk.services.kinesis.model.DescribeStreamSummaryRequest;

public final class IntegrationTestHelper {
  public static final AssetHardwareId ASSET_HARDWARE_ID = AssetHardwareId.of("000001", "000002");
  public static final ChassisId CHASSIS_ID = new ChassisId("SERIES-NUMBER");
  public static final ServiceId EXT_JMS_SERVICE_ID = new ServiceId(321);
  public static final ServiceVersion EXT_JMS_SERVICE_VERSION = new ServiceVersion(1);
  public static final ServiceId SERVICE_ID = new ServiceId(1337);
  public static final ServiceVersion SERVICE_VERSION = new ServiceVersion(42);
  public static final String SQS_OUT_QUEUE_NAME = "mo-message-out-queue";
  public static final Version VERSION = new Version(2L);
  public static final Vpi VPI = Vpi.ofString("1FAC35BF1EDB1DFF91AAC0BEBC56F22B");
  private static final String INTEGRATION_LOG_STREAM = "integration-log-stream";

  private IntegrationTestHelper() {
    throw new IllegalStateException();
  }

  public static void awaitVPIExistsInDB(VehicleInformationRepository vehicleInformationRepository, String vpi) {
    Awaitility.await().atMost(Duration.ofSeconds(3)).until(() -> vehicleInformationRepository.findByVpi(vpi).isPresent());
  }

  public static EmbeddedActiveMqWrapper createAndStartEmbeddedActiveMqWrapper() throws Exception {
    EmbeddedActiveMQ embeddedActiveMQ = new EmbeddedActiveMQ();
    embeddedActiveMQ.setConfiguration(createActiveMQConfiguration());
    embeddedActiveMQ.start();
    return new EmbeddedActiveMqWrapper(embeddedActiveMQ);
  }

  public static WireMockServerWrapper createAndStartWireMockServer() {
    WireMockServer wireMockServer = createWireMockServer();
    wireMockServer.start();

    return new WireMockServerWrapper(wireMockServer);
  }

  public static String createStream(LocalStackContainer localStackContainer)
      throws ExecutionException, InterruptedException {
    getKinesisAsyncClient(localStackContainer)
        .createStream(CreateStreamRequest.builder()
            .streamName(INTEGRATION_LOG_STREAM)
            .shardCount(1)
            .build())
        .get();

    return getKinesisAsyncClient(localStackContainer).describeStreamSummary(DescribeStreamSummaryRequest.builder().streamName(INTEGRATION_LOG_STREAM).build())
        .get()
        .streamDescriptionSummary()
        .streamARN();
  }

  public static VehicleInformationEntity createVehicleInformationEntity(boolean operationalStatus) {
    VehicleInformationEntity vehicleInformationEntity = new VehicleInformationEntity();
    Instant now = Clock.systemUTC().instant();

    vehicleInformationEntity.setChassisId(CHASSIS_ID.value());
    vehicleInformationEntity.setCreatedAt(now);
    vehicleInformationEntity.setOperational(operationalStatus);
    vehicleInformationEntity.setPartNumber(ASSET_HARDWARE_ID.partNumber());
    vehicleInformationEntity.setSerialNumber(ASSET_HARDWARE_ID.serialNumber());
    vehicleInformationEntity.setVpi(VPI.toString());
    vehicleInformationEntity.setUpdatedAt(now);
    vehicleInformationEntity.setVersion(VERSION.value());

    return vehicleInformationEntity;
  }

  public static String getInputString(Path path) throws IOException {
    return Files.readString(path, StandardCharsets.UTF_8);
  }

  public static KinesisAsyncClient getKinesisAsyncClient(LocalStackContainer kinesisLocalStackContainer) {
    return KinesisAsyncClient.builder()
        .region(Region.of(kinesisLocalStackContainer.getRegion()))
        .endpointOverride(kinesisLocalStackContainer.getEndpointOverride(LocalStackContainer.Service.KINESIS))
        .build();
  }

  public static void mockAppConfig(MongoDBContainer mongoDBContainer, WireMockServerWrapper wireMockServerWrapper) {
    mockAppConfig(mongoDBContainer, wireMockServerWrapper,
        Map.of("moMessageQueue", "http://localhost:4566/000000000000/mo-message-queue",
            "moMessageOutQueue", "http://localhost:4566/000000000000/mo-message-out-queue"), null);
  }

  public static void mockAppConfig(MongoDBContainer mongoDBContainer, WireMockServerWrapper wireMockServerWrapper, Map<String, String> configMap,
      LocalStackContainer sqsLocalStackContainer) {
    GlobalOpenTelemetry.resetForTest();

    final int wireMockPortNumber = wireMockServerWrapper.wireMockServer().port();

    System.setProperty("spring.artemis.broker-url", JmsUtil.AMQ_URL);

    System.setProperty("management.influx.metrics.export.uri", "http://localhost:" + wireMockPortNumber);
    System.setProperty("management.influx.metrics.export.retention-policy", "30days");

    if (sqsLocalStackContainer != null) {
      System.setProperty("sqs.endpoint-override", String.valueOf(sqsLocalStackContainer.getEndpointOverride(LocalStackContainer.Service.SQS)));
    }
    System.setProperty("sqs.mo-message-queue.url", configMap.get("moMessageQueue"));
    System.setProperty("sqs.mo.message.out.queue.url", configMap.get("moMessageOutQueue"));

    System.setProperty("spring.data.mongodb.uri", mongoDBContainer.getReplicaSetUrl());

    System.setProperty("servicediscovery.subr", "http://localhost:" + wireMockPortNumber + "/");
    System.setProperty("servicediscovery.vqv", "http://localhost:" + wireMockPortNumber + "/");
    System.setProperty("sqs.mo-message.message.visibility", "2S");
    System.setProperty("kinesis.data.stream.arn", configMap.getOrDefault("streamArn", "streamArn"));
    System.setProperty("spring.cloud.aws.region.static", "eu-west-1");
  }

  public static ConfigurableApplicationContext runSpringApplication(Class<?> configurationClass) {
    return SpringApplication.run(new Class<?>[] {configurationClass}, new String[0]);
  }

  public static MongoDBContainer startMongoContainer() {
    MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:6.0");
    mongoDBContainer.withReuse(true).start();
    return mongoDBContainer;
  }

  public static void stubExternalMoMessageSubRepo() {
    Destination destinationExternal = new Destination(com.volvo.tisp.external.mo.message.client.json.v1.MessageTypes.EXTERNAL_MO_MESSAGE, MessageTypes.VERSION_1_0,
        "activemq:queue:" + JmsUtil.EXT_MO_QUEUE_NAME, "destinationExternal");

    ArrayListMultimap<String, Object> optionsMap = ArrayListMultimap.create();
    optionsMap.put("SRP_DST_SERVICE", EXT_JMS_SERVICE_ID.value());
    optionsMap.put("SRP_DST_VERSION", EXT_JMS_SERVICE_VERSION.value());

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(com.volvo.tisp.external.mo.message.client.json.v1.MessageTypes.EXTERNAL_MO_MESSAGE)
        .withOptions(optionsMap)
        .thenMessageShouldBeDeliveredTo(destinationExternal);
  }

  public static void stubMoMessageSubRepo() {
    Destination destinationForMoMessage = new Destination(MessageTypes.MO_MESSAGE,
        MessageTypes.VERSION_1_0, "activemq:queue:" + JmsUtil.MO_QUEUE_NAME,
        "destinationForMoMessage");

    ArrayListMultimap<String, Object> optionsMap = ArrayListMultimap.create();
    optionsMap.put("SRP_DST_SERVICE", SERVICE_ID.value());
    optionsMap.put("SRP_DST_VERSION", VERSION.value());


    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(MessageTypes.MO_MESSAGE)
        .withOptions(optionsMap)
        .thenMessageShouldBeDeliveredTo(destinationForMoMessage);
  }

  public static void stubMoMessageWithAssetHardwareIdSubRepo() {
    Destination destinationForMoMessageWithAssetHardwareId = new Destination(
        MessageTypes.MO_MESSAGE_WITH_ASSET_HARDWARE_ID,
        MessageTypes.VERSION_1_0, "activemq:queue:" + JmsUtil.MO__WITH_ASSET_HARDWARE_ID_QUEUE_NAME,
        "destinationForMoMessageWithAssetHardwareId");

    ArrayListMultimap<String, Object> optionsMap = ArrayListMultimap.create();
    optionsMap.put("SRP_DST_SERVICE", SERVICE_ID.value());
    optionsMap.put("SRP_DST_VERSION", VERSION.value());

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(MessageTypes.MO_MESSAGE_WITH_ASSET_HARDWARE_ID)
        .withOptions(optionsMap)
        .thenMessageShouldBeDeliveredTo(destinationForMoMessageWithAssetHardwareId);
  }

  public static void stubVqv(WireMockServerWrapper wireMockServerWrapper) {
    WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer();
    wireMockServer.stubFor(WireMock.post("/query").willReturn(createVqvResponse()));
    wireMockServer.stubFor(WireMock.post("/filter").willReturn(createVqvResponse()));
    wireMockServer.stubFor(WireMock.post("/view").willReturn(createVqvResponse()));
  }

  public static <T> T unmarshal(jakarta.jms.Message message, Class<T> type) throws JAXBException, JMSException {
    TextMessage textMessage = (ActiveMQTextMessage) message;
    JAXBContext jaxbContext = JAXBContext.newInstance(type);
    Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

    try (StringReader stringReader = new StringReader(textMessage.getText())) {
      return unmarshaller.unmarshal(new StreamSource(stringReader), type).getValue();
    }
  }

  private static Configuration createActiveMQConfiguration() throws Exception {
    return new ConfigurationImpl().addAcceptorConfiguration("tcp", JmsUtil.AMQ_URL).setPersistenceEnabled(false).setSecurityEnabled(false);
  }

  private static ResponseDefinitionBuilder createVqvResponse() {
    return WireMock
        .aResponse()
        .withStatus(200)
        .withBody("{}")
        .withHeader("Content-Type", "application/json");
  }

  private static WireMockServer createWireMockServer() {
    WireMockConfiguration wireMockConfiguration = new WireMockConfiguration().dynamicPort().notifier(new Slf4jNotifier(false));
    return new WireMockServer(wireMockConfiguration);
  }
}
