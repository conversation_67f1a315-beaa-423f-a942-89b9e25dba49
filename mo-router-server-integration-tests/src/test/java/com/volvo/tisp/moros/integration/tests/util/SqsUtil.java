package com.volvo.tisp.moros.integration.tests.util;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.utility.DockerImageName;

import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.QueueAttributeName;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

public class SqsUtil {
  public static final String ACCOUNT_NO = "************";
  public static final String REGION = "us-east-1";

  private SqsUtil() {
    throw new IllegalStateException();
  }

  public static String createQueue(LocalStackContainer localStackContainer, String queueName) throws ExecutionException, InterruptedException {
    return getSqsAsyncClient(localStackContainer).createQueue(createCreateQueueRequest(queueName))
        .thenCompose(createQueueResponse -> CompletableFuture.completedFuture(createQueueResponse.queueUrl()))
        .get();
  }

  public static String createQueueWithDLQ(LocalStackContainer localStackContainer, String queueName, String dlqName)
      throws ExecutionException, InterruptedException {
    return getSqsAsyncClient(localStackContainer).createQueue(createCreateQueueRequestWithDLQ(queueName, dlqName))
        .thenCompose(createQueueResponse -> CompletableFuture.completedFuture(createQueueResponse.queueUrl()))
        .get();
  }

  public static Message receiveOneMessageFromSqs(String queueNameUrl, LocalStackContainer localStackContainer) throws ExecutionException, InterruptedException {
    Message message = null;

    ReceiveMessageRequest receiveMessageRequest = ReceiveMessageRequest.builder()
        .maxNumberOfMessages(1)
        .queueUrl(queueNameUrl)
        .visibilityTimeout(5)
        .waitTimeSeconds(5)
        .build();
    CompletableFuture<ReceiveMessageResponse> receiveMessageResponseCompletableFuture = getSqsAsyncClient(localStackContainer).receiveMessage(
        receiveMessageRequest);

    if (!receiveMessageResponseCompletableFuture.get().messages().isEmpty()) {
      message = receiveMessageResponseCompletableFuture.get().messages().stream().findFirst().get();
    }
    return message;
  }

  public static CompletableFuture<SendMessageResponse> sendMessage(LocalStackContainer localStackContainer, String messageBody, String queueUrl) {
    return getSqsAsyncClient(localStackContainer).sendMessage(SendMessageRequest.builder().queueUrl(queueUrl).messageBody(messageBody).build());
  }

  public static LocalStackContainer startLocalStackContainer() {
    System.setProperty("aws.accessKeyId", "foo");
    System.setProperty("aws.secretAccessKey", "bar");
    LocalStackContainer localStackContainer = new LocalStackContainer(DockerImageName.parse("localstack/localstack:3.0.2")).withServices(
        LocalStackContainer.Service.SQS, LocalStackContainer.Service.KINESIS);
    localStackContainer.start();

    return localStackContainer;
  }

  private static CreateQueueRequest createCreateQueueRequest(String queueName) {
    return CreateQueueRequest.builder().queueName(queueName).build();
  }

  private static CreateQueueRequest createCreateQueueRequestWithDLQ(String queueName, String dlqName) {
    String redrivePolicy = "{\"maxReceiveCount\":\"1\", \"deadLetterTargetArn\":\"" + getQueueArn(dlqName) + "\"}";

    return CreateQueueRequest.builder().queueName(queueName).attributes(Map.of(QueueAttributeName.REDRIVE_POLICY, redrivePolicy)).build();
  }

  private static String getQueueArn(final String queueName) {
    return "arn:aws:sqs:" + REGION + ":" + ACCOUNT_NO + ":" + queueName;
  }

  private static SqsAsyncClient getSqsAsyncClient(LocalStackContainer sqsLocalStackContainer) {
    return SqsAsyncClient.builder()
        .region(Region.of(sqsLocalStackContainer.getRegion()))
        .endpointOverride(sqsLocalStackContainer.getEndpointOverride(LocalStackContainer.Service.SQS))
        .build();
  }
}
