package com.volvo.tisp.moros.integration.tests;

import static com.volvo.tisp.moros.integration.tests.util.IntegrationTestHelper.EXT_JMS_SERVICE_ID;
import static com.volvo.tisp.moros.integration.tests.util.IntegrationTestHelper.EXT_JMS_SERVICE_VERSION;

import java.time.Duration;
import java.time.Instant;
import java.util.Base64;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.connectivity.asset.messaging.gateway.client.json.v1.MoMessage;
import com.volvo.tisp.external.mo.message.client.json.v1.momessage.AssetIdentifierDetails;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.moros.database.api.VehicleInformationRepository;
import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;
import com.volvo.tisp.moros.impl.conf.AppConfig;
import com.volvo.tisp.moros.impl.model.OperationalStatus;
import com.volvo.tisp.moros.impl.model.ServiceAccessToken;
import com.volvo.tisp.moros.impl.model.ServiceId;
import com.volvo.tisp.moros.impl.model.ServiceVersion;
import com.volvo.tisp.moros.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.moros.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.moros.integration.tests.util.JmsUtil;
import com.volvo.tisp.moros.integration.tests.util.SqsUtil;
import com.volvo.tisp.moros.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;

import software.amazon.awssdk.services.sqs.model.Message;

class MoMessageIntegrationTest {
  private static final String CHASSIS_ID = IntegrationTestHelper.CHASSIS_ID.value();
  private static final String DLQ_NAME = "mo-message-queue-dlq";

  private static final Boolean IS_SOFTCAR = Boolean.TRUE;
  private static final String KEY_ID = "testKeyId";
  private static final Instant ONBOARD_TIMESTAMP = Instant.ofEpochSecond(5);
  private static final ImmutableByteArray PAYLOAD = ImmutableByteArray.of(new byte[] {1, 2, 3, 4, 5});
  private static final String BASE64_PAYLOAD = Base64.getEncoder().encodeToString(PAYLOAD.toByteArray());
  private static final String PAYLOAD_SIGNATURE = "hdsjksdah";
  private static final String QUEUE_NAME = "mo-message-queue";
  private static final Instant SERVER_TIMESTAMP = Instant.ofEpochSecond(10);
  private static final ServiceAccessToken SERVICE_ACCESS_TOKEN = new ServiceAccessToken("AQIDBAU=");
  private static final TrackingIdentifier TRACKING_ID = TrackingIdentifier.create();

  private static MoMessage createMoMessage(ServiceId serviceId, ServiceVersion serviceVersion) {
    MoMessage moMessage = new MoMessage();
    moMessage.setAssetId(IntegrationTestHelper.ASSET_HARDWARE_ID.toString());
    moMessage.setOnboardTimestamp(ONBOARD_TIMESTAMP.getEpochSecond());
    moMessage.setPayload(BASE64_PAYLOAD);
    moMessage.setPriority(MoMessage.Priority.HIGH);
    moMessage.setServerTimestamp(SERVER_TIMESTAMP.getEpochSecond());
    moMessage.setServiceAccessToken(SERVICE_ACCESS_TOKEN.toString());
    moMessage.setServiceId(serviceId.value());
    moMessage.setServiceVersion(serviceVersion.value());
    moMessage.setTrackingId(TRACKING_ID.toString());
    moMessage.setIsSoftcar(IS_SOFTCAR);
    moMessage.setKeyId(KEY_ID);
    moMessage.setPayloadSignature(PAYLOAD_SIGNATURE);

    return moMessage;
  }

  private static VehicleInformationEntity persistVehicleInformationEntity(VehicleInformationRepository vehicleInformationRepository) {
    VehicleInformationEntity vehicleInformationEntity = vehicleInformationRepository.save(
        IntegrationTestHelper.createVehicleInformationEntity(OperationalStatus.OPERATIONAL.getValue()));
    IntegrationTestHelper.awaitVPIExistsInDB(vehicleInformationRepository, vehicleInformationEntity.getVpi());

    return vehicleInformationEntity;
  }

  private static void verifyMessageInDLQ(LocalStackContainer localStackContainer, String dlqNameUrl, ObjectMapper objectMapper) {
    Awaitility.await().atMost(3, TimeUnit.SECONDS).untilAsserted(() -> {
      Message message = SqsUtil.receiveOneMessageFromSqs(dlqNameUrl, localStackContainer);
      verifyMoMessage(objectMapper.readValue(message.body(), MoMessage.class));
    });
  }

  private static void verifyMoMessage(MoMessage moMessage) {
    Assertions.assertEquals(IntegrationTestHelper.ASSET_HARDWARE_ID.toString(), moMessage.getAssetId());
    Assertions.assertEquals(IntegrationTestHelper.SERVICE_ID.value(), moMessage.getServiceId());
    Assertions.assertEquals(IntegrationTestHelper.SERVICE_VERSION.value(), moMessage.getServiceVersion());
    Assertions.assertEquals(ONBOARD_TIMESTAMP.getEpochSecond(), moMessage.getOnboardTimestamp());
    Assertions.assertEquals(SERVER_TIMESTAMP.getEpochSecond(), moMessage.getServerTimestamp());
    Assertions.assertEquals(BASE64_PAYLOAD, moMessage.getPayload());
    Assertions.assertEquals(PAYLOAD_SIGNATURE, moMessage.getPayloadSignature());
    Assertions.assertEquals(KEY_ID, moMessage.getKeyId());
  }

  private static void verifyMoMessagesPublishedToExtJms(ObjectMapper objectMapper) throws Exception {
    com.volvo.tisp.external.mo.message.client.json.v1.MoMessage moMessage = JmsUtil.consumeAndParseMoMessage(JmsUtil.EXT_MO_QUEUE_NAME, Duration.ofSeconds(6),
        objectMapper, com.volvo.tisp.external.mo.message.client.json.v1.MoMessage.class).orElseThrow();
    verifyPublishedExtMoMessage(moMessage);
  }

  private static void verifyMoMessagesPublishedToJms(String vpi, ObjectMapper objectMapper) throws Exception {
    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = JmsUtil.consumeAndParseMoMessage(JmsUtil.MO_QUEUE_NAME, Duration.ofSeconds(6),
        objectMapper, com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage.class).orElseThrow();
    verifyPublishedMoMessage(moMessage);
    Assertions.assertEquals(vpi, moMessage.getVehiclePlatformId());
  }

  private static void verifyNoMoMessagesPublishedToJms(ObjectMapper objectMapper) throws Exception {
    Optional<com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage> moMessageOptional = JmsUtil.consumeAndParseMoMessage(JmsUtil.MO_QUEUE_NAME,
        Duration.ofSeconds(6), objectMapper, com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage.class);
    Assertions.assertTrue(moMessageOptional.isEmpty());
  }

  private static void verifyPublishedExtMoMessage(com.volvo.tisp.external.mo.message.client.json.v1.MoMessage actual) {
    Assertions.assertEquals(TRACKING_ID.toString(), actual.getTrackingId());
    Assertions.assertEquals(EXT_JMS_SERVICE_ID.value(), actual.getServiceId());
    Assertions.assertEquals(EXT_JMS_SERVICE_VERSION.value(), actual.getServiceVersion());
    Assertions.assertEquals(ONBOARD_TIMESTAMP.getEpochSecond(), actual.getOnboardTimestamp());
    Assertions.assertEquals(BASE64_PAYLOAD, actual.getPayload());
    Assertions.assertEquals(
        new AssetIdentifierDetails().withAssetIdentifierType(AssetIdentifierDetails.AssetIdentifierType.CHASSIS_ID).withAssetIdentifier(CHASSIS_ID),
        actual.getAssetIdentifiers().get(0));
  }

  private static void verifyPublishedMoMessage(com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage actual) {
    Assertions.assertEquals(TRACKING_ID.toString(), actual.getTrackingId());
    Assertions.assertEquals(IntegrationTestHelper.SERVICE_ID.value(), actual.getServiceId());
    Assertions.assertEquals(IntegrationTestHelper.SERVICE_VERSION.value(), actual.getServiceVersion());
    Assertions.assertEquals(ONBOARD_TIMESTAMP.getEpochSecond(), actual.getOnboardTimestamp());
    Assertions.assertEquals(BASE64_PAYLOAD, actual.getPayload());
    Assertions.assertEquals(PAYLOAD_SIGNATURE, actual.getPayloadSignature());
    Assertions.assertEquals(KEY_ID, actual.getKeyId());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        LocalStackContainer localStackContainer = SqsUtil.startLocalStackContainer();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      WireMock.configureFor(wireMockServerWrapper.wireMockServer().port());
      String dlqUrl = SqsUtil.createQueue(localStackContainer, DLQ_NAME);
      String sqsPublishQueueUrl = SqsUtil.createQueue(localStackContainer, IntegrationTestHelper.SQS_OUT_QUEUE_NAME);
      String streamArn = IntegrationTestHelper.createStream(localStackContainer);
      String queueUrl = SqsUtil.createQueueWithDLQ(localStackContainer, QUEUE_NAME, DLQ_NAME);

      Map<String, String> configMap = Map.of("moMessageQueue", queueUrl, "moMessageOutQueue", sqsPublishQueueUrl, "streamArn", streamArn);
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, configMap, localStackContainer);
      IntegrationTestHelper.stubMoMessageSubRepo();
      IntegrationTestHelper.stubExternalMoMessageSubRepo();
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        VehicleInformationRepository vehicleInformationRepository = configurableApplicationContext.getBean(VehicleInformationRepository.class);
        VehicleInformationEntity vehicleInformationEntity = persistVehicleInformationEntity(vehicleInformationRepository);
        ObjectMapper objectMapper = configurableApplicationContext.getBean(ObjectMapper.class);

        //Internal JMS flow
        String messageBody = objectMapper.writeValueAsString(createMoMessage(IntegrationTestHelper.SERVICE_ID, IntegrationTestHelper.SERVICE_VERSION));
        SqsUtil.sendMessage(localStackContainer, messageBody, queueUrl);
        verifyMoMessagesPublishedToJms(vehicleInformationEntity.getVpi(), objectMapper);
        Assertions.assertNull(SqsUtil.receiveOneMessageFromSqs(queueUrl, localStackContainer));

        //External JMS flow
        String extJmsMessageBody = objectMapper.writeValueAsString(createMoMessage(EXT_JMS_SERVICE_ID, EXT_JMS_SERVICE_VERSION));
        SqsUtil.sendMessage(localStackContainer, extJmsMessageBody, queueUrl);
        verifyMoMessagesPublishedToExtJms(objectMapper);

        vehicleInformationRepository.delete(vehicleInformationEntity);

        SqsUtil.sendMessage(localStackContainer, messageBody, queueUrl);
        verifyMessageInDLQ(localStackContainer, dlqUrl, objectMapper);
        Assertions.assertNull(SqsUtil.receiveOneMessageFromSqs(queueUrl, localStackContainer));
        verifyNoMoMessagesPublishedToJms(objectMapper);
      }
    }
  }
}
