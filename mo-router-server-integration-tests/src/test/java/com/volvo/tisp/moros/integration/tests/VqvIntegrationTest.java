package com.volvo.tisp.moros.integration.tests;

import java.nio.file.Path;
import java.time.Duration;
import java.util.Optional;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.cache.CacheManager;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;

import com.volvo.tisp.moros.database.api.VehicleInformationRepository;
import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;
import com.volvo.tisp.moros.impl.conf.AppConfig;
import com.volvo.tisp.moros.impl.model.OperationalStatus;
import com.volvo.tisp.moros.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.moros.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.moros.integration.tests.util.JmsUtil;
import com.volvo.tisp.moros.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;

class VqvIntegrationTest {
  public static final Path REMOVE_PATH = Path.of("src/test/resources/vqv/vqv-remove.json");
  public static final Path UPDATE_NULL_STATUS_PATH = Path.of("src/test/resources/vqv/vqv-update-null-status.json");
  public static final Path UPDATE_PATH = Path.of("src/test/resources/vqv/vqv-update.json");
  private static final Vpi VPI = Vpi.ofString("1FAC35BF1EDB1DFF91AAC0BEBC56F22B");

  private static void awaitAndVerifyPersistedVehicleInformation(CacheManager cacheManager, VehicleInformationRepository vehicleInformationRepository,
      String vpi, boolean operationalStatus) {
    VehicleInformationEntity vehicleInformationEntity = awaitDataToBePersisted(vehicleInformationRepository, vpi);

    Assertions.assertEquals(IntegrationTestHelper.ASSET_HARDWARE_ID.partNumber(), vehicleInformationEntity.getPartNumber());
    Assertions.assertEquals(IntegrationTestHelper.ASSET_HARDWARE_ID.serialNumber(), vehicleInformationEntity.getSerialNumber());
    Assertions.assertEquals(IntegrationTestHelper.CHASSIS_ID.value(), vehicleInformationEntity.getChassisId());
    Assertions.assertNotNull(vehicleInformationEntity.getCreatedAt());
    Assertions.assertEquals(operationalStatus, vehicleInformationEntity.isOperational());
    Assertions.assertEquals(VPI.toString(), vehicleInformationEntity.getVpi());
    Assertions.assertNotNull(vehicleInformationEntity.getUpdatedAt());

    verifyNoCacheForVpi(cacheManager, vpi);//Verify cache evicted if present
  }

  private static void awaitAndVerifyVehicleInformationIsDeleted(CacheManager cacheManager, VehicleInformationRepository vehicleInformationRepository,
      String vpi) {
    Awaitility.await().atMost(Duration.ofSeconds(3)).until(() -> vehicleInformationRepository.findByVpi(vpi).isEmpty());
    verifyNoCacheForVpi(cacheManager, vpi);//Verify cached evicted if present
  }

  private static VehicleInformationEntity awaitDataToBePersisted(VehicleInformationRepository vehicleInformationRepository, String vpi) {
    Awaitility.await().atMost(Duration.ofSeconds(3)).until(() -> vehicleInformationRepository.findByVpi(vpi).isPresent());
    return vehicleInformationRepository.findByVpi(vpi).get();
  }

  private static Optional<VehicleInformationEntity> getCachedVehicleInformation(CacheManager cacheManager, String vpi) {
    return Optional.ofNullable(cacheManager.getCache(VehicleInformationRepository.OPERATIONAL_VEHICLE_INFORMATION_CACHE))
        .map(cache -> cache.get(vpi, VehicleInformationEntity.class));
  }

  private static void verifyNoCacheForVpi(CacheManager cacheManager, String vpi) {
    Assertions.assertEquals(Optional.empty(), getCachedVehicleInformation(cacheManager, vpi));
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper);
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        CacheManager cacheManager = configurableApplicationContext.getBean(CacheManager.class);
        VehicleInformationRepository vehicleInformationRepository = configurableApplicationContext.getBean(VehicleInformationRepository.class);

        JmsUtil.publishVqvNotification(IntegrationTestHelper.getInputString(UPDATE_PATH)); //evicts Cache if present
        awaitAndVerifyPersistedVehicleInformation(cacheManager, vehicleInformationRepository, VPI.toString(),
            OperationalStatus.OPERATIONAL.getValue());

        JmsUtil.publishVqvNotification(IntegrationTestHelper.getInputString(UPDATE_NULL_STATUS_PATH)); //evicts Cache if present
        awaitAndVerifyPersistedVehicleInformation(cacheManager, vehicleInformationRepository, VPI.toString(),
            OperationalStatus.NON_OPERATIONAL.getValue());

        JmsUtil.publishVqvNotification(IntegrationTestHelper.getInputString(REMOVE_PATH)); //evicts Cache if present
        awaitAndVerifyVehicleInformationIsDeleted(cacheManager, vehicleInformationRepository, VPI.toString());
      }
    }
  }
}
