package com.volvo.tisp.moros.integration.tests;

import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.volvo.tisp.moros.impl.conf.AppConfig;
import com.volvo.tisp.moros.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.moros.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.moros.integration.tests.util.RestUtil;
import com.volvo.tisp.moros.integration.tests.util.SqsUtil;
import com.volvo.tisp.moros.integration.tests.util.WireMockServerWrapper;

class HealthCheckIntegrationTest {
  private static final String QUEUE_NAME = "mo-message-queue";

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        LocalStackContainer localStackContainer = SqsUtil.startLocalStackContainer();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      String queueNameUrl = SqsUtil.createQueue(localStackContainer, QUEUE_NAME);
      String sqsPublishQueueUrl = SqsUtil.createQueue(localStackContainer, IntegrationTestHelper.SQS_OUT_QUEUE_NAME);
      Map<String, String> moMessageQueue = Map.of("moMessageQueue", queueNameUrl, "moMessageOutQueue", sqsPublishQueueUrl);
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, moMessageQueue, localStackContainer);
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        RestUtil.verifyOkHttpResponse("http://localhost:48090/actuator/health");
        RestUtil.verifyOkHttpResponse("http://localhost:48090/actuator/health/ping");
      }
    }
  }
}
