package com.volvo.tisp.moros.integration.tests.util;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import java.io.Closeable;
import org.apache.activemq.artemis.core.server.embedded.EmbeddedActiveMQ;

public final class EmbeddedActiveMqWrapper implements Closeable {
  private final EmbeddedActiveMQ embeddedActiveMQ;

  public EmbeddedActiveMqWrapper(EmbeddedActiveMQ embeddedActiveMQ) {
    Validate.notNull(embeddedActiveMQ, "embeddedActiveMQ");

    this.embeddedActiveMQ = embeddedActiveMQ;
  }

  @Override
  public void close() {
    try {
      embeddedActiveMQ.stop();
    } catch (RuntimeException e) {
      throw e;
    } catch (Exception e) {
      throw new IllegalStateException(e);
    }
  }
}
