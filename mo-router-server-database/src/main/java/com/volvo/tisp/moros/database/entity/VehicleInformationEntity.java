package com.volvo.tisp.moros.database.entity;

import java.io.Serializable;
import java.time.Instant;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.annotation.TypeAlias;
import org.springframework.data.domain.Persistable;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Document(collection = "vehicleInformation")
@TypeAlias("vehicleInformation")
public class VehicleInformationEntity implements Persistable<String>, Serializable {
  @Field("chassisId")
  private String chassisId;
  @Field("createdAt")
  @CreatedDate
  private Instant createdAt;
  @Id
  private String id;
  @Field("operational")
  private boolean operational;

  @Field("partNumber")
  private String partNumber;

  @Field("serialNumber")
  private String serialNumber;

  @Field("updatedAt")
  @LastModifiedDate
  private Instant updatedAt;

  @Field("version")
  private long version;

  @Field("vpi")
  private String vpi;

  public String getChassisId() {
    return chassisId;
  }

  public Instant getCreatedAt() {
    return createdAt;
  }

  @Override
  public String getId() {
    return id;
  }

  public String getPartNumber() {
    return partNumber;
  }

  public String getSerialNumber() {
    return serialNumber;
  }

  public Instant getUpdatedAt() {
    return updatedAt;
  }

  public long getVersion() {
    return version;
  }

  public String getVpi() {
    return vpi;
  }

  @Override
  public boolean isNew() {
    return createdAt == null;
  }

  public boolean isOperational() {
    return operational;
  }

  public void setChassisId(String chassisId) {
    Validate.notNull(chassisId, "chassisId");

    this.chassisId = chassisId;
  }

  public void setCreatedAt(Instant createdAt) {
    this.createdAt = createdAt;
  }

  public void setOperational(boolean operational) {
    this.operational = operational;
  }

  public void setPartNumber(String partNumber) {
    Validate.notNull(partNumber, "partNumber");

    this.partNumber = partNumber;
  }

  public void setSerialNumber(String serialNumber) {
    Validate.notNull(serialNumber, "serialNumber");

    this.serialNumber = serialNumber;
  }

  public void setUpdatedAt(Instant updatedAt) {
    this.updatedAt = updatedAt;
  }

  public void setVersion(long version) {
    Validate.notNegative(version, "version");

    this.version = version;
  }

  public void setVpi(String vpi) {
    Validate.notNull(vpi, "vpi");

    this.vpi = vpi;
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("chassisId={")
        .append(chassisId)
        .append("}, createdAt={")
        .append(createdAt)
        .append("}, operational={")
        .append(operational)
        .append("}, partNumber={")
        .append(partNumber)
        .append("}, serialNumber={")
        .append(serialNumber)
        .append("}, updatedAt={")
        .append(updatedAt)
        .append("}, vpi={")
        .append(vpi)
        .append("}, version={")
        .append(version)
        .append("}")
        .toString();
  }
}
