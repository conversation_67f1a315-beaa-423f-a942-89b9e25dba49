package com.volvo.tisp.moros.database.migration;

import org.springframework.data.mongodb.core.CollectionOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.schema.JsonSchemaProperty;
import org.springframework.data.mongodb.core.schema.MongoJsonSchema;
import org.springframework.data.mongodb.core.validation.Validator;

import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;

@ChangeUnit(order = "001", id = "createVehicleInformationCollection", author = "moros001")
public class InitVehicleInformationCollection {
  private final MongoTemplate mongoTemplate;

  public InitVehicleInformationCollection(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @Execution
  public void execution() {
    mongoTemplate.createCollection(VehicleInformationEntity.class, CollectionOptions.empty()
        .validator(Validator.schema(MongoJsonSchema.builder()
            .required("_id", "createdAt", "operational", "telematicUnitSubType", "telematicUnitType", "updatedAt", "version")
            .properties(
                JsonSchemaProperty.string("_id").maxLength(32),
                JsonSchemaProperty.string("assetId"),
                JsonSchemaProperty.date("createdAt"),
                JsonSchemaProperty.bool("operational"),
                JsonSchemaProperty.string("partNumber"),
                JsonSchemaProperty.string("serialNumber"),
                JsonSchemaProperty.string("telematicUnitSubtype").maxLength(32),
                JsonSchemaProperty.string("telematicUnitType").maxLength(32),
                JsonSchemaProperty.date("updatedAt"),
                JsonSchemaProperty.number("version")
            )
            .build())
        )
    );
  }

  @RollbackExecution
  public void rollbackExecution() {
  }
}
