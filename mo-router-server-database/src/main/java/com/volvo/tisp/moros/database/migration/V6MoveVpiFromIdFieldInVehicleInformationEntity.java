package com.volvo.tisp.moros.database.migration;

import java.util.Arrays;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;

import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;

@ChangeUnit(order = "014", id = "V13MoveVpiFromIdFieldInVehicleInformationEntity")
public class V6MoveVpiFromIdFieldInVehicleInformationEntity {
  private static final String ID = "_id";
  private static final String PART_NUMBER = "partNumber";
  private static final String SERIAL_NUMBER = "serialNumber";
  private static final String VEHICLE_INFORMATION = "vehicleInformation";

  private final MongoTemplate mongoTemplate;

  public V6MoveVpiFromIdFieldInVehicleInformationEntity(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @Execution
  public void execution() {
    validateSchema();
    migrateVpiFromIdField();
  }

  private void migrateVpiFromIdField() {
    for (Document vehicleInformation : mongoTemplate.getCollection("vehicleInformation").find(Filters.not(Filters.exists("vpi")))) {
      mongoTemplate.getCollection("vehicleInformation")
          .updateOne(Filters.eq("_id", vehicleInformation.getString("_id")), Updates.set("vpi", vehicleInformation.getString("_id")));
    }
  }

  private void validateSchema() {
    Document validationSchema = new Document("$jsonSchema", new Document("type", "object")
        .append("required", Arrays.asList(ID, "createdAt", "operational", PART_NUMBER, SERIAL_NUMBER, "updatedAt", "version"))
        .append("properties",
            new Document()
                .append("vpi", new Document("type", "string").append("maxLength", 32))
                .append("chassisId", new Document("type", "string"))
                .append("createdAt", new Document("bsonType", "date"))
                .append("operational", new Document("type", "boolean"))
                .append(PART_NUMBER, new Document("type", "string"))
                .append("processingTags", new Document("type", "array"))
                .append(SERIAL_NUMBER, new Document("type", "string"))
                .append("updatedAt", new Document("bsonType", "date"))
                .append("version", new Document("type", "number"))
        )
    );

    mongoTemplate.getDb().runCommand(new Document("collMod", VEHICLE_INFORMATION).append("validator", validationSchema));
  }

  @RollbackExecution
  public void rollbackExecution() {
  }
}
