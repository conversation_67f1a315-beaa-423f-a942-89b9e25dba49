package com.volvo.tisp.moros.database.migration;

import java.util.Arrays;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;

@ChangeUnit(order = "002", id = "v1VehicleInformationCollectionUpdateFields", author = "moros002")
public class V1VehicleInformationCollectionUpdateFields {
  private final MongoTemplate mongoTemplate;

  public V1VehicleInformationCollectionUpdateFields(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @Execution
  public void execution() {
    Document validationSchema = new Document("$jsonSchema", new Document("type", "object")
        .append("required", Arrays.asList("_id", "createdAt", "operational", "telematicUnitSubType", "telematicUnitType", "updatedAt", "version"))
        .append("properties",
            new Document()
                .append("_id", new Document("type", "string").append("maxLength", 32))
                .append("assetId", new Document("type", "string"))
                .append("createdAt", new Document("bsonType", "date"))
                .append("operational", new Document("type", "boolean"))
                .append("partNumber", new Document("type", "string"))
                .append("processingTags", new Document("type", "array"))
                .append("serialNumber", new Document("type", "string"))
                .append("telematicUnitSubType", new Document("type", "string").append("maxLength", 32))
                .append("telematicUnitType", new Document("type", "string").append("maxLength", 32))
                .append("updatedAt", new Document("bsonType", "date"))
                .append("version", new Document("type", "number"))
        )
    );
    Document command = new Document("collMod", "vehicleInformation")
        .append("validator", validationSchema);
    mongoTemplate.getDb().runCommand(command);
  }

  @RollbackExecution
  public void rollbackExecution() {
    //nothing to rollback
  }
}
