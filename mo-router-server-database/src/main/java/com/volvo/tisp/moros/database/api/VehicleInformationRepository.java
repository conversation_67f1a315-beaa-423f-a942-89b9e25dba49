package com.volvo.tisp.moros.database.api;

import java.util.Collection;
import java.util.Optional;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;

@Repository
public interface VehicleInformationRepository extends CrudRepository<VehicleInformationEntity, String> {
  String OPERATIONAL_VEHICLE_INFORMATION_CACHE = "operationalVehicleInformationCache";

  @Override
  @Caching(evict = {
      @CacheEvict(cacheNames = OPERATIONAL_VEHICLE_INFORMATION_CACHE, key = "#entity.partNumber.concat('-').concat(#entity.serialNumber)")
  })
  void delete(@NonNull @Param("entity") VehicleInformationEntity entity);

  @NonNull
  Optional<VehicleInformationEntity> findByVpi(@NonNull String vpi);

  @Cacheable(cacheNames = OPERATIONAL_VEHICLE_INFORMATION_CACHE, key = "#partNumber.concat('-').concat(#serialNumber)")
  Optional<VehicleInformationEntity> findByPartNumberAndSerialNumber(@Param("partNumber") String partNumber, @Param("serialNumber") String serialNumber);

  Collection<VehicleInformationEntity> findAllByPartNumberAndSerialNumber(@Param("partNumber") String partNumber, @Param("serialNumber") String serialNumber);

  @NonNull
  @Override
  @Caching(evict = {
      @CacheEvict(cacheNames = OPERATIONAL_VEHICLE_INFORMATION_CACHE, key = "#entity.partNumber.concat('-').concat(#entity.serialNumber)")
  })
  <S extends VehicleInformationEntity> S save(@NonNull @Param("entity") S entity);

  @NonNull
  void deleteByVpi(@NonNull String vpi);
}
