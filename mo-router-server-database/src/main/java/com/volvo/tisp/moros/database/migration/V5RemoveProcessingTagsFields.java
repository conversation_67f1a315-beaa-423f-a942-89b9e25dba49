package com.volvo.tisp.moros.database.migration;

import java.util.Arrays;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;

@ChangeUnit(order = "006", id = "v5RemoveProcessingTagsFields", author = "moros005")
public class V5RemoveProcessingTagsFields {
  private final MongoTemplate mongoTemplate;

  private static final String ID = "_id";
  private static final String PART_NUMBER = "partNumber";
  private static final String SERIAL_NUMBER = "serialNumber";
  private static final String STRING = "string";

  public V5RemoveProcessingTagsFields(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @Execution
  public void execution() {
    Document validationSchema = new Document("$jsonSchema", new Document("type", "object")
        .append("required", Arrays.asList(ID, "createdAt", "operational", PART_NUMBER, SERIAL_NUMBER, "updatedAt", "version"))
        .append("properties",
            new Document()
                .append(ID, new Document("type", STRING).append("maxLength", 32))
                .append("chassisId", new Document("type", STRING))
                .append("createdAt", new Document("bsonType", "date"))
                .append("operational", new Document("type", "boolean"))
                .append(PART_NUMBER, new Document("type", STRING))
                .append(SERIAL_NUMBER, new Document("type", STRING))
                .append("updatedAt", new Document("bsonType", "date"))
                .append("version", new Document("type", "number"))
        )
    );

    mongoTemplate.getDb().runCommand(new Document("collMod", "vehicleInformation").append("validator", validationSchema));

    mongoTemplate.updateMulti(new Query(), new Update().unset("processingTags"), "vehicleInformation");
  }

  @RollbackExecution
  public void rollbackExecution() {
    //nothing to rollback
  }
}
