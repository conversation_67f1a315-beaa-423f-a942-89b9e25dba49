package com.volvo.tisp.moros.database.migration;

import java.util.Arrays;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;

@ChangeUnit(order = "005", id = "v4MigrateAssetIdToSeparateFields", author = "a396942")
public class V4MigrateAssetIdToSeparateFields {
  private static final String ID = "_id";
  private static final String PART_NUMBER = "partNumber";
  private static final String SERIAL_NUMBER = "serialNumber";
  private static final String VEHICLE_INFORMATION = "vehicleInformation";

  private final MongoTemplate mongoTemplate;

  public V4MigrateAssetIdToSeparateFields(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @Execution
  public void execute() {
    migrateAssetIdField();
    addCompoundIndex();
    validateSchema();
  }

  @RollbackExecution
  public void rollbackExecution() {
    MongoCollection<Document> collection = mongoTemplate.getCollection(VEHICLE_INFORMATION);
    collection.updateMany(new Document(), new Document("$unset", new Document(PART_NUMBER, "").append(SERIAL_NUMBER, "")));
  }

  private void addCompoundIndex() {
    MongoCollection<Document> collection = mongoTemplate.getCollection(VEHICLE_INFORMATION);
    collection.createIndex(Indexes.ascending("partNumber", "serialNumber"), new IndexOptions().unique(true));
  }

  private void migrateAssetIdField() {
    MongoCollection<Document> collection = mongoTemplate.getCollection(VEHICLE_INFORMATION);

    collection.find().forEach(document -> {
      String assetId = document.getString("assetId");

      if (assetId != null && assetId.contains(".")) {
        String[] parts = assetId.split("\\.", 2);
        String partNumber = parts[0];
        String serialNumber = parts.length > 1 ? parts[1] : "UNKNOWN";

        collection.updateOne(
            new Document(ID, document.get(ID)),
            new Document("$set", new Document(PART_NUMBER, partNumber).append(SERIAL_NUMBER, serialNumber)));
      }
    });
  }

  private void validateSchema() {
    Document validationSchema = new Document("$jsonSchema", new Document("type", "object")
        .append("required", Arrays.asList(ID, "createdAt", "operational", PART_NUMBER, SERIAL_NUMBER, "updatedAt", "version"))
        .append("properties",
            new Document()
                .append(ID, new Document("type", "string").append("maxLength", 32))
                .append("chassisId", new Document("type", "string"))
                .append("createdAt", new Document("bsonType", "date"))
                .append("operational", new Document("type", "boolean"))
                .append(PART_NUMBER, new Document("type", "string"))
                .append("processingTags", new Document("type", "array"))
                .append(SERIAL_NUMBER, new Document("type", "string"))
                .append("updatedAt", new Document("bsonType", "date"))
                .append("version", new Document("type", "number"))
        )
    );

    mongoTemplate.getDb().runCommand(new Document("collMod", "vehicleInformation").append("validator", validationSchema));
  }
}
