package com.volvo.tisp.moros.database.migration;

import java.util.Arrays;

import org.bson.Document;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.mongodb.client.MongoDatabase;

class V1VehicleInformationCollectionUpdateFieldsTest {
  private static void verifyDocument(Document actual) {
    Document validationSchema = new Document("$jsonSchema", new Document("type", "object").append("required",
            Arrays.asList("_id", "createdAt", "operational", "telematicUnitSubType", "telematicUnitType", "updatedAt", "version"))
        .append("properties", new Document().append("_id", new Document("type", "string").append("maxLength", 32))
            .append("assetId", new Document("type", "string"))
            .append("createdAt", new Document("bsonType", "date"))
            .append("operational", new Document("type", "boolean"))
            .append("partNumber", new Document("type", "string"))
            .append("processingTags", new Document("type", "array"))
            .append("serialNumber", new Document("type", "string"))
            .append("telematicUnitSubType", new Document("type", "string").append("maxLength", 32))
            .append("telematicUnitType", new Document("type", "string").append("maxLength", 32))
            .append("updatedAt", new Document("bsonType", "date"))
            .append("version", new Document("type", "number"))));
    Document expected = new Document("collMod", "vehicleInformation").append("validator", validationSchema);

    Assertions.assertEquals(expected, actual);
  }

  @Test
  void testExecution() {
    MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
    MongoDatabase mongoDatabase = Mockito.mock(MongoDatabase.class);

    Mockito.when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
    ArgumentCaptor<Document> documentArgumentCaptor = ArgumentCaptor.forClass(Document.class);

    new V1VehicleInformationCollectionUpdateFields(mongoTemplate).execution();

    Mockito.verify(mongoTemplate, Mockito.times(1)).getDb();
    Mockito.verify(mongoDatabase, Mockito.times(1)).runCommand(documentArgumentCaptor.capture());
    verifyDocument(documentArgumentCaptor.getValue());
    Mockito.verifyNoMoreInteractions(mongoTemplate, mongoDatabase);
  }
}
