package com.volvo.tisp.moros.database.api;

import java.time.Duration;
import java.util.Optional;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.data.mongo.DataMongoTest;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.volvo.tisp.moros.database.entity.VehicleInformationEntity;

@DataMongoTest
@Testcontainers
class VehicleInformationRepositoryTest {
  @Container
  static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:6.0");

  private static final String CHASSIS_ID = "PART-SERIES";
  private static final boolean OPERATIONAL = true;
  private static final String PART_NUMBER = "000001";
  private static final String SERIAL_NUMBER = "000002";
  private static final String PART_SERIAL_CACHE_KEY = PART_NUMBER + "-" + SERIAL_NUMBER;
  private static final String VPI = "1FAC35BF1EDB1DFF91AAC0BEBC56F22B";

  @Autowired
  private CacheManager cacheManager;

  @Autowired
  private VehicleInformationRepository vehicleInformationRepository;

  @DynamicPropertySource
  static void setProperties(DynamicPropertyRegistry registry) {
    registry.add("spring.data.mongodb.uri", mongoDBContainer::getReplicaSetUrl);
    registry.add("mongock.migration-scan-package", () -> "com.volvo.tisp.moros.database.migration");
  }

  private static void verifyVehicleInformation(VehicleInformationEntity vehicleInformationEntity) {
    verifyVehicleInformation(vehicleInformationEntity, OPERATIONAL);
  }

  private static void verifyVehicleInformation(VehicleInformationEntity vehicleInformationEntity, boolean operational) {
    Assertions.assertNotNull(vehicleInformationEntity.getChassisId());
    Assertions.assertNotNull(vehicleInformationEntity.getCreatedAt());
    Assertions.assertEquals(operational, vehicleInformationEntity.isOperational());
    Assertions.assertNotNull(vehicleInformationEntity.getPartNumber());
    Assertions.assertNotNull(vehicleInformationEntity.getSerialNumber());
    Assertions.assertNotNull(vehicleInformationEntity.getUpdatedAt());
    Assertions.assertEquals(VPI, vehicleInformationEntity.getVpi());
  }

  @BeforeEach
  void beforeEachTest() {
    insertVehicleInformation();
  }

  @Test
  void cacheTtlTest() {
    vehicleInformationRepository.findByPartNumberAndSerialNumber(PART_NUMBER, SERIAL_NUMBER);
    getCachedVehicleInformation(PART_SERIAL_CACHE_KEY).orElseThrow();
    Awaitility.await()
        .atMost(Duration.ofSeconds(2))
        .until(() -> getCachedVehicleInformation(PART_SERIAL_CACHE_KEY).isEmpty()); // cache ttl set to 1 sec, verify ttl eviction
  }

  @AfterEach
  void cleanUp() {
    evictCacheByKey(VPI);
    evictCacheByKey(PART_SERIAL_CACHE_KEY);
    evictCacheByKey(CHASSIS_ID);
    this.vehicleInformationRepository.deleteAll();
  }

  @Test
  void deleteTest() {
    Optional<VehicleInformationEntity> vehicleInformationEntity2 = vehicleInformationRepository.findByPartNumberAndSerialNumber(PART_NUMBER, SERIAL_NUMBER);
    verifyNoCacheForKey(VPI);
    verifyNoCacheForKey(CHASSIS_ID);
    verifyVehicleInformation(getCachedVehicleInformation(PART_SERIAL_CACHE_KEY).orElseThrow());
    vehicleInformationRepository.delete(vehicleInformationEntity2.orElseThrow());
    Assertions.assertEquals(Optional.empty(), vehicleInformationRepository.findByPartNumberAndSerialNumber(PART_NUMBER, SERIAL_NUMBER));
    verifyNoCacheForKey(PART_SERIAL_CACHE_KEY);
    verifyNoCacheForKey(VPI);
    verifyNoCacheForKey(CHASSIS_ID);
  }

  @Test
  void findByPartNumberAndSerialNumberTest() {
    verifyNoCacheForKey(PART_SERIAL_CACHE_KEY);
    Optional<VehicleInformationEntity> byAssetHardwareId = vehicleInformationRepository.findByPartNumberAndSerialNumber(PART_NUMBER, SERIAL_NUMBER);
    verifyVehicleInformation(byAssetHardwareId.orElseThrow());
    verifyVehicleInformation(getCachedVehicleInformation(PART_SERIAL_CACHE_KEY).orElseThrow()); // verifies that cache is present

    evictCacheByKey(PART_SERIAL_CACHE_KEY);
    verifyNoCacheForKey(PART_SERIAL_CACHE_KEY);
  }

  @Test
  void findByIdTest() {
    verifyNoCacheForKey(VPI);
    Optional<VehicleInformationEntity> byVpi = vehicleInformationRepository.findByVpi(VPI);
    verifyVehicleInformation(byVpi.orElseThrow());
  }

  @Test
  void updateTest() {
    boolean operationStatus = false;
    VehicleInformationEntity vehicleInformationEntity = vehicleInformationRepository.findByPartNumberAndSerialNumber(PART_NUMBER, SERIAL_NUMBER).orElseThrow(); // result cached

    updateVehicleInformation(operationStatus, vehicleInformationEntity); // updating record should evict cache
    Assertions.assertEquals(Optional.empty(), getCachedVehicleInformation(VPI)); // confirms cache evicted
    verifyVehicleInformation(vehicleInformationRepository.findByVpi(VPI).orElseThrow(), operationStatus);

    updateVehicleInformation(true, vehicleInformationEntity);
    Assertions.assertEquals(Optional.empty(), getCachedVehicleInformation(PART_SERIAL_CACHE_KEY));
    verifyVehicleInformation(vehicleInformationRepository.findByVpi(VPI).orElseThrow(), true);

    updateVehicleInformation(operationStatus, vehicleInformationEntity);
    Assertions.assertEquals(Optional.empty(), getCachedVehicleInformation(CHASSIS_ID));
    verifyVehicleInformation(vehicleInformationRepository.findByVpi(VPI).orElseThrow(), operationStatus);
  }

  private void evictCacheByKey(String key) {
    cacheManager.getCache(VehicleInformationRepository.OPERATIONAL_VEHICLE_INFORMATION_CACHE).evictIfPresent(key);
  }

  private Optional<VehicleInformationEntity> getCachedVehicleInformation(String key) {
    return Optional.ofNullable(cacheManager.getCache(VehicleInformationRepository.OPERATIONAL_VEHICLE_INFORMATION_CACHE))
        .map(cache -> cache.get(key, VehicleInformationEntity.class));
  }

  private VehicleInformationEntity insertVehicleInformation() {
    VehicleInformationEntity vehicleInformationEntity = new VehicleInformationEntity();

    vehicleInformationEntity.setOperational(OPERATIONAL);
    vehicleInformationEntity.setPartNumber(PART_NUMBER);
    vehicleInformationEntity.setSerialNumber(SERIAL_NUMBER);
    vehicleInformationEntity.setVpi(VPI);
    vehicleInformationEntity.setChassisId(CHASSIS_ID);

    return vehicleInformationRepository.save(vehicleInformationEntity);
  }

  private void updateVehicleInformation(boolean operationStatus, VehicleInformationEntity vehicleInformationEntity) {
    vehicleInformationEntity.setOperational(operationStatus);

    vehicleInformationRepository.save(vehicleInformationEntity);
  }

  private void verifyNoCacheForKey(String key) {
    Assertions.assertEquals(Optional.empty(), getCachedVehicleInformation(key));
  }
}
