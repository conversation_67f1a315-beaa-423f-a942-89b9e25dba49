package com.volvo.tisp.moros.database.migration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.data.mongo.DataMongoTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

@DataMongoTest
@Testcontainers
@ContextConfiguration(classes = {InitVehicleInformationCollectionTest.class})
@EnableMongoRepositories
class InitVehicleInformationCollectionTest {
  @Container
  private static final MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:6.0");
  @Autowired
  private MongoTemplate mongoTemplate;

  @DynamicPropertySource
  static void setProperties(DynamicPropertyRegistry registry) {
    registry.add("spring.data.mongodb.uri", mongoDBContainer::getReplicaSetUrl);
  }

  @Test
  void executionTest() {
    InitVehicleInformationCollection initVehicleInformationCollection = new InitVehicleInformationCollection(mongoTemplate);
    initVehicleInformationCollection.execution();
    Assertions.assertNotNull(mongoTemplate.getCollection("vehicleInformation"));
  }
}
