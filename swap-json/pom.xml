<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.volvo.tisp.vehiclepingservice</groupId>
    <artifactId>vehicle-ping-service-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>
  <artifactId>vehicle-ping-service-server-swap-json</artifactId>
  <name>Vehicle Ping Service :: Server :: SWAP-JSON</name>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.networknt</groupId>
      <artifactId>json-schema-validator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.spotbugs</groupId>
      <artifactId>spotbugs-annotations</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jsonschema2pojo</groupId>
        <artifactId>jsonschema2pojo-maven-plugin</artifactId>
        <version>1.2.2</version>
        <configuration>
          <sourceDirectory>${basedir}/src/main/resources/schema</sourceDirectory>
          <targetPackage>com.volvo.tisp.vps.api</targetPackage>
          <useJodaDates>false</useJodaDates>
          <useJodaLocalTimes>false</useJodaLocalTimes>
          <useJodaLocalDates>false</useJodaLocalDates>
          <includeJsr303Annotations>false</includeJsr303Annotations>
          <includeAdditionalProperties>false</includeAdditionalProperties>
          <dateTimeType>java.time.ZonedDateTime</dateTimeType>
          <dateType>java.time.LocalDate</dateType>
          <useTitleAsClassname>true</useTitleAsClassname>
          <generateBuilders>true</generateBuilders>
          <formatDateTimes>true</formatDateTimes>
          <customDateTimePattern>yyyy-MM-dd HH:mm:ss</customDateTimePattern>
        </configuration>
        <executions>
          <execution>
            <id>jsonschema</id>
            <goals>
              <goal>generate</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
