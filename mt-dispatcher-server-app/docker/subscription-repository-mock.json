{"request": {"urlPattern": "/api/(?:publishers|subscribers)/.*", "method": "ANY", "headers": {"Accept": {"equalTo": "application/x-protobuf"}}}, "response": {"status": 200, "headers": {"Content-Type": "{{{request.headers.Accept}}}", "Content-Length": "{{{request.headers.Content-Length}}}", "X-Protobuf-Message": "{{{request.headers.X-Protobuf-Message}}}", "X-Protobuf-Schema": "{{{request.headers.X-Protobuf-Schema}}}", "VGT-TrackingId": "{{{request.headers.VGT-TrackingId}}}", "VGT-ClientId": "{{{request.headers.VGT-ClientId}}}"}, "body": "{{{request.body}}}"}}