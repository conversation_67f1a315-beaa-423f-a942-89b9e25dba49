version: '3.8'
services:
  influxdb:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/influxdb:2.7.12@sha256:b357bde62be5ae1ce1a0dea4f04e7194a62ae82fb4ccc2f4993df3f5209a40d0
    ports:
      - 8086:8086
  mongodb_container:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/mongo:8.0@sha256:a6bda40d00e56682aeaa1bfc88e024b7dd755782c575c02760104fe02010f94f
    environment:
      MONGO_INITDB_DATABASE: testmongo
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: rootpassword
    ports:
      - 27017:27017
  sqs:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/localstack/localstack:4.7.0@sha256:12253acd9676770e9bd31cbfcf17c5ca6fd7fb5c0c62f3c46dd701f20304260c
    environment:
      - SERVICES=sqs
    volumes:
      - ./localstack_bootstrap/sqs_bootstrap.sh:/etc/localstack/init/ready.d/init-aws.sh
    ports:
      - 4566:4566
  wiremock:
    image: "ecr.prod.de.eu-west-1.de.aws.vgthosting.net/delivery-engine-docker-wiremock2@sha256:03900565fa8a630c1bfd42b8a2d99aeb9e61f20176553c4d1e933f2522b27f6b"
    command: --global-response-templating
    ports:
      - 8080:8080/tcp
    volumes:
      - type: bind
        source: ./subscription-repository-mock.json
        target: /home/<USER>/mappings/subscription-repository-mock.json
  amq:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/vromero/activemq-artemis:latest@sha256:408d6a46b153ca20bd263d94a2503fab74c178f5a956ab7814747c7dbb8fc578
    environment:
      - ARTEMIS_USERNAME=admin
      - ARTEMIS_PASSWORD=admin
    ports:
      - 8161:8161
      - 61616:61616
  zoo:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/zookeeper@sha256:4613d276467d79e938d0f849b0040f8a9ea089dfbc0a528dda6dfbb0aed50b59
    ports:
      - 2181:2181/tcp
