<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information -->
  <!-- +=============================================== -->
  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tisp-parent</artifactId>
    <version>159</version>
    <relativePath/>
  </parent>

  <groupId>com.volvo.tisp.vehiclepingservice</groupId>
  <artifactId>vehicle-ping-service-server</artifactId>
  <version>0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <name>Vehicle Ping Service :: Server</name>
  <description>Description of component</description>
  <url/>

  <properties>
    <common-dto-lib.version>182</common-dto-lib.version>
    <tisp-dependencies.version>159</tisp-dependencies.version>
    <vehicle-ping-service-client.version>78</vehicle-ping-service-client.version>
    <subscriptionrepository-client.version>674</subscriptionrepository-client.version>
    <mt.dispatcher.api.version>36</mt.dispatcher.api.version>
    <mo.router.version>19</mo.router.version>
    <zstd.version>1.5.7-4</zstd.version>
    <jwt.version>0.12.6</jwt.version>
    <nimbus.version>10.4</nimbus.version>
    <telematicunitservice.version>147.0.15</telematicunitservice.version>
    <tce.opus.version>181</tce.opus.version>
    <idpm2m.jwks.client.version>17</idpm2m.jwks.client.version>
    <state.version>368</state.version>

    <json-schema-validator.version>1.5.8</json-schema-validator.version>
    <jackson.nullable.version>0.2.6</jackson.nullable.version>
    <swagger.parser.version>2.1.31</swagger.parser.version>
    <openapi-generator-maven-plugin.version>7.6.0</openapi-generator-maven-plugin.version>
    <support-tool-server-iwc.version>8</support-tool-server-iwc.version>

    <!-- for GUI -->
    <test-utils-lib.version>72</test-utils-lib.version>
    <lombok.version>1.18.38</lombok.version>
    <wiremock-testcontainers-module.version>1.0-alpha-15</wiremock-testcontainers-module.version>

    <component.long-name>vehicle-ping-service-server</component.long-name>
    <!--  CDK  -->
    <aws-cdk-lib.version>2.208.0</aws-cdk-lib.version>
    <constructs.version>10.4.2</constructs.version>
    <tisp-cdk-constructs.version>2.0.17</tisp-cdk-constructs.version>
    <autovalue.version>1.11.0</autovalue.version>
    <commons-configuration2.version>2.12.0</commons-configuration2.version>
    <pipelines-constructs-base.version>4.2.2</pipelines-constructs-base.version>
    <de-pipelines-constructs.version>3.1.69</de-pipelines-constructs.version>

    <component.long-name>vehicle-ping-service</component.long-name>
    <component.short-name>vps</component.short-name>
    <component.debug.port>8787</component.debug.port>
    <component.debug.suspend>n</component.debug.suspend>
    <skipFindbugs>false</skipFindbugs>

    <maven.compiler.release>21</maven.compiler.release>
  </properties>

  <!-- +=============================================== -->
  <!-- | Section 3: Dependency (Management) -->
  <!-- +=============================================== -->
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>common-dto-lib</artifactId>
        <version>${common-dto-lib.version}</version>
      </dependency>
      <!-- Our API -->
      <dependency>
        <groupId>com.volvo.tisp.vehiclepingservice</groupId>
        <artifactId>vehicle-ping-service-client-api</artifactId>
        <version>${vehicle-ping-service-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tisp-dependencies</artifactId>
        <version>${tisp-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.support-tool</groupId>
        <artifactId>support-tool-server-iwc</artifactId>
        <version>${support-tool-server-iwc.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-api</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-impl</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>

      <dependency>
        <groupId>com.wirelesscar.telematicunitservice</groupId>
        <artifactId>telematicunitservice-client-impl-rest</artifactId>
        <version>${telematicunitservice.version}</version>
      </dependency>

      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-opus-client-api</artifactId>
        <version>${tce.opus.version}</version>
      </dependency>

      <!-- state repo -->
      <dependency>
        <groupId>com.wirelesscar.staterepository-client</groupId>
        <artifactId>staterepository-client-impl-rest</artifactId>
        <version>${state.version}</version>
      </dependency>

      <!-- MT DISPATCHER API -->
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>mt-message-client-json</artifactId>
        <version>${mt.dispatcher.api.version}</version>
      </dependency>

      <!-- MO ROUTER API -->
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>mo-router-client-json</artifactId>
        <version>${mo.router.version}</version>
      </dependency>

      <!-- ZSTD Compression -->
      <dependency>
        <groupId>com.github.luben</groupId>
        <artifactId>zstd-jni</artifactId>
        <version>${zstd.version}</version>
      </dependency>

      <!-- IDPM2M CLIENT -->
      <dependency>
        <groupId>com.volvo.tisp.idpm2m</groupId>
        <artifactId>identityprovider-m2m-client-impl-http</artifactId>
        <version>${idpm2m.jwks.client.version}</version>
      </dependency>

      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>test-utils-lib</artifactId>
        <version>${test-utils-lib.version}</version>
        <scope>test</scope>
      </dependency>

      <!-- JWT -->
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>${jwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>${jwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <version>${jwt.version}</version>
      </dependency>

      <!-- nimbus -->
      <dependency>
        <groupId>com.nimbusds</groupId>
        <artifactId>nimbus-jose-jwt</artifactId>
        <version>${nimbus.version}</version>
      </dependency>

      <!-- OpenAPI -->
      <dependency>
        <groupId>io.swagger.parser.v3</groupId>
        <artifactId>swagger-parser-v3</artifactId>
        <version>${swagger.parser.version}</version>
      </dependency>
      <dependency>
        <groupId>org.openapitools</groupId>
        <artifactId>jackson-databind-nullable</artifactId>
        <version>${jackson.nullable.version}</version>
      </dependency>

      <dependency>
        <groupId>com.networknt</groupId>
        <artifactId>json-schema-validator</artifactId>
        <version>${json-schema-validator.version}</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
        <scope>provided</scope>
      </dependency>

      <!-- Test -->
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-test-util</artifactId>
        <version>${subscriptionrepository-client.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>com.wirelesscar.staterepository-client</groupId>
        <artifactId>staterepository-client-mock</artifactId>
        <version>${state.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>com.volvo.tisp.idpm2m</groupId>
        <artifactId>identityprovider-m2m-client-mock</artifactId>
        <version>${idpm2m.jwks.client.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.wiremock.integrations.testcontainers</groupId>
        <artifactId>wiremock-testcontainers-module</artifactId>
        <version>${wiremock-testcontainers-module.version}</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <annotationProcessorPaths>
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
              </path>
            </annotationProcessorPaths>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <!-- +=============================================== -->
  <!-- | Section 2: Module definitions -->
  <!-- +=============================================== -->

  <profiles>
    <profile>
      <id>vps-default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>swap-asn1</module>
        <module>swap-json</module>
        <module>api-openapi-impl</module>
        <module>app</module>
        <module>gui</module>
        <module>impl</module>
        <module>database</module>
        <module>integration-tests</module>
      </modules>
    </profile>
    <profile>
      <id>cdk</id>
      <modules>
        <module>cdk</module>
      </modules>
    </profile>
    <profile>
      <id>component-tests</id>
    </profile>
  </profiles>

</project>
