<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tisp-parent</artifactId>
    <version>159</version>
  </parent>

  <artifactId>mt-dispatcher-server</artifactId>
  <version>0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <properties>
    <component.long-name>mt-dispatcher-server</component.long-name>
    <component.short-name>mtdisps</component.short-name>
    <component.debug.port>8787</component.debug.port>
    <component.debug.suspend>n</component.debug.suspend>

    <asset-mt-scheduler-client.version>0.0.8</asset-mt-scheduler-client.version>
    <aws-cdk-lib.version>2.208.0</aws-cdk-lib.version>
    <bouncycastle.version>1.70.0</bouncycastle.version>
    <common-dto-lib.version>182</common-dto-lib.version>
    <commons-configuration2.version>2.12.0</commons-configuration2.version>
    <commons-csv.version>1.14.1</commons-csv.version>
    <constructs.version>10.4.2</constructs.version>
    <AmazonCorrettoCryptoProvider.version>2.5.0</AmazonCorrettoCryptoProvider.version>
    <external-mt-message-client.version>23</external-mt-message-client.version>
    <integration-log-event-repository-client.version>21</integration-log-event-repository-client.version>
    <mockito-core.version>5.18.0</mockito-core.version>
    <mongock-bom.version>5.5.1</mongock-bom.version>
    <mt-message-client.version>36</mt-message-client.version>
    <nimbus-jose-jwt.version>10.4</nimbus-jose-jwt.version>
    <spring-cloud-aws-dependencies.version>3.4.0</spring-cloud-aws-dependencies.version>
    <subscriptionrepository-client.version>676</subscriptionrepository-client.version>
    <tce-opus-client.version>181</tce-opus-client.version>
    <test-utils-lib.version>72</test-utils-lib.version>
    <testcontainers.version>1.21.3</testcontainers.version>
    <tisp-cdk-constructs.version>2.0.17</tisp-cdk-constructs.version>
    <tisp-dependencies.version>159</tisp-dependencies.version>
    <vehicleactivation-client.version>199.0.13</vehicleactivation-client.version>
    <vc-uncaught-exception-handler-lib.version>34</vc-uncaught-exception-handler-lib.version>
    <vehiclequeryview-client.version>406</vehiclequeryview-client.version>
    <java.version>21</java.version>
    <maven.compiler.release>21</maven.compiler.release>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tisp-dependencies</artifactId>
        <version>${tisp-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-uncaught-exception-handler-lib</artifactId>
        <version>${vc-uncaught-exception-handler-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.vqv</groupId>
        <artifactId>vehiclequeryview-client-api</artifactId>
        <version>${vehiclequeryview-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.vqv</groupId>
        <artifactId>vehiclequeryview-client-impl-http</artifactId>
        <version>${vehiclequeryview-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>common-dto-lib</artifactId>
        <version>${common-dto-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>mt-message-client-json</artifactId>
        <version>${mt-message-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-opus-client-api</artifactId>
        <version>${tce-opus-client.version}</version>
      </dependency>
      <dependency>
        <groupId>io.awspring.cloud</groupId>
        <artifactId>spring-cloud-aws-dependencies</artifactId>
        <version>${spring-cloud-aws-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.mongock</groupId>
        <artifactId>mongock-bom</artifactId>
        <version>${mongock-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.nimbusds</groupId>
        <artifactId>nimbus-jose-jwt</artifactId>
        <version>${nimbus-jose-jwt.version}</version>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15on</artifactId>
        <version>${bouncycastle.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>integration-log-event-repository-client-json</artifactId>
        <version>${integration-log-event-repository-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>external-mt-message-client-json</artifactId>
        <version>${external-mt-message-client.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-csv</artifactId>
        <version>${commons-csv.version}</version>
      </dependency>
      <dependency>
        <groupId>software.amazon.cryptools</groupId>
        <artifactId>AmazonCorrettoCryptoProvider</artifactId>
        <version>${AmazonCorrettoCryptoProvider.version}</version>
        <classifier>linux-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-impl</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.va</groupId>
        <artifactId>vehicleactivation-client-api</artifactId>
        <version>${vehicleactivation-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>asset-mt-scheduler-client-protobuf</artifactId>
        <version>${asset-mt-scheduler-client.version}</version>
      </dependency>

      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>test-utils-lib</artifactId>
        <version>${test-utils-lib.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>${testcontainers.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers-bom</artifactId>
        <version>${testcontainers.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-test-util</artifactId>
        <version>${subscriptionrepository-client.version}</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <skip>true</skip>
          <skipDeploy>true</skipDeploy>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>mtdisps-default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>mt-dispatcher-server-app</module>
        <module>mt-dispatcher-server-impl</module>
        <module>mt-dispatcher-server-database</module>
        <module>mt-dispatcher-server-integration-tests</module>
      </modules>
    </profile>
    <profile>
      <id>component-tests</id>
    </profile>
    <profile>
      <id>cdk</id>
      <modules>
        <module>cdk</module>
      </modules>
    </profile>
  </profiles>
</project>
