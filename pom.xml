<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tisp-parent</artifactId>
    <version>159</version>
  </parent>

  <artifactId>mo-router-server</artifactId>
  <version>0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <properties>
    <component.long-name>mo-router-server</component.long-name>
    <component.short-name>moros</component.short-name>
    <component.debug.port>8787</component.debug.port>
    <component.debug.suspend>n</component.debug.suspend>

    <asset-messaging-gateway-client.version>12</asset-messaging-gateway-client.version>
    <aws-cdk-lib.version>2.210.0</aws-cdk-lib.version>
    <common-dto-lib.version>182</common-dto-lib.version>
    <commons-configuration2.version>2.12.0</commons-configuration2.version>
    <constructs.version>10.4.2</constructs.version>
    <external-mo-router-client.version>12</external-mo-router-client.version>
    <integration-log-event-repository-client.version>21</integration-log-event-repository-client.version>
    <main-utils-lib.version>116</main-utils-lib.version>
    <mo-router-client.version>19</mo-router-client.version>
    <mockito-core.version>5.18.0</mockito-core.version>
    <mongock-bom.version>5.5.1</mongock-bom.version>
    <processing-tags-client.version>132</processing-tags-client.version>
    <spring-cloud-aws.version>3.4.0</spring-cloud-aws.version>
    <subscriptionrepository-client.version>676</subscriptionrepository-client.version>
    <test-utils-lib.version>72</test-utils-lib.version>
    <testcontainers.version>1.21.3</testcontainers.version>
    <junit-jupiter.version>5.13.4</junit-jupiter.version>
    <tisp-cdk-constructs.version>2.0.17</tisp-cdk-constructs.version>
    <tisp-dependencies.version>159</tisp-dependencies.version>
    <vehiclequeryview-client.version>412</vehiclequeryview-client.version>
    <vc-uncaught-exception-handler-lib.version>34</vc-uncaught-exception-handler-lib.version>
    <java.version>21</java.version>
    <maven.compiler.release>21</maven.compiler.release>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tisp-dependencies</artifactId>
        <version>${tisp-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.vqv</groupId>
        <artifactId>vehiclequeryview-client-api</artifactId>
        <version>${vehiclequeryview-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.vqv</groupId>
        <artifactId>vehiclequeryview-client-impl-http</artifactId>
        <version>${vehiclequeryview-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>mo-router-client-json</artifactId>
        <version>${mo-router-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-impl</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.connectivity</groupId>
        <artifactId>asset-messaging-gateway-client-json</artifactId>
        <version>${asset-messaging-gateway-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>common-dto-lib</artifactId>
        <version>${common-dto-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>main-utils-lib</artifactId>
        <version>${main-utils-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>io.awspring.cloud</groupId>
        <artifactId>spring-cloud-aws-dependencies</artifactId>
        <version>${spring-cloud-aws.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.mongock</groupId>
        <artifactId>mongock-bom</artifactId>
        <version>${mongock-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-uncaught-exception-handler-lib</artifactId>
        <version>${vc-uncaught-exception-handler-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>integration-log-event-repository-client-json</artifactId>
        <version>${integration-log-event-repository-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>external-mo-router-client-json</artifactId>
        <version>${external-mo-router-client.version}</version>
      </dependency>

      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>test-utils-lib</artifactId>
        <version>${test-utils-lib.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>${testcontainers.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers-bom</artifactId>
        <version>${testcontainers.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-test-util</artifactId>
        <version>${subscriptionrepository-client.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>${junit-jupiter.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito-core.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>${mockito-core.version}</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <skip>true</skip>
          <skipDeploy>true</skipDeploy>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>moros-default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>mo-router-server-app</module>
        <module>mo-router-server-impl</module>
        <module>mo-router-server-database</module>
        <module>mo-router-server-integration-tests</module>
      </modules>
    </profile>
    <profile>
      <id>component-tests</id>
    </profile>
    <profile>
      <id>cdk</id>
      <modules>
        <module>cdk</module>
      </modules>
    </profile>
  </profiles>
</project>
