package com.volvo.tisp.vehiclepingservice.integration.tests.conf;

import org.springframework.boot.testcontainers.service.connection.ServiceConnection;
import org.testcontainers.activemq.ArtemisContainer;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.utility.DockerImageName;
import org.wiremock.integrations.testcontainers.WireMockContainer;

import software.amazon.awssdk.regions.Region;

public final class Containers {
  private static final String SHARED_SERVICES_DOCKER_PUBLIC = "artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/";
  @Container
  public static final LocalStackContainer localStackContainer =
      new LocalStackContainer(
          getDockerImageName("localstack/localstack").withTag("latest"))
          .withEnv("DEFAULT_REGION", Region.EU_WEST_1.id())
          .withServices(LocalStackContainer.Service.S3)
          .withReuse(true);
  @ServiceConnection
  public static final MongoDBContainer mongoContainer =
      new MongoDBContainer(getDockerImageName("mongo:6.0"))
          .withReuse(true);
  public static final WireMockContainer wireMockContainer =
      new WireMockContainer(
          getDockerImageName("wiremock/wiremock").withTag("latest-alpine"))
          .withReuse(true);
  @ServiceConnection
  public static final ArtemisContainer artemisContainer =
      new ArtemisContainer(getDockerImageName("apache/activemq-artemis").withTag("latest")).withEnv("ANONYMOUS_LOGIN", "true").withReuse(true);

  private Containers() {
    throw new IllegalArgumentException();
  }

  private static DockerImageName getDockerImageName(String imageName) {
    return DockerImageName.parse(SHARED_SERVICES_DOCKER_PUBLIC + imageName).asCompatibleSubstituteFor(imageName);
  }
}