package com.volvo.tisp.vehiclepingservice.integration.tests.mock;

import static com.nimbusds.jose.JWSAlgorithm.ES384;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.ECDSASigner;
import com.nimbusds.jose.jwk.Curve;
import com.nimbusds.jose.jwk.ECKey;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.KeyUse;
import com.nimbusds.jose.jwk.gen.ECKeyGenerator;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.PlainJWT;
import com.nimbusds.jwt.SignedJWT;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import software.amazon.awssdk.auth.credentials.AnonymousCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.BucketAlreadyExistsException;
import software.amazon.awssdk.services.s3.model.BucketAlreadyOwnedByYouException;
import software.amazon.awssdk.services.s3.model.CreateBucketRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

// a copy of JWKSClientTestUtils from idpm2mclient with a change of VEHICLE_JWKS_JSON
public class JWKSClientTestMock {
  private static final String BUCKET_NAME = "t3-test-bucket";
  private static final String JWKS_JSON = "jwks.json";
  private static final String VEHICLE_JWKS_JSON = "vehicles/%s/jwks.json";
  private static final Logger logger = LoggerFactory.getLogger(JWKSClientTestMock.class);
  private final List<String> createdObjects = new ArrayList<>();
  private final S3Client s3Client;

  @SuppressFBWarnings({"CT_CONSTRUCTOR_THROW"})
  public JWKSClientTestMock(Environment environment) {
    this.s3Client = createS3Client(environment);

    try {
      CreateBucketRequest createBucketRequest = CreateBucketRequest.builder().bucket(BUCKET_NAME).build();
      this.s3Client.createBucket(createBucketRequest);
    } catch (BucketAlreadyOwnedByYouException | BucketAlreadyExistsException var3) {
      //
    }

    String jwksFileContent = this.readFileContent();
    this.putObject(JWKS_JSON, jwksFileContent);
    logger.info("Created jwks.json in S3 with content {}", jwksFileContent);
  }

  public static String getPlainJwtToken(ECKey key, String scope, Instant instant) {
    JWTClaimsSet jwtClaimsSet = getJwtClaimsSet(key, scope, instant);
    PlainJWT plainJWT = new PlainJWT(jwtClaimsSet);
    return plainJWT.serialize();
  }

  public static String getSignedJwtToken(ECKey key, String scope, Instant instant) throws JOSEException {
    JWTClaimsSet jwtClaimsSet = getJwtClaimsSet(key, scope, instant);
    SignedJWT signedJWT = new SignedJWT(new JWSHeader.Builder(ES384).keyID(key.getKeyID()).build(), jwtClaimsSet);
    signedJWT.sign(new ECDSASigner(key));
    return signedJWT.serialize();
  }

  public static String getSignedJwtToken(ECKey key) throws JOSEException {
    return getSignedJwtToken(key, "mh.w svc.256", Instant.now().plus(10, ChronoUnit.MINUTES));
  }

  private static S3Client createS3Client(Environment environment) {
    return S3Client.builder()
        .region(Region.EU_WEST_1)
        .credentialsProvider(AnonymousCredentialsProvider.create())
        .endpointOverride(URI.create(environment.getProperty("s3Values.mockUrl")))
        .forcePathStyle(Boolean.TRUE)
        .build();
  }

  private static JWTClaimsSet getJwtClaimsSet(ECKey key, String scope, Instant instant) {
    return new JWTClaimsSet.Builder()
        .claim("scope", scope)
        .expirationTime(Date.from(instant))
        .issuer("http://idm2mp/tenantUUID/" + key.getKeyID() + "/jwks.json")
        .build();
  }

  public ECKey createECKey() throws JOSEException {
    return new ECKeyGenerator(Curve.P_384)
        .algorithm(ES384)
        .keyUse(KeyUse.SIGNATURE) // indicate the intended use of the key (optional)
        .keyID(UUID.randomUUID().toString()) // give the key a unique ID (optional)
        .generate();
  }

  public void emptyObjects() {
    createdObjects.forEach(this::deleteObject);
    createdObjects.clear();
  }

  public String setUpJwtToken() throws JOSEException {
    ECKey ecKey = createECKey();
    setupVehicleKey(ecKey);
    IdpMock.mockKeyQuery(ecKey);
    return getSignedJwtToken(ecKey);
  }

  public void setupVehicleKey(JWK vehicleJWk) {
    JWKSet jwks = new JWKSet(vehicleJWk);
    String keyId = vehicleJWk.getKeyID();
    String objectKey = String.format(Locale.ENGLISH, VEHICLE_JWKS_JSON, keyId);
    putObject(objectKey, jwks.toString());
  }

  private void deleteObject(String objectKey) {
    DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder().bucket(BUCKET_NAME).key(objectKey).build();
    this.s3Client.deleteObject(deleteObjectRequest);
  }

  private void putObject(String objectKey, String jwks) {
    PutObjectRequest putObjectRequest = PutObjectRequest.builder().bucket(BUCKET_NAME).key(objectKey).build();
    this.s3Client.putObject(putObjectRequest, RequestBody.fromString(jwks));
    createdObjects.add(objectKey);
  }

  private String readFileContent() {
    try {
      var resource = JWKSClientTestMock.class.getClassLoader().getResource(JWKSClientTestMock.JWKS_JSON);
      if (resource == null) {
        throw new IllegalArgumentException("File not found: " + JWKSClientTestMock.JWKS_JSON);
      }
      return Files.readString(Paths.get(resource.toURI()), StandardCharsets.UTF_8);
    } catch (IOException | URISyntaxException ex) {
      throw new RuntimeException("Failed to read file: " + JWKSClientTestMock.JWKS_JSON, ex);
    }
  }
}
