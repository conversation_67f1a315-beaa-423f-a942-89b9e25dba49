package com.volvo.tisp.vehiclepingservice.integration.tests.mock;

import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.nimbusds.jose.jwk.ECKey;
import com.nimbusds.jose.jwk.JWKSet;

public class IdpMock {

  public static void mockKeyQuery(ECKey key) {
    mockIdpm2mKey(key);
  }

  public static void verifyIdpWiremockTokenInteractions(int times) {
    WireMock.verify(times, postRequestedFor(urlEqualTo("/oauth2/token")));
  }

  private static void mockIdpm2mKey(ECKey key) {
    WireMock.stubFor(
        WireMock.get(urlEqualTo("/tenantUUID/" + key.getKeyID() + "/jwks.json"))
            .willReturn(WireMock.aResponse().withBody(new JWKSet(key).toString()).withStatus(200)));
  }
}
