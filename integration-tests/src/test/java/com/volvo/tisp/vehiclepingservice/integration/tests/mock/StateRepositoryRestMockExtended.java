package com.volvo.tisp.vehiclepingservice.integration.tests.mock;

import java.time.Instant;

import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.framework.jms.DestinationNamingConvention;
import com.volvo.tisp.protobuf.state.StateInfo;
import com.volvo.tisp.staterepository.StateRepositoryRestMock;

public class StateRepositoryRestMockExtended extends StateRepositoryRestMock {

  private final String timeoutQueue;

  public StateRepositoryRestMockExtended(DestinationNamingConvention convention) {
    super(convention);
    this.timeoutQueue = convention.addQueuePrefix("STATE_TIMEOUT");
  }

  private static ResponseDefinitionBuilder stubPushResponse(int statusCode, StateInfo stateInfo) {
    return WireMock.aResponse()
        .withStatus(statusCode)
        .withHeader("Content-Type", new String[] {"application/x-protobuf"})
        .withBody(stateInfo.toByteArray());
  }

  public StubVerifier stubPush(final Instant timeout, int statusCode) {
    StateInfo stateInfo = createStateInfo(timeout);

    WireMock.stubFor(
        WireMock.put(WireMock.urlPathMatching("/api/states/.*"))
            .withHeader("Content-Type", WireMock.containing("application/x-protobuf"))
            .withHeader("Accept", WireMock.containing("application/x-protobuf"))
            .willReturn(
                stubPushResponse(statusCode, stateInfo)));
    return () -> {
      WireMock.verify(
          WireMock.putRequestedFor(WireMock.urlPathMatching("/api/states/.*"))
              .withHeader("Accept", WireMock.containing("application/x-protobuf"))
              .withHeader("Content-Type", WireMock.containing("application/x-protobuf")));
    };
  }

  private StateInfo createStateInfo(Instant timeout) {
    return StateInfo.newBuilder()
        .setCallback(this.timeoutQueue)
        .setRefId("temp")
        .addState("")
        .setTimeout(timeout.toEpochMilli())
        .build();
  }
}
