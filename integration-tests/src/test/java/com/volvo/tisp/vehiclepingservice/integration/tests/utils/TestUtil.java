package com.volvo.tisp.vehiclepingservice.integration.tests.utils;

import java.io.StringWriter;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;

import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.volvo.tisp.identifier.VehicleIdentifier;
import com.volvo.tisp.subscriptionrepository.client.Destination;
import com.volvo.tisp.subscriptionrepository.client.SubscriptionStubber;
import com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.integration.tests.conf.Containers;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.model.PingToVehicleRequest;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.api.v2.SrpOption;
import com.wirelesscar.tce.client.opus.MessageTypesJms;
import com.wirelesscar.telematicunitservice.telematicunit._1_0.TelematicUnit;

import tisp.framework.internal.web.auth.VerifyTokenResponse;

public class TestUtil {
  public static final String EMEA = "EMEA";
  public static final MockConfiguration MOCK_CONFIGURATION = MockConfiguration.getConfig();
  public static final String PAYLOAD = "payload";
  public static final String QUEUE_PREFIX = "LOCAL.LOCAL.LOCAL.VPS.";
  public static final int SERVICE_VERSION = 1;
  public static final long START_TIME = Instant.now().toEpochMilli();
  private static final String MT_DISPATCHER_DESTINATION = "activemq:queue:LOCAL.LOCAL.LOCAL.VPS.MT.NEW.IN";
  private static final String TCE_DESTINATION = "activemq:queue:LOCAL.LOCAL.LOCAL.VPS.MT.IN";
  private static final Logger logger = LoggerFactory.getLogger(TestUtil.class);

  private TestUtil() {
    throw new IllegalArgumentException();
  }

  public static MoMessage createMoMessage(byte[] payload, int serviceVersion, String vpi) {
    MoMessage moMessage = new MoMessage();
    moMessage.setSrpOption(new SrpOption());
    moMessage.getSrpOption().setDstService(256);
    moMessage.getSrpOption().setDstVersion(serviceVersion);
    moMessage.setVehiclePlatformId(vpi);
    moMessage.setPayload(payload);
    return moMessage;
  }

  public static MtStatusMessage createMtStatusMessage(String correlationId, String vpi, String status) {
    MtStatusMessage mtStatusMessage = new MtStatusMessage();
    mtStatusMessage.setCorrelationId(correlationId);
    mtStatusMessage.setVehiclePlatformId(vpi);
    mtStatusMessage.setStatus(status);
    return mtStatusMessage;
  }

  public static PingEntity createPingEntity(String randomId, String vpi) {
    PingEntity entity = new PingEntity();
    entity.setVpi(vpi);
    entity.setCorrelationId(randomId);
    entity.setStatus(Status.SUCCESS);
    entity.setStartTimeInMillis(START_TIME);
    entity.setServiceVersion(SERVICE_VERSION);
    entity.setPayloadSent(PAYLOAD.getBytes());
    entity.setCopyPayload(true);
    return entity;
  }

  public static PingToVehicleRequest createPingToVehicleRequest(String vpi, boolean copyToPayload, String payload, Channel channel) {
    PingToVehicleRequest req = new PingToVehicleRequest();
    req.setVehiclePlatformId(vpi);
    req.setCopyPayload(copyToPayload);
    req.setChannel(channel);
    req.setTimeout(10);
    req.setPayload(payload);
    return req;
  }

  public static PingToVehicleRequest createPingToVehicleRequestUdp(String vpi, boolean copyToPayload, String payload) {
    PingToVehicleRequest req = new PingToVehicleRequest();
    req.setVehiclePlatformId(vpi);
    req.setCopyPayload(copyToPayload);
    req.setChannel(Channel.UDP);
    req.setTimeout(10);
    req.setPayload(payload);
    return req;
  }

  public static String createVehiclePlatformIdentifier() {
    return VehicleIdentifier.fromString(randomUUID()).toString();
  }

  public static void setupMockConfiguration() {
    WireMock.configureFor(Containers.wireMockContainer.getHost(), Containers.wireMockContainer.getPort());
    MOCK_CONFIGURATION.deleteAllProperties();
    MOCK_CONFIGURATION.setSite("local");
    MOCK_CONFIGURATION.setEnvironment("local");
    MOCK_CONFIGURATION.setSolution("local");

    final String componentShortName = MOCK_CONFIGURATION.getComponentShortName();
    MOCK_CONFIGURATION.setPropertySpecific(componentShortName, "oauth2.idpm2m.secret", "Y2xpZW50SWQ6Y2xpZW50U2VjcmV0");
  }

  public static void stubAuth() {
    String response = "{\n"
        + "    \"accountId\"     : \"1234567890ABCDEF1234567890ABCDEF\",\n"
        + "    \"clientId\"      : \"dummyClientId\",\n"
        + "    \"userId\"        : \"1234567890ABCDEF1234567890ABCDEF\",\n"
        + "    \"userContextId\" : \"dummyContextId\",\n"
        + "    \"labels\"        : [],\n"
        + "    \"scopes\"        : []\n"
        + "}";

    WireMock.stubFor(
        WireMock.post("/verify")
            .willReturn(WireMock.aResponse().withBody(response).withHeader(HttpHeaders.CONTENT_TYPE, VerifyTokenResponse.VERIFYTOKEN_RESPONSE_TYPE.toString()))
    );
  }

  public static void stubSubr() {
    WireMock.stubFor(
        WireMock.get(WireMock.urlMatching("/api/publishers/vps/messages/MT_MESSAGE/subscriptions"))
            .willReturn(WireMock.aResponse()
                .withStatus(200)));
  }

  public static void stubSubscriptions() {
    Multimap<String, Object> options = ArrayListMultimap.create();
    options.put("SYSTEM", EMEA);

    SubscriptionStubber.builder()
        .whenPublisherWithName("vps")
        .triesToPublishMessageOfType(MessageTypesJms.TCE_MT_MESSAGE_TYPE)
        .withOptions(options)
        .thenMessageShouldBeDeliveredTo(List.of(new Destination(MessageTypesJms.TCE_MT_MESSAGE_TYPE, MessageTypesJms.VERSION_2_0, TCE_DESTINATION)));
    logger.info("Mock subscription to TCE: {}", TCE_DESTINATION);

    SubscriptionStubber.builder()
        .whenPublisherWithName("vps")
        .triesToPublishMessageOfType(MessageTypes.MT_MESSAGE)
        .withOptions(options)
        .thenMessageShouldBeDeliveredTo(new Destination(MessageTypes.MT_MESSAGE, MessageTypes.VERSION_1_0, MT_DISPATCHER_DESTINATION));
    logger.info("Mock subscription to MtDispatcher: {}", MT_DISPATCHER_DESTINATION);
  }

  public static void stubTus(String vpi, int serviceVersion) {
    logger.info("Mocking TUS with vpi='{}', serviceVersion='{}' and system='{}'", vpi, serviceVersion, EMEA);

    TelematicUnit tu = createTelematicUnit(vpi, (long) serviceVersion);
    String payload;
    try {
      payload = getPayload(tu);
    } catch (JAXBException e) {
      throw new RuntimeException(e);
    }

    WireMock.stubFor(WireMock.get(WireMock.urlPathEqualTo("/system/" + vpi))
        .willReturn(WireMock.aResponse().withStatus(200).withHeader("Content-Type", "text/plain").withBody(EMEA)));

    WireMock.stubFor(WireMock.get(WireMock.urlMatching("/telematicunit/" + vpi + "/256"))
        .willReturn(WireMock.aResponse()
            .withStatus(200)
            .withHeader("Content-Type", "application/vnd.com.wirelesscar.telematicunitservice.telematicunit.v1.0+xml")
            .withBody(payload)));
  }

  public static String stubTusSubscription(int serviceVersion) {
    String vpi = TestUtil.createVehiclePlatformIdentifier();
    TestUtil.stubTus(vpi, serviceVersion);
    TestUtil.stubSubscriptions();

    return vpi;
  }

  public static void verifyDataApiSuccess(String vpi, LegacyOutput vehiclePingDataResponse) {
    Assertions.assertEquals(vpi, vehiclePingDataResponse.vpi());
    Assertions.assertNull(vehiclePingDataResponse.payloadSent());
    Assertions.assertNull(vehiclePingDataResponse.payloadReceived());
    Assertions.assertNotNull(vehiclePingDataResponse.startTime());
    Assertions.assertNotNull(vehiclePingDataResponse.stopTime());
    Assertions.assertTrue(vehiclePingDataResponse.success());
    Assertions.assertNull(vehiclePingDataResponse.errorReason());
  }

  public static void verifyGetDataApiPending(String vpi, LegacyOutput initialRestResponse) {
    Assertions.assertEquals(vpi, initialRestResponse.vpi());
    Assertions.assertFalse(initialRestResponse.success());
    Assertions.assertTrue(initialRestResponse.errorReason().contains("PENDING"));
    Assertions.assertNull(initialRestResponse.stopTime());
    Assertions.assertNotNull(initialRestResponse.startTime());
    Assertions.assertNull(initialRestResponse.payloadReceived());
    Assertions.assertNull(initialRestResponse.payloadSent());
  }

  public static void verifyPingEntity(PingEntity pingEntityFinalDb, String vpi) {
    Assertions.assertEquals(Status.ERROR.name(), pingEntityFinalDb.getStatus().name());
    Assertions.assertEquals(vpi, pingEntityFinalDb.getVpi());
    Assertions.assertArrayEquals(PAYLOAD.getBytes(), pingEntityFinalDb.getPayloadSent());
    Assertions.assertNull(pingEntityFinalDb.getPayloadReceived());
    Assertions.assertNotNull(pingEntityFinalDb.getStartTimeInMillis());
    Assertions.assertNotNull(pingEntityFinalDb.getStopTimeInMillis());
    Assertions.assertNotNull(pingEntityFinalDb.getError());
  }

  public static void verifyPingEntity(Status expectedStatus, PingEntity pingEntityFinalDb, String vpi) {
    Assertions.assertEquals(expectedStatus, pingEntityFinalDb.getStatus());
    Assertions.assertEquals(vpi, pingEntityFinalDb.getVpi());
    Assertions.assertNull(pingEntityFinalDb.getPayloadSent());
    Assertions.assertNull(pingEntityFinalDb.getPayloadReceived());
    Assertions.assertNotNull(pingEntityFinalDb.getStartTimeInMillis());
    Assertions.assertNotNull(pingEntityFinalDb.getStopTimeInMillis());
  }

  public static void verifyPingEntityPending(String vpi, PingEntity pingEntity) {
    Assertions.assertEquals(vpi, pingEntity.getVpi());
    Assertions.assertEquals(Status.PENDING, pingEntity.getStatus());
    Assertions.assertNull(pingEntity.getStopTimeInMillis());
    Assertions.assertNotNull(pingEntity.getStartTimeInMillis());
  }

  public static void verifyPingEntityPendingWithBeefy(PingEntity pingEntityFinalDb, String vpi) {
    Assertions.assertEquals(Status.PENDING.name(), pingEntityFinalDb.getStatus().name());
    Assertions.assertEquals(vpi, pingEntityFinalDb.getVpi());
    Assertions.assertArrayEquals(TestUtil.PAYLOAD.getBytes(), pingEntityFinalDb.getPayloadSent());
    Assertions.assertNull(pingEntityFinalDb.getPayloadReceived());
    Assertions.assertNotNull(pingEntityFinalDb.getStartTimeInMillis());
    Assertions.assertNull(pingEntityFinalDb.getStopTimeInMillis());
  }

  public static void verifyPingEntitySuccess(PingEntity pingEntityFinalDb, String vpi) {
    Assertions.assertEquals(Status.SUCCESS.name(), pingEntityFinalDb.getStatus().name());
    Assertions.assertEquals(vpi, pingEntityFinalDb.getVpi());
    Assertions.assertNull(pingEntityFinalDb.getPayloadSent());
    Assertions.assertNull(pingEntityFinalDb.getPayloadReceived());
    Assertions.assertNotNull(pingEntityFinalDb.getStartTimeInMillis());
    Assertions.assertNotNull(pingEntityFinalDb.getStopTimeInMillis());
  }

  public static void verifyPingEntitySuccessWithBeefy(PingEntity pingEntityFinalDb, String vpi) {
    Assertions.assertEquals(Status.SUCCESS.name(), pingEntityFinalDb.getStatus().name());
    Assertions.assertEquals(vpi, pingEntityFinalDb.getVpi());
    Assertions.assertArrayEquals(TestUtil.PAYLOAD.getBytes(), pingEntityFinalDb.getPayloadSent());
    Assertions.assertNotNull(pingEntityFinalDb.getPayloadReceived());
    Assertions.assertNotNull(pingEntityFinalDb.getStartTimeInMillis());
    Assertions.assertNotNull(pingEntityFinalDb.getStopTimeInMillis());
  }

  public static void verifyPingEntitySuccessWithPayload(PingEntity pingEntityFinalDb, String vpi) {
    Assertions.assertEquals(Status.SUCCESS.name(), pingEntityFinalDb.getStatus().name());
    Assertions.assertEquals(vpi, pingEntityFinalDb.getVpi());
    Assertions.assertArrayEquals(TestUtil.PAYLOAD.getBytes(), pingEntityFinalDb.getPayloadSent());
    Assertions.assertNull(pingEntityFinalDb.getPayloadReceived());
    Assertions.assertNotNull(pingEntityFinalDb.getStartTimeInMillis());
    Assertions.assertNotNull(pingEntityFinalDb.getStopTimeInMillis());
  }

  private static @NotNull TelematicUnit createTelematicUnit(String vpi, long serviceVersion) {
    TelematicUnit tu = new TelematicUnit();
    tu.setSystem(EMEA);
    tu.setVehicleId(vpi);
    tu.setVersion(serviceVersion);
    return tu;
  }

  private static String getPayload(final Object data) throws JAXBException {
    final Marshaller marshaller = JAXBContext.newInstance(data.getClass()).createMarshaller();
    final StringWriter sw = new StringWriter();

    marshaller.marshal(data, sw);

    return sw.toString();
  }

  private static String randomUUID() {
    return UUID.randomUUID().toString().replace("-", "");
  }
}
