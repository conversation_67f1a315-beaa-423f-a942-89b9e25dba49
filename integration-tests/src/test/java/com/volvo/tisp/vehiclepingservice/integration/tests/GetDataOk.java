package com.volvo.tisp.vehiclepingservice.integration.tests;

import java.io.IOException;
import java.util.UUID;

import org.springframework.stereotype.Component;
import org.springframework.test.web.reactive.server.WebTestClient;

import com.nimbusds.jose.JOSEException;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.Idpm2mMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.RestUtil;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingDataResponse;

@Component
public class GetDataOk {
  private final JWKSClientTestMock jWKSClientTestMock;
  private final PingEntityService pingEntityService;
  private final WebTestClient webTestClient;

  public GetDataOk(PingEntityService pingEntityService, JWKSClientTestMock jWKSClientTestMock, WebTestClient webTestClient) {
    this.pingEntityService = pingEntityService;
    this.jWKSClientTestMock = jWKSClientTestMock;
    this.webTestClient = webTestClient;
  }

  public void test() throws JOSEException, IOException {
    String correlationId = UUID.randomUUID().toString();
    String vpi = TestUtil.createVehiclePlatformIdentifier();
    PingEntity pingEntity = TestUtil.createPingEntity(correlationId, vpi);
    pingEntityService.upsert(pingEntity);

    // Mock service access token
    Idpm2mMock.mockCallToFetchServiceAccessToken("mh.w svc.256", "clientId");
    String obsToken = jWKSClientTestMock.setUpJwtToken();

    RestUtil.verifyGetHttpResponse(webTestClient, "/v2/data/correlationId/" + correlationId, obsToken, VehiclePingDataResponse.class);
  }
}