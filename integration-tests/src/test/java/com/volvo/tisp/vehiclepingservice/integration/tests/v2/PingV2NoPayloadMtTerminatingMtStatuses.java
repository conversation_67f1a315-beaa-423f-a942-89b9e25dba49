package com.volvo.tisp.vehiclepingservice.integration.tests.v2;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.IdpMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.Idpm2mMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.MtDispatcherMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.StateRepositoryRestMockExtended;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.TceMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.RestUtil;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.model.PingToVehicleRequest;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingDataResponse;
import com.volvo.tisp.vps.api.PingResponse;


@Component
public class PingV2NoPayloadMtTerminatingMtStatuses {
  private final JWKSClientTestMock jWKSClientTestMock;
  private final MtDispatcherMock mtDispatcherMock;
  private final MtMessagePublisher mtMessagePublisher;
  private final PingEntityService pingEntityService;
  private final StateRepositoryRestMockExtended stateRepositoryRestMock;
  private final TceMock tceMock;
  private final WebTestClient webTestClient;

  public PingV2NoPayloadMtTerminatingMtStatuses(JWKSClientTestMock jWKSClientTestMock, MtDispatcherMock mtDispatcherMock, MtMessagePublisher mtMessagePublisher,
      PingEntityService pingEntityService, StateRepositoryRestMockExtended stateRepositoryRestMock, TceMock tceMock, WebTestClient webTestClient) {
    this.jWKSClientTestMock = jWKSClientTestMock;
    this.mtDispatcherMock = mtDispatcherMock;
    this.mtMessagePublisher = mtMessagePublisher;
    this.pingEntityService = pingEntityService;
    this.stateRepositoryRestMock = stateRepositoryRestMock;
    this.tceMock = tceMock;
    this.webTestClient = webTestClient;
  }

  private static void verifyDataApi(String vpi, VehiclePingDataResponse vehiclePingDataResponse) {
    assertEquals(vpi, vehiclePingDataResponse.getVpi());
    assertNull(vehiclePingDataResponse.getPayloadSent());
    assertNull(vehiclePingDataResponse.getPayloadReceived());
    assertNotNull(vehiclePingDataResponse.getStartTime());
    assertNotNull(vehiclePingDataResponse.getStopTime());
    assertEquals(VehiclePingDataResponse.StatusEnum.FAILED, vehiclePingDataResponse.getStatus());
    assertNotNull(vehiclePingDataResponse.getErrorDescription());
  }

  public void test(boolean mtDispatcher, int mtStatusType) throws Exception {
    mtMessagePublisher.setMtDispatcherFeature(mtDispatcher);

    if (mtDispatcher) {
      Idpm2mMock.mockCallToFetchServiceAccessToken("mh.w svc.256", "clientId");
    }
    String obsToken = jWKSClientTestMock.setUpJwtToken();

    String vpi = TestUtil.createVehiclePlatformIdentifier();
    // Stub TUS and subscriptions
    TestUtil.stubTus(vpi, mtDispatcher ? 2 : 1);
    TestUtil.stubSubscriptions();

    stateRepositoryRestMock.stubPush(Instant.now().plus(3, ChronoUnit.MINUTES), 200);

    PingToVehicleRequest pingToVehicleRequest = TestUtil.createPingToVehicleRequestUdp(vpi, false, null);
    // REST api call
    String correlationId = String.valueOf(
        RestUtil.verifyPostHttpResponse(webTestClient, "/v2/ping", obsToken, pingToVehicleRequest, PingResponse.class)
            .getCorrelationId());

    stateRepositoryRestMock.stubPop(correlationId, List.of(correlationId));

    PingEntity pingEntity = pingEntityService.findByCorrelationId(correlationId).get();
    TestUtil.verifyPingEntityPending(vpi, pingEntity);

    // Respond with Pong
    if (mtDispatcher) {
      com.volvo.tisp.vc.mt.message.client.json.v1.Status status = switch (mtStatusType) {
        case 1 -> com.volvo.tisp.vc.mt.message.client.json.v1.Status.OVERRIDDEN;
        case 2 -> com.volvo.tisp.vc.mt.message.client.json.v1.Status.CANCELED;
        case 3 -> com.volvo.tisp.vc.mt.message.client.json.v1.Status.FAILED;
        case 4 -> com.volvo.tisp.vc.mt.message.client.json.v1.Status.TIMEOUT;
        default -> com.volvo.tisp.vc.mt.message.client.json.v1.Status.ASSET_NOT_FOUND;
      };

      mtDispatcherMock.moRouterReplyMtStatus(correlationId, vpi, status);
    } else {
      String status = switch (mtStatusType) {
        case 1 -> "OVERRIDDEN";
        case 2 -> "CANCELLED";
        case 3 -> "FAILED";
        case 4 -> "TIMEOUT";
        default -> "SERVICE_UNSUPPORTED";
      };
      tceMock.replyMtStatus(correlationId, vpi, status);
    }

    // Poll DB data
    Awaitility.await()
        .atMost(Duration.of(10, ChronoUnit.SECONDS))
        .until(() -> pingEntityService.findByCorrelationId(correlationId).join().getStatus() != Status.PENDING);
    PingEntity pingEntityFinalDb = pingEntityService.findByCorrelationId(correlationId).join();

    Status expectedStatus = switch (mtStatusType) {
      case 1 -> Status.OVERRIDDEN;
      case 2 -> Status.CANCELLED;
      case 3 -> Status.ERROR;
      case 4 -> Status.TIMEOUT;
      default -> Status.ERROR; /* 5 */
    };

    // assert DB data
    TestUtil.verifyPingEntity(expectedStatus, pingEntityFinalDb, vpi);

    // get REST data
    VehiclePingDataResponse vehiclePingDataResponse = RestUtil.verifyGetHttpResponse(webTestClient, "/v2/data/correlationId/" + correlationId, obsToken,
        VehiclePingDataResponse.class);

    // validate REST data
    verifyDataApi(vpi, vehiclePingDataResponse);

    if (mtDispatcher) {
      IdpMock.verifyIdpWiremockTokenInteractions(1);
    }
  }
}