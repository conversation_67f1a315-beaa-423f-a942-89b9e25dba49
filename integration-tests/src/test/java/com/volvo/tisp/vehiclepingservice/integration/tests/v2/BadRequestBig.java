package com.volvo.tisp.vehiclepingservice.integration.tests.v2;

import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.test.web.reactive.server.WebTestClient;

import com.volvo.tisp.vehiclepingservice.integration.tests.mock.Idpm2mMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.model.PingToVehicleRequest;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;

@Component
public class BadRequestBig {
  private final JWKSClientTestMock jWKSClientTestMock;
  private final MtMessagePublisher mtMessagePublisher;
  private final ServiceAccessTokenService serviceAccessTokenService;
  private final WebTestClient webTestClient;

  public BadRequestBig(JWKSClientTestMock jWKSClientTestMock, MtMessagePublisher mtMessagePublisher, ServiceAccessTokenService serviceAccessTokenService,
      WebTestClient webTestClient) {
    this.jWKSClientTestMock = jWKSClientTestMock;
    this.mtMessagePublisher = mtMessagePublisher;
    this.serviceAccessTokenService = serviceAccessTokenService;
    this.webTestClient = webTestClient;
  }

  public void test() throws Exception {
    mtMessagePublisher.setMtDispatcherFeature(true);
    serviceAccessTokenService.setServiceAccessTokenValidationEnabled(true);

    Idpm2mMock.mockCallToFetchServiceAccessToken("mh.w svc.256", "clientId");
    String obsToken = jWKSClientTestMock.setUpJwtToken();

    String vpi = TestUtil.stubTusSubscription(2);

    String body = "-".repeat(128000);

    PingToVehicleRequest pingToVehicleRequest = TestUtil.createPingToVehicleRequestUdp(vpi, true, body);
    // REST api call
    webTestClient.post()
        .uri("/v2/ping")
        .header("Authorization", "Bearer " + obsToken)
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(pingToVehicleRequest)
        .exchange()
        .expectStatus().isBadRequest();
  }
}