package com.volvo.tisp.vehiclepingservice.integration.tests.v1;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import org.awaitility.Awaitility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.test.web.reactive.server.WebTestClient;

import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.IdpMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.Idpm2mMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.MtDispatcherMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.StateRepositoryRestMockExtended;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.TceMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.LegacyOutput;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.RestUtil;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;

@Component
public class PingV2NoPayloadLegacy {
  private static final Logger logger = LoggerFactory.getLogger(PingV2NoPayloadLegacy.class);
  private final JWKSClientTestMock jWKSClientTestMock;
  private final MtDispatcherMock mtDispatcherMock;
  private final MtMessagePublisher mtMessagePublisher;
  private final PingEntityService pingEntityService;
  private final ServiceAccessTokenService serviceAccessTokenService;
  private final StateRepositoryRestMockExtended stateRepositoryRestMock;
  private final TceMock tceMock;
  private final WebTestClient webTestClient;

  public PingV2NoPayloadLegacy(JWKSClientTestMock jWKSClientTestMock, MtDispatcherMock mtDispatcherMock, MtMessagePublisher mtMessagePublisher,
      PingEntityService pingEntityService, ServiceAccessTokenService serviceAccessTokenService, StateRepositoryRestMockExtended stateRepositoryRestMock,
      TceMock tceMock, WebTestClient webTestClient) {
    this.jWKSClientTestMock = jWKSClientTestMock;
    this.mtDispatcherMock = mtDispatcherMock;
    this.mtMessagePublisher = mtMessagePublisher;
    this.pingEntityService = pingEntityService;
    this.serviceAccessTokenService = serviceAccessTokenService;
    this.stateRepositoryRestMock = stateRepositoryRestMock;
    this.tceMock = tceMock;
    this.webTestClient = webTestClient;
  }

  public void test(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation, boolean unauthorized) throws Exception {
    mtMessagePublisher.setMtDispatcherFeature(mtDispatcher);
    serviceAccessTokenService.setServiceAccessTokenValidationEnabled(tokenValidation);
    logger.info("MtDispatcher enabled={}", mtDispatcher);
    logger.info("ServiceAccessTokenValidation enabled={}", tokenValidation);

    String vpi = TestUtil.stubTusSubscription(2);

    if (mtDispatcher) {
      Idpm2mMock.mockCallToFetchServiceAccessToken("mh.w svc.256", "clientId");
    }
    String obsToken = null;
    if (tokenValidation) {
      obsToken = jWKSClientTestMock.setUpJwtToken();
    }

    stateRepositoryRestMock.stubPush(Instant.now().plus(3, ChronoUnit.MINUTES), 200);

    String correlationId;
    if (unauthorized) {
      correlationId = RestUtil.verifyGetHttpResponse(webTestClient, "/unauthorized/vehicleping/vpi/" + vpi, String.class);
    } else {
      correlationId = RestUtil.verifyGetHttpResponse(webTestClient, "/vehicleping/vpi/" + vpi, obsToken, String.class);
    }

    PingEntity db = pingEntityService.findByCorrelationId(correlationId).get();
    TestUtil.verifyPingEntityPending(vpi, db);

    LegacyOutput getDataPending = RestUtil.verifyGetHttpResponse(webTestClient, "/vehicleping/data/correlationid/" + correlationId, obsToken,
        LegacyOutput.class);

    TestUtil.verifyGetDataApiPending(vpi, getDataPending);

    stateRepositoryRestMock.stubPop(correlationId, List.of(correlationId));
    if (mtDispatcher) {
      mtDispatcherMock.checkAndReplyPongV2(useMoRouter, obsToken);
    } else {
      tceMock.checkAndReplyPongV2();
    }

    Awaitility.await()
        .atMost(Duration.of(10, ChronoUnit.SECONDS))
        .until(() -> pingEntityService.findByCorrelationId(correlationId).get().getStatus() != Status.PENDING);

    PingEntity finalDb = pingEntityService.findByCorrelationId(correlationId).get();
    TestUtil.verifyPingEntitySuccess(finalDb, vpi);

    LegacyOutput vehiclePingDataResponse = RestUtil.verifyGetHttpResponse(webTestClient, "/vehicleping/data/correlationid/" + correlationId, obsToken,
        LegacyOutput.class);
    TestUtil.verifyDataApiSuccess(vpi, vehiclePingDataResponse);

    if (mtDispatcher) {
      IdpMock.verifyIdpWiremockTokenInteractions(1);
    }
  }
}
