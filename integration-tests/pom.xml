<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp.vehiclepingservice</groupId>
    <artifactId>vehicle-ping-service-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>integration-tests</artifactId>
  <packaging>jar</packaging>
  <name>Vehicle Ping Service :: Server :: Integration Tests</name>

  <dependencies>
    <!-- Application under test -->
    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-server-app</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-server-impl</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-database</artifactId>
      <version>${project.version}</version>
    </dependency>
    <!-- Test framework -->
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- Spring Boot Test and Reactor WebTestClient support -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- Testcontainers and WireMock for integration testing -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-testcontainers</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>testcontainers</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>activemq</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mongodb</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>localstack</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.wiremock.integrations.testcontainers</groupId>
      <artifactId>wiremock-testcontainers-module</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.wirelesscar.subscriptionrepository</groupId>
      <artifactId>subscriptionrepository-client-test-util</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- STATE MOCK -->
    <dependency>
      <groupId>com.wirelesscar.staterepository-client</groupId>
      <artifactId>staterepository-client-mock</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- IDPM2M MOCK -->
    <dependency>
      <groupId>com.volvo.tisp.idpm2m</groupId>
      <artifactId>identityprovider-m2m-client-mock</artifactId>
    </dependency>

    <!-- JWT -->
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-impl</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-jackson</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!-- Use failsafe for integration tests if desired -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>3.5.3</version>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
