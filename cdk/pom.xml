<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp.vehiclepingservice</groupId>
    <artifactId>vehicle-ping-service-server</artifactId>
    <version>0-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>vehicle-ping-service-server-cdk</artifactId>

  <properties>
    <spotbugs.failOnError>false</spotbugs.failOnError>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.volvo.vgcs.de.cdk</groupId>
      <artifactId>pipelines-constructs-base</artifactId>
      <version>${pipelines-constructs-base.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.vgcs.de.cdk</groupId>
      <artifactId>de-pipelines-constructs</artifactId>
      <version>${de-pipelines-constructs.version}</version>
    </dependency>
    <dependency>
      <groupId>software.amazon.awscdk</groupId>
      <artifactId>aws-cdk-lib</artifactId>
      <version>${aws-cdk-lib.version}</version>
    </dependency>
    <dependency>
      <groupId>software.constructs</groupId>
      <artifactId>constructs</artifactId>
      <version>${constructs.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.cdk.constructs</groupId>
      <artifactId>tisp-cdk-constructs</artifactId>
      <version>${tisp-cdk-constructs.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.auto.value</groupId>
      <artifactId>auto-value-annotations</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-configuration2</artifactId>
      <version>${commons-configuration2.version}</version>
    </dependency>

    <!-- Test -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <annotationProcessorPaths combine.children="append">
            <path>
              <groupId>com.google.auto.value</groupId>
              <artifactId>auto-value</artifactId>
              <version>${autovalue.version}</version>
            </path>
          </annotationProcessorPaths>
          <failOnWarning>false</failOnWarning>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
