{"app": "mvn -e -q exec:java -Dexec.mainClass=com.volvo.tisp.moros.cdk.MoRouterServerCdkApp", "context": {"config": {"global": {"instance": {"type": "t3a.small", "min": "1", "max": "2"}}, "build": {}, "test": {"eu-west-1": {"ec2-allowed-sqs-arn": ["arn:aws:sqs:eu-west-1:131500841528:mo_rcv_queue"], "integration-log-event-config": {"kinesis-arn": "arn:aws:kinesis:eu-west-1:871869755054:stream/integration-logging-source-stream", "kms-arn": "arn:aws:kms:eu-west-1:871869755054:key/88653868-e440-42ed-8b43-928a9d36ba78"}}}, "iot1": {"eu-west-1": {"ec2-allowed-sqs-arn": ["arn:aws:sqs:eu-west-1:753034065663:mo_rcv_queue"], "integration-log-event-config": {"kinesis-arn": "arn:aws:kinesis:eu-west-1:449531230221:stream/integration-logging-source-stream", "kms-arn": "arn:aws:kms:eu-west-1:449531230221:key/b57cd513-0263-46da-bf6d-4c227baa89e8"}}, "us-east-1": {"ec2-allowed-sqs-arn": ["arn:aws:sqs:us-east-1:753034065663:mo_rcv_queue"]}}, "qa": {"eu-west-1": {"ec2-allowed-sqs-arn": ["arn:aws:sqs:eu-west-1:127285105207:mo_rcv_queue"], "integration-log-event-config": {"kinesis-arn": "arn:aws:kinesis:eu-west-1:221901989683:stream/integration-logging-source-stream", "kms-arn": "arn:aws:kms:eu-west-1:221901989683:key/a2cc0150-0e35-4a4c-bf23-0206a9338057"}}, "ap-northeast-1": {"ec2-allowed-sqs-arn": ["arn:aws:sqs:ap-northeast-1:354918379449:mo_rcv_queue"]}, "us-east-1": {"ec2-allowed-sqs-arn": ["arn:aws:sqs:us-east-1:127285105207:mo_rcv_queue"]}}, "prod": {"eu-west-1": {"ec2-allowed-sqs-arn": ["arn:aws:sqs:eu-west-1:877610408563:mo_rcv_queue"], "integration-log-event-config": {"kinesis-arn": "arn:aws:kinesis:eu-west-1:061485807246:stream/integration-logging-source-stream", "kms-arn": "arn:aws:kms:eu-west-1:061485807246:key/d7df5b1b-73f3-42dc-afde-4d27d412dbfc"}}, "ap-northeast-1": {"ec2-allowed-sqs-arn": ["arn:aws:sqs:ap-northeast-1:************:mo_rcv_queue"]}}}, "@aws-cdk/aws-lambda:recognizeLayerVersion": true, "@aws-cdk/core:checkSecretUsage": true, "@aws-cdk/core:target-partitions": ["aws", "aws-cn"], "@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": true, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": true, "@aws-cdk/aws-ecs:arnFormatIncludesClusterName": true, "@aws-cdk/aws-iam:minimizePolicies": true, "@aws-cdk/core:validateSnapshotRemovalPolicy": true, "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName": true, "@aws-cdk/aws-s3:createDefaultLoggingPolicy": true, "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption": true, "@aws-cdk/aws-apigateway:disableCloudWatchRole": true, "@aws-cdk/core:enablePartitionLiterals": true, "@aws-cdk/aws-events:eventsTargetQueueSameAccount": true, "@aws-cdk/aws-iam:standardizedServicePrincipals": true, "@aws-cdk/aws-ecs:disableExplicitDeploymentControllerForCircuitBreaker": true, "@aws-cdk/aws-iam:importedRoleStackSafeDefaultPolicyName": true, "@aws-cdk/aws-s3:serverAccessLogsUseBucketPolicy": true, "@aws-cdk/aws-route53-patters:useCertificate": true, "@aws-cdk/customresources:installLatestAwsSdkDefault": false, "@aws-cdk/aws-rds:databaseProxyUniqueResourceName": true, "@aws-cdk/aws-codedeploy:removeAlarmsFromDeploymentGroup": true, "@aws-cdk/aws-apigateway:authorizerChangeDeploymentLogicalId": true, "@aws-cdk/aws-ec2:launchTemplateDefaultUserData": true, "@aws-cdk/aws-secretsmanager:useAttachedSecretResourcePolicyForSecretTargetAttachments": true, "@aws-cdk/aws-redshift:columnId": true, "@aws-cdk/aws-stepfunctions-tasks:enableEmrServicePolicyV2": true, "@aws-cdk/aws-ec2:restrictDefaultSecurityGroup": true, "@aws-cdk/aws-apigateway:requestValidatorUniqueId": true, "@aws-cdk/aws-kms:aliasNameRef": true, "@aws-cdk/aws-autoscaling:generateLaunchTemplateInsteadOfLaunchConfig": true, "@aws-cdk/core:includePrefixInUniqueNameGeneration": true, "@aws-cdk/aws-efs:denyAnonymousAccess": true, "@aws-cdk/aws-opensearchservice:enableOpensearchMultiAzWithStandby": true, "@aws-cdk/aws-lambda-nodejs:useLatestRuntimeVersion": true, "@aws-cdk/aws-efs:mountTargetOrderInsensitiveLogicalId": true, "@aws-cdk/aws-rds:auroraClusterChangeScopeOfInstanceParameterGroupWithEachParameters": true, "@aws-cdk/aws-appsync:useArnForSourceApiAssociationIdentifier": true, "@aws-cdk/aws-rds:preventRenderingDeprecatedCredentials": true}}