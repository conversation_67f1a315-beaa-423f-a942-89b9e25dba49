{"app": "mvn -e -q exec:java -Dexec.mainClass=com.volvo.tisp.mtdisps.cdk.MtDispatcherServerCdkApp", "context": {"config": {"global": {"instance": {"type": "t3a.small", "min": "1", "max": "2"}}, "build": {"sqs-sender-account-principals": ["************"]}, "test": {"eu-west-1": {"instance": {"type": "m7a.large", "min": "2", "max": "4"}, "integration-log-event-config": {"kinesis-arn": "arn:aws:kinesis:eu-west-1:************:stream/integration-logging-source-stream", "kms-arn": "arn:aws:kms:eu-west-1:************:key/********-e440-42ed-8b43-928a9d36ba78"}, "sqs-sender-account-principals": ["************"]}}, "iot1": {"eu-west-1": {"integration-log-event-config": {"kinesis-arn": "arn:aws:kinesis:eu-west-1:************:stream/integration-logging-source-stream", "kms-arn": "arn:aws:kms:eu-west-1:************:key/b57cd513-0263-46da-bf6d-4c227baa89e8"}, "sqs-sender-account-principals": ["************"]}, "us-east-1": {"sqs-sender-account-principals": ["************"]}}, "qa": {"eu-west-1": {"integration-log-event-config": {"kinesis-arn": "arn:aws:kinesis:eu-west-1:************:stream/integration-logging-source-stream", "kms-arn": "arn:aws:kms:eu-west-1:************:key/a2cc0150-0e35-4a4c-bf23-0206a9338057"}, "sqs-sender-account-principals": ["************"]}, "us-east-1": {"sqs-sender-account-principals": ["************"]}, "ap-northeast-1": {"sqs-sender-account-principals": ["************"]}}, "prod": {"eu-west-1": {"integration-log-event-config": {"kinesis-arn": "arn:aws:kinesis:eu-west-1:************:stream/integration-logging-source-stream", "kms-arn": "arn:aws:kms:eu-west-1:************:key/d7df5b1b-73f3-42dc-afde-4d27d412dbfc"}, "sqs-sender-account-principals": ["************"]}}}, "@aws-cdk/aws-lambda:recognizeLayerVersion": true, "@aws-cdk/core:checkSecretUsage": true, "@aws-cdk/core:target-partitions": ["aws", "aws-cn"], "@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": true, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": true, "@aws-cdk/aws-ecs:arnFormatIncludesClusterName": true, "@aws-cdk/aws-iam:minimizePolicies": true, "@aws-cdk/core:validateSnapshotRemovalPolicy": true, "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName": true, "@aws-cdk/aws-s3:createDefaultLoggingPolicy": true, "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption": true, "@aws-cdk/aws-apigateway:disableCloudWatchRole": true, "@aws-cdk/core:enablePartitionLiterals": true, "@aws-cdk/aws-events:eventsTargetQueueSameAccount": true, "@aws-cdk/aws-iam:standardizedServicePrincipals": true, "@aws-cdk/aws-ecs:disableExplicitDeploymentControllerForCircuitBreaker": true, "@aws-cdk/aws-iam:importedRoleStackSafeDefaultPolicyName": true, "@aws-cdk/aws-s3:serverAccessLogsUseBucketPolicy": true, "@aws-cdk/aws-route53-patters:useCertificate": true, "@aws-cdk/customresources:installLatestAwsSdkDefault": false, "@aws-cdk/aws-rds:databaseProxyUniqueResourceName": true, "@aws-cdk/aws-codedeploy:removeAlarmsFromDeploymentGroup": true, "@aws-cdk/aws-apigateway:authorizerChangeDeploymentLogicalId": true, "@aws-cdk/aws-ec2:launchTemplateDefaultUserData": true, "@aws-cdk/aws-secretsmanager:useAttachedSecretResourcePolicyForSecretTargetAttachments": true, "@aws-cdk/aws-redshift:columnId": true, "@aws-cdk/aws-stepfunctions-tasks:enableEmrServicePolicyV2": true, "@aws-cdk/aws-ec2:restrictDefaultSecurityGroup": true, "@aws-cdk/aws-apigateway:requestValidatorUniqueId": true, "@aws-cdk/aws-kms:aliasNameRef": true, "@aws-cdk/aws-autoscaling:generateLaunchTemplateInsteadOfLaunchConfig": true, "@aws-cdk/core:includePrefixInUniqueNameGeneration": true, "@aws-cdk/aws-efs:denyAnonymousAccess": true, "@aws-cdk/aws-opensearchservice:enableOpensearchMultiAzWithStandby": true, "@aws-cdk/aws-lambda-nodejs:useLatestRuntimeVersion": true, "@aws-cdk/aws-efs:mountTargetOrderInsensitiveLogicalId": true, "@aws-cdk/aws-rds:auroraClusterChangeScopeOfInstanceParameterGroupWithEachParameters": true, "@aws-cdk/aws-appsync:useArnForSourceApiAssociationIdentifier": true, "@aws-cdk/aws-rds:preventRenderingDeprecatedCredentials": true}}