package com.volvo.tisp.moros.cdk;

import java.util.List;

import com.volvo.tisp.cdk.constructs.CustomEgressRules;
import com.volvo.tisp.cdk.constructs.TispAlbToAsg;
import com.volvo.tisp.cdk.constructs.TispAlbToAsgProps;
import com.volvo.tisp.moros.cdk.config.AppConfiguration;
import com.volvo.tisp.moros.cdk.config.ConfigLoader;
import com.volvo.tisp.moros.cdk.dto.InstanceConfig;
import com.volvo.vgcs.pipelines.constructs.base.VgcsStack;
import com.volvo.vgcs.pipelines.constructs.base.VpcLookup;

import software.amazon.awscdk.Fn;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.services.autoscaling.CfnAutoScalingGroupProps;
import software.amazon.awscdk.services.ec2.ISecurityGroup;
import software.amazon.awscdk.services.ec2.IVpc;
import software.amazon.awscdk.services.ec2.SecurityGroup;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationListener;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationListenerAttributes;
import software.amazon.awscdk.services.elasticloadbalancingv2.IApplicationListener;
import software.constructs.Construct;

public class MoRouterServerCdkStack extends VgcsStack {
  private static final String VTCE_ALB_STACK_NAME = "vtce-alb";
  private static final String ALB_FQDN_KEY = VTCE_ALB_STACK_NAME + "-alb-fqdn";

  public MoRouterServerCdkStack(final Construct scope, final String id, final StackProps props) {
    super(scope, id, props);

    final AppConfiguration appConfiguration = ConfigLoader.loadConfig(this);

    InstanceConfig ec2InstanceConfig = appConfiguration.getEc2InstanceConfig();

    final IVpc vpc = VpcLookup.getVpc(this, "vpc");

    final String sgId = Fn.importValue(VTCE_ALB_STACK_NAME + "-sg-id");
    final ISecurityGroup securityGroup =
        SecurityGroup.fromSecurityGroupId(this, "moros-imported-sg", sgId);

    final List<CustomEgressRules> egressRules =
        List.of(
            CustomEgressRules.builder()
                .ipv4Cidr("0.0.0.0/0")
                .tcpPort(27017)
                .description("mongo-db")
                .build(),
            CustomEgressRules.builder()
                .ipv4Cidr("0.0.0.0/0")
                .tcpPort(1024)
                .description("mongo-srv-db-a")
                .build(),
            CustomEgressRules.builder()
                .ipv4Cidr("0.0.0.0/0")
                .tcpPort(1025)
                .description("mongo-srv-db-b")
                .build(),
            CustomEgressRules.builder()
                .ipv4Cidr("0.0.0.0/0")
                .tcpPort(1026)
                .description("mongo-srv-db-c")
                .build(),
            CustomEgressRules.builder()
                .ipv4Cidr("0.0.0.0/0")
                .tcpPort(11580)
                .description("Subscription Repository")
                .build(),
            CustomEgressRules.builder()
                .ipv4Cidr("0.0.0.0/0")
                .tcpPort(10370)
                .description("VQV")
                .build());

    final String listenerArn = Fn.importValue(VTCE_ALB_STACK_NAME + "-listener-arn");
    final IApplicationListener applicationListener =
        ApplicationListener.fromApplicationListenerAttributes(
            this,
            "moros-imported-listener",
            ApplicationListenerAttributes.builder()
                .listenerArn(listenerArn)
                .securityGroup(securityGroup)
                .build());

    CfnAutoScalingGroupProps cfnAutoScalingGroupProps = CfnAutoScalingGroupProps.builder()
        .minSize(ec2InstanceConfig.min())
        .maxSize(ec2InstanceConfig.max())
        .defaultInstanceWarmup(60)
        .healthCheckGracePeriod(30)
        .build();

    final TispAlbToAsgProps tispProps =
        TispAlbToAsgProps.builder()
            .albSg(securityGroup)
            .priority(30)
            .autoscalingProps(cfnAutoScalingGroupProps)
            .egressRules(egressRules)
            .existingListener(applicationListener)
            .existingVpc(vpc)
            .fqdnKey(ALB_FQDN_KEY)
            .instanceType(ec2InstanceConfig.instanceType())
            .port(48090)
            .shortName("moros")
            .build();
    TispAlbToAsg tispAlbToAsg = new TispAlbToAsg(this, "moros-tisp-alb-asg", tispProps);

    InstanceRoleConstruct morosInstanceRoleConstruct = new InstanceRoleConstruct(this, "morosInstanceRoleConstruct", appConfiguration);

    morosInstanceRoleConstruct.getPolicyStatementList()
        .forEach(policyStatement -> tispAlbToAsg.getFleet().getInstanceRole().addToPolicy(policyStatement));
  }
}
