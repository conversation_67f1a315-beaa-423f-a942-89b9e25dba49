package com.volvo.tisp.moros.cdk.config;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.configuration2.Configuration;

import com.volvo.tisp.moros.cdk.dto.InstanceConfig;
import com.volvo.tisp.moros.cdk.dto.IntegrationLogEventConfig;

@SuppressWarnings("rawtypes")
public class AppConfiguration {
  private final Configuration configuration;

  AppConfiguration(Configuration configuration) {
    this.configuration = configuration;
  }

  public List<String> getAllowedSqsArn() {
    return this.configuration.getList(String.class, "ec2-allowed-sqs-arn", Collections.emptyList());
  }

  public InstanceConfig getEc2InstanceConfig() {
    Map instance = new HashMap<String, Object>(this.configuration.get(Map.class, "instance", Collections.emptyMap()));
    return new InstanceConfig(instance.get("type").toString(), instance.get("min").toString(), instance.get("max").toString());
  }

  public IntegrationLogEventConfig getIntegrationLogEventConfig() {
    Map instance = new HashMap<String, Object>(this.configuration.get(Map.class, "integration-log-event-config", Collections.emptyMap()));
    return new IntegrationLogEventConfig(instance.getOrDefault("kinesis-arn", "").toString(), instance.getOrDefault("kms-arn", "").toString());
  }
}
