package com.volvo.tisp.moros.cdk.config;

import java.util.Collections;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.configuration2.MapConfiguration;

import com.volvo.vgcs.pipelines.constructs.base.BuildEnvironment;

import software.amazon.awscdk.Stack;

public class ConfigLoader {
  public static final String ENV;
  public static final String REGION;
  public static final String SOLUTION;

  private static final String GLOBAL = "global";

  static {
    ENV = Optional.ofNullable(BuildEnvironment.getDeployEnvironment()).map(environment -> environment.toLowerCase(Locale.ROOT)).orElse("build");
    REGION = Optional.ofNullable(BuildEnvironment.getDeployRegion()).map(region -> region.toLowerCase(Locale.ROOT)).orElse("eu-west-1");
    SOLUTION = Optional.of(BuildEnvironment.getSolution()).map(solution -> solution.toLowerCase(Locale.ROOT)).orElse("shared");
  }

  private ConfigLoader() {
  }

  public static AppConfiguration loadConfig(Stack stack) {
    return new AppConfiguration(new MapConfiguration(getConfigMap(stack)));
  }

  private static Map<String, Object> getConfigMap(Stack stack) {
    Map<String, Map<String, Object>> configMap = (Map<String, Map<String, Object>>) stack.getNode().tryGetContext("config");
    HashMap<String, Object> globalConfig = new HashMap<>(configMap.getOrDefault(GLOBAL, Collections.emptyMap()));
    Map<String, Object> envMap = configMap.getOrDefault(ENV, Collections.emptyMap());
    HashMap<String, Object> regionMap = new HashMap<>((Map<String, Object>) envMap.getOrDefault(REGION, Collections.emptyMap()));

    globalConfig.putAll(regionMap);

    return globalConfig;
  }
}
