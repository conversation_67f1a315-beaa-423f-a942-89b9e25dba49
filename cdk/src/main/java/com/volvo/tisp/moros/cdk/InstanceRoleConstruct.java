package com.volvo.tisp.moros.cdk;

import java.util.ArrayList;
import java.util.List;

import org.jetbrains.annotations.NotNull;

import com.volvo.tisp.moros.cdk.config.AppConfiguration;
import com.volvo.tisp.moros.cdk.dto.IntegrationLogEventConfig;

import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.constructs.Construct;

public class InstanceRoleConstruct extends Construct {
  private final List<PolicyStatement> policyStatementList = new ArrayList<>();

  public InstanceRoleConstruct(@NotNull Construct scope, @NotNull String id, AppConfiguration appConfiguration) {
    super(scope, id);

    IntegrationLogEventConfig integrationLogEventConfig = appConfiguration.getIntegrationLogEventConfig();

    if (!appConfiguration.getAllowedSqsArn().isEmpty()) {
      PolicyStatement sqsAllowedPolicy = PolicyStatement.Builder.create()
          .effect(Effect.ALLOW)
          .actions(List.of("sqs:ReceiveMessage",
              "sqs:DeleteMessage",
              "sqs:GetQueueUrl",
              "sqs:GetQueueAttributes",
              "sqs:ChangeMessageVisibility"))
          .resources(appConfiguration.getAllowedSqsArn())
          .build();
      this.policyStatementList.add(sqsAllowedPolicy);
    }

    if (!integrationLogEventConfig.kinesisArn().isEmpty()) {
      PolicyStatement kinesisPolicy = PolicyStatement.Builder.create()
          .effect(Effect.ALLOW)
          .actions(List.of("kinesis:DescribeStreamSummary",
              "kinesis:ListShards",
              "kinesis:PutRecord",
              "kinesis:PutRecords"))
          .resources(List.of(integrationLogEventConfig.kinesisArn()))
          .build();
      this.policyStatementList.add(kinesisPolicy);
    }

    if (!integrationLogEventConfig.kmsArn().isEmpty()) {
      PolicyStatement kmsPolicy = PolicyStatement.Builder.create()
          .effect(Effect.ALLOW)
          .actions(List.of("kms:Encrypt",
              "kms:Decrypt",
              "kms:DescribeKey",
              "kms:ListKeys",
              "kms:GenerateDataKey",
              "kms:ReEncrypt"))
          .resources(List.of(integrationLogEventConfig.kmsArn()))
          .build();
      this.policyStatementList.add(kmsPolicy);
    }
  }

  List<PolicyStatement> getPolicyStatementList() {
    return policyStatementList;
  }
}
