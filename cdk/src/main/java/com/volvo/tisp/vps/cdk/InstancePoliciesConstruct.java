package com.volvo.tisp.vps.cdk;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.jetbrains.annotations.NotNull;

import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.constructs.Construct;

public class InstancePoliciesConstruct extends Construct {
  private final List<PolicyStatement> policyStatementList = new ArrayList<>();

  public InstancePoliciesConstruct(@NotNull Construct scope, @NotNull String id, String idpM2mBucketArn) {
    super(scope, id);

    Optional.ofNullable(idpM2mBucketArn).ifPresent(arn -> {
      PolicyStatement s3Policy = PolicyStatement.Builder.create()
          .effect(Effect.ALLOW)
          .actions(List.of("s3:*"))
          .resources(List.of(arn, arn + "/*"))
          .build();

      this.policyStatementList.add(s3Policy);
    });
  }

  List<PolicyStatement> getPolicyStatementList() {
    return policyStatementList;
  }
}
