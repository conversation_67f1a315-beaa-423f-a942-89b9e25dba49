package com.volvo.tisp.vps.cdk.config;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.configuration2.Configuration;

import com.volvo.tisp.vps.cdk.dto.InstanceConfig;

@SuppressWarnings("rawtypes")
public class AppConfiguration {
  private final Configuration configuration;

  public AppConfiguration(Configuration configuration) {
    this.configuration = configuration;
  }

  public InstanceConfig getInstanceConfig() {
    Map instance = new HashMap<String, Object>(this.configuration.get(Map.class, "instance", Collections.emptyMap()));
    return new InstanceConfig(instance.get("type").toString(), instance.get("min").toString(), instance.get("max").toString());
  }

  public String idpM2mBucketArn() {
    return this.configuration.getString("idpM2mBucketArn");
  }
}
