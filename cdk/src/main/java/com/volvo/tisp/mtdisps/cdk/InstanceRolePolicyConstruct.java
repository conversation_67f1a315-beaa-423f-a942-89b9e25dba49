package com.volvo.tisp.mtdisps.cdk;

import java.util.Collection;
import java.util.List;

import org.jetbrains.annotations.NotNull;

import com.volvo.tisp.mtdisps.cdk.config.AppConfiguration;
import com.volvo.tisp.mtdisps.cdk.dto.IntegrationLogEventConfig;

import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.IRole;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.constructs.Construct;

public class InstanceRolePolicyConstruct extends Construct {
  public InstanceRolePolicyConstruct(@NotNull Construct scope, AppConfiguration appConfiguration, IRole instanceRole,
      Collection<SqsQueueConstruct> sqsQueuesToReadFrom) {
    super(scope, "mtdisps-instance-role-policy");

    IntegrationLogEventConfig integrationLogEventConfig = appConfiguration.getIntegrationLogEventConfig();

    if (!integrationLogEventConfig.kinesisArn().isEmpty()) {
      PolicyStatement kinesisPolicy = PolicyStatement.Builder.create()
          .effect(Effect.ALLOW)
          .actions(List.of("kinesis:DescribeStreamSummary",
              "kinesis:ListShards",
              "kinesis:PutRecord",
              "kinesis:PutRecords"))
          .resources(List.of(integrationLogEventConfig.kinesisArn()))
          .build();
      instanceRole.addToPrincipalPolicy(kinesisPolicy);
    }

    if (!integrationLogEventConfig.kmsArn().isEmpty()) {
      PolicyStatement kmsPolicy = PolicyStatement.Builder.create()
          .effect(Effect.ALLOW)
          .actions(List.of("kms:Encrypt",
              "kms:Decrypt",
              "kms:DescribeKey",
              "kms:ListKeys",
              "kms:GenerateDataKey",
              "kms:ReEncrypt"))
          .resources(List.of(integrationLogEventConfig.kmsArn()))
          .build();
      instanceRole.addToPrincipalPolicy(kmsPolicy);
    }

    if (!sqsQueuesToReadFrom.isEmpty()) {
      instanceRole.addToPrincipalPolicy(buildSqsQueueReadPermissionsPolicyStatement(sqsQueuesToReadFrom));
    }
  }

  private PolicyStatement buildSqsQueueReadPermissionsPolicyStatement(Collection<SqsQueueConstruct> sqsQueueConstructs) {
    return PolicyStatement.Builder.create()
        .effect(Effect.ALLOW)
        .actions(List.of("sqs:ReceiveMessage",
            "sqs:DeleteMessage",
            "sqs:GetQueueUrl",
            "sqs:GetQueueAttributes",
            "sqs:ChangeMessageVisibility"))
        .resources(sqsQueueConstructs.stream().map(sqsQueueConstruct -> sqsQueueConstruct.getQueue().getQueueArn()).toList())
        .build();
  }
}
