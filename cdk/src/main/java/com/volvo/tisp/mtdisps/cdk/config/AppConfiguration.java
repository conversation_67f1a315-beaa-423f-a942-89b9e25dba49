package com.volvo.tisp.mtdisps.cdk.config;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.configuration2.Configuration;

import com.volvo.tisp.mtdisps.cdk.dto.InstanceConfig;
import com.volvo.tisp.mtdisps.cdk.dto.IntegrationLogEventConfig;
import com.volvo.tisp.mtdisps.cdk.dto.SqsConfig;

import software.amazon.awscdk.services.iam.AccountPrincipal;

@SuppressWarnings("rawtypes")
public class AppConfiguration {
  private final Configuration configuration;

  public AppConfiguration(Configuration configuration) {
    this.configuration = configuration;
  }

  public InstanceConfig getInstanceConfig() {
    Map instance = new HashMap<String, Object>(this.configuration.get(Map.class, "instance", Collections.emptyMap()));
    return new InstanceConfig(instance.get("type").toString(), instance.get("min").toString(), instance.get("max").toString());
  }

  public IntegrationLogEventConfig getIntegrationLogEventConfig() {
    Map instance = new HashMap<String, Object>(this.configuration.get(Map.class, "integration-log-event-config", Collections.emptyMap()));
    return new IntegrationLogEventConfig(instance.getOrDefault("kinesis-arn", "").toString(), instance.getOrDefault("kms-arn", "").toString());
  }

  public SqsConfig getSqsConfig() {
    List<String> sqsSendAccountPrincipals = this.configuration.getList(String.class, "sqs-sender-account-principals", List.of());
    List<AccountPrincipal> accountPrincipals = sqsSendAccountPrincipals.stream()
        .map(AccountPrincipal::new)
        .toList();
    return new SqsConfig(accountPrincipals);
  }
}