package com.volvo.tisp.mtdisps.cdk;

import java.util.Collection;
import java.util.List;

import com.volvo.tisp.mtdisps.cdk.config.AppConfiguration;
import com.volvo.tisp.mtdisps.cdk.config.ConfigLoader;
import com.volvo.vgcs.pipelines.constructs.base.VgcsStack;

import software.amazon.awscdk.StackProps;
import software.constructs.Construct;

public class MtDispatcherServerStatefulCdkStack extends VgcsStack {
  private final Collection<SqsQueueConstruct> sqsQueueConstructs;

  public MtDispatcherServerStatefulCdkStack(final Construct scope, final String id, final StackProps props) {
    super(scope, id, props);

    AppConfiguration appConfiguration = ConfigLoader.loadConfig(this);

    SqsQueueConstruct mtdispsInMtMessageQueue = new SqsQueueConstruct(this, "mtdispsInMtMessageQueue", "mtdisps-in-mt-message-queue", appConfiguration.getSqsConfig());
    SqsQueueConstruct mtdispsInMtMessageWithAssetHardwareIdQueue = new SqsQueueConstruct(this, "mtdispsInMtMessageWithAhi", "mtdisps-in-mt-message-with-ahi",
        appConfiguration.getSqsConfig());
    this.sqsQueueConstructs = List.of(mtdispsInMtMessageQueue, mtdispsInMtMessageWithAssetHardwareIdQueue);
  }

  public Collection<SqsQueueConstruct> getSqsQueueConstructs() {
    return this.sqsQueueConstructs;
  }
}
