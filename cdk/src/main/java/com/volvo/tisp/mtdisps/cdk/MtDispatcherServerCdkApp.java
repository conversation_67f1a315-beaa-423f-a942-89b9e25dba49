package com.volvo.tisp.mtdisps.cdk;

import software.amazon.awscdk.App;
import software.amazon.awscdk.StackProps;

public class MtDispatcherServerCdkApp {
  public static void main(final String[] args) {
    App app = new App();

    MtDispatcherServerStatefulCdkStack mtDispatcherServerStatefulCdkStack = new MtDispatcherServerStatefulCdkStack(app, "MtDispatcherServerStatefulCdkStack",
        StackProps.builder().terminationProtection(true).build());

    new MtDispatcherServerCdkStack(app, "mtDispatcherServerCdkStack", StackProps.builder().build(), mtDispatcherServerStatefulCdkStack);

    app.synth();
  }
}
