package com.volvo.tisp.mtdisps.cdk;

import java.util.List;

import org.jetbrains.annotations.NotNull;

import com.volvo.tisp.mtdisps.cdk.config.ConfigLoader;
import com.volvo.tisp.mtdisps.cdk.dto.SqsConfig;

import software.amazon.awscdk.Duration;
import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.amazon.awscdk.services.sqs.DeadLetterQueue;
import software.amazon.awscdk.services.sqs.IQueue;
import software.amazon.awscdk.services.sqs.Queue;
import software.constructs.Construct;

public class SqsQueueConstruct extends Construct {
  private final IQueue queue;

  public SqsQueueConstruct(@NotNull Construct scope, String id, String queueName, SqsConfig sqsConfig) {
    super(scope, id);

    IQueue dlq = Queue.Builder.create(this, id.concat("Dlq"))
        .queueName(createQueueName(queueName + "-dlq"))
        .retentionPeriod(Duration.days(14))
        .receiveMessageWaitTime(Duration.seconds(20))
        .build();

    this.queue = Queue.Builder.create(this, id)
        .queueName(createQueueName(queueName))
        .retentionPeriod(Duration.days(14))
        .receiveMessageWaitTime(Duration.seconds(20))
        .visibilityTimeout(Duration.seconds(120))
        .deadLetterQueue(DeadLetterQueue.builder().queue(dlq).maxReceiveCount(3).build())
        .build();

    addResourcePolicyToQueue(sqsConfig);
  }

  private static String createQueueName(String queueSuffix) {
    return String.join("-", ConfigLoader.ENV, ConfigLoader.REGION, ConfigLoader.SOLUTION, ConfigLoader.ENV, queueSuffix);
  }

  void addResourcePolicyToQueue(SqsConfig sqsConfig) {
    if (!sqsConfig.senderAccountPrincipals().isEmpty()) {
      PolicyStatement sendPolicyStatement = PolicyStatement.Builder.create()
          .effect(Effect.ALLOW)
          .principals(sqsConfig.senderAccountPrincipals())
          .resources(List.of(getQueue().getQueueArn()))
          .actions(List.of("sqs:SendMessage", "sqs:GetQueueUrl", "sqs:GetQueueAttributes"))
          .build();

      this.queue.addToResourcePolicy(sendPolicyStatement);
    }
  }

  IQueue getQueue() {
    return this.queue;
  }
}
