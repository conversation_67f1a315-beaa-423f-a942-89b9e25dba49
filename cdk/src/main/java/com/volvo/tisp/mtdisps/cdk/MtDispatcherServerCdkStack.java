package com.volvo.tisp.mtdisps.cdk;

import java.util.List;

import com.volvo.tisp.cdk.constructs.CustomEgressRules;
import com.volvo.tisp.cdk.constructs.TispAlbToAsg;
import com.volvo.tisp.cdk.constructs.TispAlbToAsgProps;
import com.volvo.tisp.mtdisps.cdk.config.AppConfiguration;
import com.volvo.tisp.mtdisps.cdk.config.ConfigLoader;
import com.volvo.tisp.mtdisps.cdk.dto.InstanceConfig;
import com.volvo.vgcs.pipelines.constructs.base.VgcsStack;
import com.volvo.vgcs.pipelines.constructs.base.VpcLookup;

import software.amazon.awscdk.Fn;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.services.autoscaling.CfnAutoScalingGroupProps;
import software.amazon.awscdk.services.ec2.ISecurityGroup;
import software.amazon.awscdk.services.ec2.IVpc;
import software.amazon.awscdk.services.ec2.Port;
import software.amazon.awscdk.services.ec2.SecurityGroup;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationListener;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationListenerAttributes;
import software.amazon.awscdk.services.elasticloadbalancingv2.IApplicationListener;
import software.constructs.Construct;

public class MtDispatcherServerCdkStack extends VgcsStack {
  public static final String IPV_4_CIDR = "0.0.0.0/0";
  private static final String VTCE_ALB_STACK_NAME = "vtce-alb";
  private static final String ALB_FQDN_KEY = VTCE_ALB_STACK_NAME + "-alb-fqdn";

  public MtDispatcherServerCdkStack(final Construct scope, final String id, final StackProps props,
      final MtDispatcherServerStatefulCdkStack mtDispatcherServerStatefulCdkStack) {
    super(scope, id, props);

    AppConfiguration appConfiguration = ConfigLoader.loadConfig(this);

    InstanceConfig instanceConfig = appConfiguration.getInstanceConfig();
    IVpc vpc = VpcLookup.getVpc(this, "vpc");

    String sgId = Fn.importValue(VTCE_ALB_STACK_NAME + "-sg-id");
    ISecurityGroup securityGroup = SecurityGroup.fromSecurityGroupId(this, "imported-sg", sgId);

    List<CustomEgressRules> egressRules =
        List.of(
            CustomEgressRules.builder()
                .ipv4Cidr(IPV_4_CIDR)
                .tcpEgressPort(Port.tcp(27017))
                .description("mongodb database")
                .build(),
            CustomEgressRules.builder()
                .ipv4Cidr(IPV_4_CIDR)
                .tcpEgressPort(Port.tcpRange(1024, 1026))
                .description("mongodb altas database")
                .build(),
            CustomEgressRules.builder()
                .ipv4Cidr(IPV_4_CIDR)
                .tcpEgressPort(Port.tcp(11580))
                .description("Subscription Repository")
                .build(),
            CustomEgressRules.builder()
                .ipv4Cidr(IPV_4_CIDR)
                .tcpEgressPort(Port.tcp(10370))
                .description("VQV")
                .build());

    IApplicationListener applicationListener = ApplicationListener.fromApplicationListenerAttributes(
        this,
        "imported-listener",
        ApplicationListenerAttributes.builder()
            .listenerArn(Fn.importValue(VTCE_ALB_STACK_NAME + "-listener-arn"))
            .securityGroup(securityGroup)
            .build());

    CfnAutoScalingGroupProps cfnAutoScalingGroupProps = CfnAutoScalingGroupProps.builder()
        .minSize(instanceConfig.min())
        .maxSize(instanceConfig.max())
        .healthCheckGracePeriod(30)
        .defaultInstanceWarmup(60)
        .build();

    TispAlbToAsgProps tispProps =
        TispAlbToAsgProps.builder()
            .albSg(securityGroup)
            .priority(40)
            .autoscalingProps(cfnAutoScalingGroupProps)
            .egressRules(egressRules)
            .existingListener(applicationListener)
            .existingVpc(vpc)
            .fqdnKey(ALB_FQDN_KEY)
            .instanceType(instanceConfig.instanceType())
            .port(48000)
            .shortName("mtdisps")
            .build();

    TispAlbToAsg tispAlbToAsg = new TispAlbToAsg(this, "mtdisps-tisp-alb-asg", tispProps);

    new InstanceRolePolicyConstruct(this, appConfiguration, tispAlbToAsg.getFleet().getInstanceRole(),
        mtDispatcherServerStatefulCdkStack.getSqsQueueConstructs());
  }
}
