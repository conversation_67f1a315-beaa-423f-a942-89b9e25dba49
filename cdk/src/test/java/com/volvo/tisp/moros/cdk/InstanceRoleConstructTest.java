package com.volvo.tisp.moros.cdk;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.volvo.tisp.moros.cdk.config.AppConfiguration;
import com.volvo.tisp.moros.cdk.config.ConfigLoader;
import com.volvo.tisp.moros.cdk.dto.InstanceConfig;
import com.volvo.tisp.moros.cdk.dto.IntegrationLogEventConfig;

import software.amazon.awscdk.App;
import software.amazon.awscdk.services.iam.PolicyStatement;

class InstanceRoleConstructTest {

  private App app;
  private AppConfiguration appConfiguration;
  private MockedStatic<ConfigLoader> mockedConfigLoader;

  @BeforeEach
  void setUp() {
    app = new App();
    appConfiguration = Mockito.mock(AppConfiguration.class);
    when(appConfiguration.getEc2InstanceConfig()).thenReturn(new InstanceConfig("t3.micro", "1", "1"));

    // Mock ConfigLoader
    mockedConfigLoader = Mockito.mockStatic(ConfigLoader.class);
    mockedConfigLoader.when(() -> ConfigLoader.loadConfig(Mockito.any())).thenReturn(appConfiguration);
  }

  @AfterEach
  void tearDown() {
    if (mockedConfigLoader != null) {
      mockedConfigLoader.close();
    }
  }

  @Test
  void testInstanceRoleConstructWithAllConfigs() {
    when(appConfiguration.getAllowedSqsArn()).thenReturn(List.of("arn:aws:sqs:us-west-2:123456789012:test-queue"));
    when(appConfiguration.getIntegrationLogEventConfig()).thenReturn(
        new IntegrationLogEventConfig(
            "arn:aws:kinesis:us-west-2:123456789012:stream/test-stream",
            "arn:aws:kms:us-west-2:123456789012:key/test-key"
        )
    );

    InstanceRoleConstruct instanceRole = new InstanceRoleConstruct(app, "TestInstanceRole", appConfiguration);
    List<PolicyStatement> policyStatements = instanceRole.getPolicyStatementList();

    assertEquals(3, policyStatements.size(), "Should have three policy statements for SQS, Kinesis, and KMS");

    PolicyStatement sqsPolicy = policyStatements.get(0);
    assertTrue(sqsPolicy.getActions().containsAll(Arrays.asList(
        "sqs:ReceiveMessage", "sqs:DeleteMessage", "sqs:GetQueueUrl",
        "sqs:GetQueueAttributes", "sqs:ChangeMessageVisibility"
    )), "SQS policy should contain all expected actions");

    PolicyStatement kinesisPolicy = policyStatements.get(1);
    assertTrue(kinesisPolicy.getActions().containsAll(Arrays.asList(
        "kinesis:DescribeStreamSummary", "kinesis:ListShards",
        "kinesis:PutRecord", "kinesis:PutRecords"
    )), "Kinesis policy should contain all expected actions");

    PolicyStatement kmsPolicy = policyStatements.get(2);
    assertTrue(kmsPolicy.getActions().containsAll(Arrays.asList(
        "kms:Encrypt", "kms:Decrypt", "kms:DescribeKey",
        "kms:ListKeys", "kms:GenerateDataKey", "kms:ReEncrypt"
    )), "KMS policy should contain all expected actions");
  }

  @Test
  void testInstanceRoleConstructWithEmptyConfig() {
    when(appConfiguration.getAllowedSqsArn()).thenReturn(List.of());
    when(appConfiguration.getIntegrationLogEventConfig()).thenReturn(new IntegrationLogEventConfig("", ""));

    InstanceRoleConstruct instanceRole = new InstanceRoleConstruct(app, "TestInstanceRole", appConfiguration);
    List<PolicyStatement> policyStatements = instanceRole.getPolicyStatementList();

    assertTrue(policyStatements.isEmpty(), "Policy statements should be empty with empty configuration");
  }

  @Test
  void testInstanceRoleConstructWithKinesisConfig() {
    when(appConfiguration.getAllowedSqsArn()).thenReturn(List.of());
    when(appConfiguration.getIntegrationLogEventConfig()).thenReturn(
        new IntegrationLogEventConfig("arn:aws:kinesis:us-west-2:123456789012:stream/test-stream", "")
    );

    InstanceRoleConstruct instanceRole = new InstanceRoleConstruct(app, "TestInstanceRole", appConfiguration);
    List<PolicyStatement> policyStatements = instanceRole.getPolicyStatementList();

    assertEquals(1, policyStatements.size(), "Should have one policy statement for Kinesis");
    PolicyStatement kinesisPolicy = policyStatements.get(0);
    assertTrue(kinesisPolicy.getActions().containsAll(Arrays.asList(
        "kinesis:DescribeStreamSummary", "kinesis:ListShards",
        "kinesis:PutRecord", "kinesis:PutRecords"
    )), "Kinesis policy should contain all expected actions");
  }

  @Test
  void testInstanceRoleConstructWithKmsConfig() {
    when(appConfiguration.getAllowedSqsArn()).thenReturn(List.of());
    when(appConfiguration.getIntegrationLogEventConfig()).thenReturn(
        new IntegrationLogEventConfig("", "arn:aws:kms:us-west-2:123456789012:key/test-key")
    );

    InstanceRoleConstruct instanceRole = new InstanceRoleConstruct(app, "TestInstanceRole", appConfiguration);
    List<PolicyStatement> policyStatements = instanceRole.getPolicyStatementList();

    assertEquals(1, policyStatements.size(), "Should have one policy statement for KMS");
    PolicyStatement kmsPolicy = policyStatements.get(0);
    assertTrue(kmsPolicy.getActions().containsAll(Arrays.asList(
        "kms:Encrypt", "kms:Decrypt", "kms:DescribeKey",
        "kms:ListKeys", "kms:GenerateDataKey", "kms:ReEncrypt"
    )), "KMS policy should contain all expected actions");
  }

  @Test
  void testInstanceRoleConstructWithSqsConfig() {
    when(appConfiguration.getAllowedSqsArn()).thenReturn(List.of("arn:aws:sqs:us-west-2:123456789012:test-queue"));
    when(appConfiguration.getIntegrationLogEventConfig()).thenReturn(new IntegrationLogEventConfig("", ""));

    InstanceRoleConstruct instanceRole = new InstanceRoleConstruct(app, "TestInstanceRole", appConfiguration);
    List<PolicyStatement> policyStatements = instanceRole.getPolicyStatementList();

    assertEquals(1, policyStatements.size(), "Should have one policy statement for SQS");
    PolicyStatement sqsPolicy = policyStatements.get(0);
    assertTrue(sqsPolicy.getActions().containsAll(Arrays.asList(
        "sqs:ReceiveMessage", "sqs:DeleteMessage", "sqs:GetQueueUrl",
        "sqs:GetQueueAttributes", "sqs:ChangeMessageVisibility"
    )), "SQS policy should contain all expected actions");
  }
}
