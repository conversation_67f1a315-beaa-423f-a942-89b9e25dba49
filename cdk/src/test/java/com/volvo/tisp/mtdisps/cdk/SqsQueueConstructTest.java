package com.volvo.tisp.mtdisps.cdk;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.mtdisps.cdk.dto.SqsConfig;
import com.volvo.vgcs.pipelines.constructs.base.BuildEnvironment;

import software.amazon.awscdk.Stack;
import software.amazon.awscdk.assertions.Match;
import software.amazon.awscdk.assertions.Template;
import software.amazon.awscdk.services.iam.AccountPrincipal;

class SqsQueueConstructTest {
  public static final String ACCOUNT_NUMBER = "local";

  @Test
  void testSqsQueueConstruct() {
    Stack stack = new Stack();
    new SqsQueueConstruct(stack, "testQueue", "test-queue", new SqsConfig(List.of(new AccountPrincipal(ACCOUNT_NUMBER))));
    String solution = Optional.ofNullable(BuildEnvironment.getSolution())
        .filter(str -> !str.isBlank())
        .map(sol -> sol.toLowerCase(Locale.ROOT))
        .orElse("shared");
    Template template = Template.fromStack(stack);
    template.hasResourceProperties("AWS::SQS::Queue", Map.of(
        "QueueName", String.format("build-eu-west-1-%s-build-test-queue", solution),
        "ReceiveMessageWaitTimeSeconds", 20,
        "MessageRetentionPeriod", 1209600,
        "VisibilityTimeout", 120
    ));

    template.hasResourceProperties("AWS::SQS::Queue", Map.of(
        "QueueName", String.format("build-eu-west-1-%s-build-test-queue-dlq", solution),
        "ReceiveMessageWaitTimeSeconds", 20,
        "MessageRetentionPeriod", 1209600
    ));

    template.hasResourceProperties("AWS::SQS::QueuePolicy", Map.of("PolicyDocument", Map.of(
        "Statement", List.of(
            Map.of(
                "Action", List.of("sqs:SendMessage",
                    "sqs:GetQueueUrl",
                    "sqs:GetQueueAttributes"),
                "Effect", "Allow",
                "Principal", Map.of(
                    "AWS", Map.of(
                        "Fn::Join", List.of(
                            "",
                            List.of(
                                "arn:",
                                Map.of("Ref", "AWS::Partition"),
                                Match.stringLikeRegexp(":iam::.*:root")
                            )
                        )
                    )
                )
            )
        )
    )));
  }
}
