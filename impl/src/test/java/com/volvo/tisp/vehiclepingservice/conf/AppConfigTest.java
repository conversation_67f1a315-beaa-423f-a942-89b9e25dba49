package com.volvo.tisp.vehiclepingservice.conf;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

import com.mongodb.client.MongoDatabase;
import com.volvo.tisp.idpm2m.client.config.JWKSClientProperties;
import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.staterepository.StateTimeoutHandler;
import com.volvo.tisp.vehiclepingservice.metrics.ServiceAccessTokenMetricReporter;
import com.volvo.tisp.vehiclepingservice.util.TokenFetcher;

class AppConfigTest {
  private static JWKSClientProperties.Cache.BosKeysVehicle createBosKeysVehicle() {
    JWKSClientProperties.Cache.BosKeysVehicle bosKeysVehicle = new JWKSClientProperties.Cache.BosKeysVehicle();
    bosKeysVehicle.setEvictionTimeSeconds("5");

    return bosKeysVehicle;
  }

  private static JWKSClientProperties.Cache createCache() {
    JWKSClientProperties.Cache.Vehicle vehicle = createVehicle();
    JWKSClientProperties.Cache.BosKeysVehicle bosKeysVehicle = createBosKeysVehicle();

    return new JWKSClientProperties.Cache(vehicle, bosKeysVehicle);
  }

  private static JWKSClientProperties.S3Values createS3Values() {
    return new JWKSClientProperties.S3Values("t3Bucket", "http://mockUrl.com");
  }

  private static JWKSClientProperties.Cache.Vehicle createVehicle() {
    JWKSClientProperties.Cache.Vehicle vehicle = new JWKSClientProperties.Cache.Vehicle();
    vehicle.setMaxKeys("5");
    vehicle.setEvictionTimeSeconds("5");

    return vehicle;
  }

  @Test
  void compressionHandlerTest() {
    Assertions.assertNotNull(new AppConfig().compressionHandler());
  }

  @Test
  void jwksClientTest() {
    Assertions.assertNotNull(new AppConfig().jwksClient(new JWKSClientProperties(createS3Values(), createCache())));
  }

  @Test
  void mongoDatabaseTest() {
    MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
    Mockito.when(mongoTemplate.getDb()).thenReturn(Mockito.mock(MongoDatabase.class));

    Assertions.assertNotNull(new AppConfig().mongoDatabase(mongoTemplate));
  }

  @Test
  void serviceAccessTokenClientTest() {
    Assertions.assertNotNull(new AppConfig().serviceAccessTokenClient(Mockito.mock(TokenFetcher.class), Mockito.mock(ServiceAccessTokenMetricReporter.class)));
  }

  @Test
  void stateRepositoryTest() {
    StateRepository.Builder stateRepositoryBuilder = Mockito.mock(StateRepository.Builder.class);
    StateTimeoutHandler stateTimeoutHandler = Mockito.mock(StateTimeoutHandler.class);
    Mockito.when(stateRepositoryBuilder.timeoutHandler(stateTimeoutHandler)).thenReturn(stateRepositoryBuilder);
    Mockito.when(stateRepositoryBuilder.build()).thenReturn(Mockito.mock(StateRepository.class));

    Assertions.assertNotNull(new AppConfig().stateRepository(stateRepositoryBuilder, stateTimeoutHandler));
  }

  @Test
  void tokenFetcherTest() {
    Assertions.assertNotNull(new AppConfig().tokenFetcher(Mockito.mock(WebClient.class)));
  }

  @Test
  void tusClientTest() {
    WebClient.Builder webClientBuilder = Mockito.mock(WebClient.Builder.class);
    Mockito.when(webClientBuilder.baseUrl("http://tus")).thenReturn(webClientBuilder);
    Mockito.when(webClientBuilder.exchangeStrategies(Mockito.any(ExchangeStrategies.class))).thenReturn(webClientBuilder);
    Mockito.when(webClientBuilder.build()).thenReturn(Mockito.mock(WebClient.class));

    Assertions.assertNotNull(new AppConfig().tusClient(webClientBuilder));
  }

  @Test
  void webClientTest() {
    WebClient.Builder webClientBuilder = Mockito.mock(WebClient.Builder.class);
    Mockito.when(webClientBuilder.baseUrl("https://idpm2m")).thenReturn(webClientBuilder);
    Mockito.when(webClientBuilder.filters(Mockito.any())).thenReturn(webClientBuilder);
    Mockito.when(webClientBuilder.build()).thenReturn(Mockito.mock(WebClient.class));

    Assertions.assertNotNull(new AppConfig().webClient(webClientBuilder));
  }
}
