package com.volvo.tisp.vehiclepingservice.util;

import static com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService.IDS;
import static com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService.RAW_SCOPE_LIST;
import static com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService.SERVICE_ID_SCOPE_LIST;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.idpm2m.client.JWKSClient;
import com.volvo.tisp.idpm2m.client.model.UnauthorizedTokenException;
import com.volvo.tisp.vehiclepingservice.metrics.ServiceAccessTokenMetricReporter;

@ExtendWith(SpringExtension.class)
class ServiceAccessTokenServiceTest {

  @Mock
  private JWKSClient jwksClient;
  @Mock
  private ServiceAccessTokenClient serviceAccessTokenClient;
  @Mock
  private ServiceAccessTokenMetricReporter serviceAccessTokenMetricReporter;
  private ServiceAccessTokenService serviceAccessTokenService;

  @Test
  void getServiceAccessTokenTest() {
    String expectedToken = "token";
    Mockito.when(serviceAccessTokenClient.fetchServiceAccessToken(SERVICE_ID_SCOPE_LIST, RAW_SCOPE_LIST))
        .thenReturn(CompletableFuture.completedFuture(expectedToken));

    String token = serviceAccessTokenService.getServiceAccessToken().join();

    Assertions.assertEquals(expectedToken, token);
    Mockito.verify(serviceAccessTokenClient).fetchServiceAccessToken(SERVICE_ID_SCOPE_LIST, RAW_SCOPE_LIST);
  }

  @BeforeEach
  void setup() {
    serviceAccessTokenService = new ServiceAccessTokenService(serviceAccessTokenClient, Optional.of(jwksClient), true, serviceAccessTokenMetricReporter);
  }

  @Test
  void validateTokenTest() throws UnauthorizedTokenException {
    String token = "token";

    ServiceAccessTokenService serviceAccessTokenServiceValidationEnabled = new ServiceAccessTokenService(serviceAccessTokenClient, Optional.of(jwksClient),
        true, serviceAccessTokenMetricReporter);
    serviceAccessTokenServiceValidationEnabled.validateToken(token);

    Mockito.verify(jwksClient).authorizeOBSServiceAccessToken(token, IDS);

    ServiceAccessTokenService serviceAccessTokenValidationServiceValidationDisabled = new ServiceAccessTokenService(serviceAccessTokenClient,
        Optional.of(jwksClient), false, serviceAccessTokenMetricReporter);
    serviceAccessTokenValidationServiceValidationDisabled.validateToken(token);

    Mockito.verifyNoMoreInteractions(jwksClient);
  }
}
