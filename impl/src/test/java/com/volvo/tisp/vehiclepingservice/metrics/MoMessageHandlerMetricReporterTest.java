package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class MoMessageHandlerMetricReporterTest {
  @Test
  void onInvalidServiceVersion() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageHandlerMetricReporter::new, (registry, reporter) -> {
      reporter.onInvalidServiceVersion();
      MetricsReporterTestUtils.checkCounter(registry, MoMessageHandlerMetricReporter.MO_MESSAGE_HANDLING,
          Tags.of(MoMessageHandlerMetricReporter.TYPE, "service-version.invalid"), 1);
    });
  }

  @Test
  void onInvalidSrpOption() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageHandlerMetricReporter::new, (registry, reporter) -> {
      reporter.onInvalidSrpOption();
      MetricsReporterTestUtils.checkCounter(registry, MoMessageHandlerMetricReporter.MO_MESSAGE_HANDLING,
          Tags.of(MoMessageHandlerMetricReporter.TYPE, "srp-option.invalid"), 1);
    });
  }

  @Test
  void onProcessingV1Failure() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageHandlerMetricReporter::new, (registry, reporter) -> {
      reporter.onProcessingV1Failure();
      MetricsReporterTestUtils.checkCounter(registry, MoMessageHandlerMetricReporter.MO_MESSAGE_HANDLING,
          Tags.of(MoMessageHandlerMetricReporter.TYPE, "processing-v1.failure"), 1);
    });
  }

  @Test
  void onProcessingV1Success() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageHandlerMetricReporter::new, (registry, reporter) -> {
      reporter.onProcessingV1Success();
      MetricsReporterTestUtils.checkCounter(registry, MoMessageHandlerMetricReporter.MO_MESSAGE_HANDLING,
          Tags.of(MoMessageHandlerMetricReporter.TYPE, "service-v1.success"), 1);
    });
  }

  @Test
  void onProcessingV2Failure() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageHandlerMetricReporter::new, (registry, reporter) -> {
      reporter.onProcessingV2Failure();
      MetricsReporterTestUtils.checkCounter(registry, MoMessageHandlerMetricReporter.MO_MESSAGE_HANDLING,
          Tags.of(MoMessageHandlerMetricReporter.TYPE, "processing-v2.failure"), 1);
    });
  }

  @Test
  void onProcessingV2Success() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageHandlerMetricReporter::new, (registry, reporter) -> {
      reporter.onProcessingV2Success();
      MetricsReporterTestUtils.checkCounter(registry, MoMessageHandlerMetricReporter.MO_MESSAGE_HANDLING,
          Tags.of(MoMessageHandlerMetricReporter.TYPE, "service-v2.success"), 1);
    });
  }

  @Test
  void onTokenValidationFailure() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageHandlerMetricReporter::new, (registry, reporter) -> {
      reporter.onTokenValidationFailure();
      MetricsReporterTestUtils.checkCounter(registry, MoMessageHandlerMetricReporter.MO_MESSAGE_HANDLING,
          Tags.of(MoMessageHandlerMetricReporter.TYPE, "token-validation.failure"), 1);
    });
  }

  @Test
  void onUnsupportedVersion() {
    MetricsReporterTestUtils.initReporterAndTest(MoMessageHandlerMetricReporter::new, (registry, reporter) -> {
      reporter.onUnsupportedVersion();
      MetricsReporterTestUtils.checkCounter(registry, MoMessageHandlerMetricReporter.MO_MESSAGE_HANDLING,
          Tags.of(MoMessageHandlerMetricReporter.TYPE, "version.unsupported"), 1);
    });
  }
}