package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class ApplicationEventReporterTest {
  private static final String APPLICATION_EVENT = "app-event";
  private static final String TYPE = "type";

  @Test
  void onApplicationReadyEventTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onApplicationReadyEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "STARTED"), 1);

      mtStatusMetricReporter.onApplicationReadyEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "STARTED"), 2);
    });
  }

  @Test
  void onContextClosedTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onContextClosed();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "STOPPED"), 1);

      mtStatusMetricReporter.onContextClosed();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "STOPPED"), 2);
    });
  }

  @Test
  void onServiceActivatedEventTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onServiceActivatedEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "ACTIVATED"), 1);

      mtStatusMetricReporter.onServiceActivatedEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "ACTIVATED"), 2);
    });
  }

  @Test
  void onServiceDeactivatedEventTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onServiceDeactivatedEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "DEACTIVATED"), 1);

      mtStatusMetricReporter.onServiceDeactivatedEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(TYPE, "DEACTIVATED"), 2);
    });
  }
}
