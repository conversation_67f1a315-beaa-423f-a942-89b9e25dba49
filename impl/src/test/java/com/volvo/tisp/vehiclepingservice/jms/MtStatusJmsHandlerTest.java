package com.volvo.tisp.vehiclepingservice.jms;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.when;

import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus;
import com.volvo.tisp.vc.mt.message.client.json.v1.Status;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.metrics.MtStatusMetricReporter;
import com.wirelesscar.tce.api.v2.MtStatusMessage;

public class MtStatusJmsHandlerTest {

  private static MtStatusMessage createMtStatusMessage(String status) {
    MtStatusMessage mtStatusMessage = new MtStatusMessage();
    mtStatusMessage.setStatus(status);
    mtStatusMessage.setVehiclePlatformId("1234567890ABCDEF1234567890ABCDEF");
    mtStatusMessage.setCorrelationId("CORR123");
    return mtStatusMessage;
  }

  private static JmsMessage<MtStatusMessage> getMockJmsMessage(MtStatusMessage mtStatusMessage) {
    JmsMessage<MtStatusMessage> message = mock(JmsMessage.class);
    Mockito.when(message.payload()).thenReturn(mtStatusMessage);
    return message;
  }

  @Test
  void MtStatusJmsHandlerConstructorInjectionTest() {
    PingManager pingManager = Mockito.mock(PingManager.class);
    MtStatusMetricReporter reporter = Mockito.mock(MtStatusMetricReporter.class);
    MtStatusJmsHandler mtStatusJmsHandler = new MtStatusJmsHandler(pingManager, reporter);
    Assertions.assertNotNull(mtStatusJmsHandler);
    mtStatusJmsHandler = new MtStatusJmsHandler(null, reporter);
    Assertions.assertNotNull(mtStatusJmsHandler);
  }

  @Test
  void pingStatusMoRouterNullPayloadTest() {
    PingManager pingManager = Mockito.mock(PingManager.class);
    MtStatusMetricReporter reporter = Mockito.mock(MtStatusMetricReporter.class);
    MtStatusJmsHandler mtStatusJmsHandler = new MtStatusJmsHandler(pingManager, reporter);

    JmsMessage<MtStatus> message = mock(JmsMessage.class);
    when(message.payload()).thenReturn(null);

    TispContext.runInContext(() -> Assertions.assertThrows(NullPointerException.class, () -> mtStatusJmsHandler.pingStatusMoRouter(message)));

    Mockito.verify(pingManager, never()).processMtStatus(anyString(), any(Status.class));
    Mockito.verifyNoMoreInteractions(pingManager);
  }

  @Test
  void pingStatusMoRouterProcessesValidMessageTest() {
    PingManager pingManager = Mockito.mock(PingManager.class);
    MtStatusMetricReporter reporter = Mockito.mock(MtStatusMetricReporter.class);
    MtStatusJmsHandler mtStatusJmsHandler = new MtStatusJmsHandler(pingManager, reporter);

    MtStatus mtStatus = createMtStatus(Status.DELIVERED);
    JmsMessage<MtStatus> message = Mockito.mock(JmsMessage.class);
    Mockito.when(message.payload()).thenReturn(mtStatus);
    Mockito.when(pingManager.processMtStatus(anyString(), any(Status.class))).thenReturn(CompletableFuture.completedFuture(null));

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = mtStatusJmsHandler.pingStatusMoRouter(message);
      Assertions.assertTrue(result.isDone());
    });
    Mockito.verify(pingManager).processMtStatus("CORR123", Status.DELIVERED);
    Mockito.verifyNoMoreInteractions(pingManager);
  }

  @Test
  void pingStatusUnknownStatusTest() {
    PingManager pingManager = Mockito.mock(PingManager.class);
    MtStatusMetricReporter reporter = Mockito.mock(MtStatusMetricReporter.class);
    MtStatusJmsHandler handler = new MtStatusJmsHandler(pingManager, reporter);

    MtStatusMessage mtStatusMessage = createMtStatusMessage("UNKNOWN_STATUS");

    JmsMessage<MtStatusMessage> message = getMockJmsMessage(mtStatusMessage);

    TispContext.runInContext(() -> Assertions.assertThrows(com.volvo.tisp.vehiclepingservice.domain.exceptions.InternalServerErrorException.class,
        () -> handler.pingStatus(message)));
    Mockito.verifyNoMoreInteractions(pingManager);
  }

  @Test
  void pingStatusWithValidMessageTest() {
    PingManager pingManager = Mockito.mock(PingManager.class);
    MtStatusMetricReporter reporter = Mockito.mock(MtStatusMetricReporter.class);
    MtStatusJmsHandler mtStatusJmsHandler = new MtStatusJmsHandler(pingManager, reporter);

    MtStatusMessage mtStatusMessage = createMtStatusMessage("DELIVERED");
    JmsMessage<MtStatusMessage> message = getMockJmsMessage(mtStatusMessage);
    Mockito.when(pingManager.processMtStatus(anyString(), any(Status.class))).thenReturn(CompletableFuture.completedFuture(null));

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = mtStatusJmsHandler.pingStatus(message);
      Assertions.assertTrue(result.isDone());
    });

    Mockito.verify(pingManager).processMtStatus("CORR123", Status.DELIVERED);
    Mockito.verifyNoMoreInteractions(pingManager);
  }

  private MtStatus createMtStatus(Status status) {
    MtStatus mtStatus = new MtStatus();
    mtStatus.withStatus(status);
    mtStatus.withCorrelationId("CORR123");
    mtStatus.withVehiclePlatformId("1234567890ABCDEF1234567890ABCDEF");
    return mtStatus;
  }
}
