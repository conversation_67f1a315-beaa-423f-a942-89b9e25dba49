package com.volvo.tisp.vehiclepingservice.converters;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.google.gson.Gson;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NotFoundException;

public class PingJsonOutputConverterTest {
  @Test
  void applyEmptyPingEntityTest() {
    Gson gson = new Gson();
    PingJsonOutputConverter pingJsonOutputConverter = new PingJsonOutputConverter(gson);

    Assertions.assertThrows(NotFoundException.class, () -> pingJsonOutputConverter.apply(null));
  }

  @Test
  void applyValidPingEntityTest() {
    Gson gson = new Gson();
    PingJsonOutputConverter pingJsonOutputConverter = new PingJsonOutputConverter(gson);

    PingEntity pingEntity = new PingEntity();
    pingEntity.setVpi("testVPI");
    pingEntity.setStatus(Status.SUCCESS);
    pingEntity.setStartTimeInMillis(1625097600000L); // 2021-07-01T00:00:00Z
    pingEntity.setStopTimeInMillis(1625097601000L);  // 2021-07-01T00:00:01Z
    pingEntity.setPayloadSent("Hello".getBytes());
    pingEntity.setPayloadReceived("World".getBytes());

    String result = pingJsonOutputConverter.apply(pingEntity);

    assertNotNull(result);
    assertTrue(result.contains("\"vpi\":\"testVPI\""));
    assertTrue(result.contains("\"success\":true"));
    assertTrue(result.contains("\"startTime\":\"2021-07-01T00:00:00Z\""));
    assertTrue(result.contains("\"stopTime\":\"2021-07-01T00:00:01Z\""));
    assertTrue(result.contains("\"payloadSent\":\"Hello\""));
    assertTrue(result.contains("\"payloadReceived\":\"World\""));
  }

  @Test
  void pingJsonOutputConverterConstructorTest() {
    Assertions.assertNotNull(new PingJsonOutputConverter(new Gson()));
  }
}
