package com.volvo.tisp.vehiclepingservice.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.domain.exceptions.BadRequestException;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NotFoundException;

class ExceptionUtilTest {
  @Test
  void isCauseAssignableFromTest() {
    IllegalArgumentException exception = new IllegalArgumentException(new NotFoundException());
    Assertions.assertTrue(ExceptionUtil.isCauseAssignableFrom(exception, NotFoundException.class));
    Assertions.assertFalse(ExceptionUtil.isCauseAssignableFrom(exception, BadRequestException.class));
  }
}
