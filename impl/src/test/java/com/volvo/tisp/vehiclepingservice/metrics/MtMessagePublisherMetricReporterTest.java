package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class MtMessagePublisherMetricReporterTest {
  @Test
  void onMtPublishFailure() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessagePublisherMetricReporter::new, (registry, reporter) -> {
      reporter.onMtPublishFailure();
      MetricsReporterTestUtils.checkCounter(registry, MtMessagePublisherMetricReporter.MT_MESSAGE_PUBLISHER,
          Tags.of(MtMessagePublisherMetricReporter.TYPE, "failure"), 1);
    });
  }

  @Test
  void onMtPublishSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessagePublisherMetricReporter::new, (registry, reporter) -> {
      reporter.onMtPublishSuccess();
      MetricsReporterTestUtils.checkCounter(registry, MtMessagePublisherMetricReporter.MT_MESSAGE_PUBLISHER,
          Tags.of(MtMessagePublisherMetricReporter.TYPE, "success"), 1);
    });
  }
}
