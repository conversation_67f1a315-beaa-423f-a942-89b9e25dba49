package com.volvo.tisp.vehiclepingservice.domain.v1;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Ping;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;

@ExtendWith(MockitoExtension.class)
class ReceiveMoPingSendMtPongStepV1Test {

  @Mock
  private PingCoder pingCoder;
  @InjectMocks
  private ReceiveMoPingSendMtPongStepV1 receiveMoPingSendMtPongStepV1;

  private static MoDtoV1 createMoDtoV1() {
    MoDtoV1 requestDto = new MoDtoV1();
    requestDto.setVpi(TestUtils.VPI);
    DecodedV1 decodedV1 = new DecodedV1();
    Ping ping = new Ping();

    ping.setId(TestUtils.MESSAGE_ID);

    decodedV1.setPing(ping);
    requestDto.setDecoded(decodedV1);
    requestDto.setCorrelationId(TestUtils.CORRELATION_ID);
    return requestDto;
  }

  @Test
  void getPayloadShouldEncodePong() {
    MoDtoV1 moDtoV1 = createMoDtoV1();

    byte[] expectedEncodedPayload = "encoded payload".getBytes();
    Mockito.when(pingCoder.encodePong(TestUtils.MESSAGE_ID)).thenReturn(expectedEncodedPayload);

    byte[] result = receiveMoPingSendMtPongStepV1.getPayload(moDtoV1);

    Mockito.verify(pingCoder).encodePong(TestUtils.MESSAGE_ID);
    Assertions.assertArrayEquals(expectedEncodedPayload, result);
  }

  @Test
  void getPayloadShouldHandleEncodingException() {
    MoDtoV1 moDtoV1 = createMoDtoV1();

    Mockito.when(pingCoder.encodePong(TestUtils.MESSAGE_ID)).thenThrow(new RuntimeException("Encoding failed"));

    Assertions.assertThrows(RuntimeException.class, () -> receiveMoPingSendMtPongStepV1.getPayload(moDtoV1));
    Mockito.verify(pingCoder).encodePong(TestUtils.MESSAGE_ID);
  }
}
