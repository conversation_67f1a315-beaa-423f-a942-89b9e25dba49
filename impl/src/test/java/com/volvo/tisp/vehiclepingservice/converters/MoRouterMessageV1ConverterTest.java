package com.volvo.tisp.vehiclepingservice.converters;

import static org.mockito.ArgumentMatchers.any;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.PingCoder;

@ExtendWith(SpringExtension.class)
class MoRouterMessageV1ConverterTest {
  private static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  @InjectMocks
  private MoRouterMessageV1Converter converter;

  @Mock
  private PingCoder pingCoder;

  private static void assertResult(MoDtoV1 result) {
    Assertions.assertNotNull(result);
    Assertions.assertEquals(VPI, result.getVpi());
    Assertions.assertNotNull(result.getDecoded());
    Assertions.assertEquals(1234L, result.getOnBoardTimeStamp());
    Assertions.assertEquals(5678L, result.getServerTimestamp());
    Assertions.assertEquals("testToken", result.getServiceAccessToken());
    Assertions.assertEquals("testKeyId", result.getKeyId());
  }

  private static com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage getV1MoMessage() {
    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = new com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage();
    moMessage.withVehiclePlatformId(VPI.toString());
    moMessage.withServiceVersion(1);
    moMessage.withServiceId(256);
    moMessage.withPayload(Base64.getEncoder().encodeToString("testPayload".getBytes()));
    moMessage.withOnboardTimestamp(1234L);
    moMessage.withServerTimestamp(5678L);
    moMessage.withServiceAccessToken("testToken");
    moMessage.withKeyId("testKeyId");

    return moMessage;
  }

  @Test
  void convertSuccessfullyTransformsMoMessageTest() {
    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = getV1MoMessage();
    DecodedV1 decodedV1 = new DecodedV1();

    Mockito.when(pingCoder.decode(any(byte[].class))).thenReturn(decodedV1);

    MoDtoV1 result = converter.convert(moMessage);

    assertResult(result);
    Mockito.verify(pingCoder).decode("testPayload".getBytes(StandardCharsets.UTF_8));
    Mockito.verifyNoMoreInteractions(pingCoder);
  }

  @Test
  void moRouterMessageV1ConverterConstructorTest() {
    Assertions.assertNotNull(new MoRouterMessageV1Converter(pingCoder), "The converter should be initialized");
  }
}
