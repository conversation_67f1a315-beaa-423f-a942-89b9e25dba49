package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class ServiceAccessTokenMetricReporterTest {
  @Test
  void onRetrievingNewToken() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onRetrievingNewToken();
      MetricsReporterTestUtils.checkCounter(registry, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of("bostoken", "new"), 1);
    });
  }

  @Test
  void onReuseToken() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onReuseToken();
      MetricsReporterTestUtils.checkCounter(registry, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of("bostoken", "reuse"), 1);
    });
  }

  @Test
  void onTokenFailure() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onTokenFailure();
      MetricsReporterTestUtils.checkCounter(registry, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "failure"), 1);
    });
  }

  @Test
  void onTokenRequest() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onTokenRequest();
      MetricsReporterTestUtils.checkCounter(registry, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "request"), 1);
    });
  }

  @Test
  void onTokenSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onTokenSuccess();
      MetricsReporterTestUtils.checkCounter(registry, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "success"), 1);
    });
  }

  @Test
  void onValidateFailure() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onValidateFailure();
      MetricsReporterTestUtils.checkCounter(registry, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "validate-failure"), 1);
    });
  }

  @Test
  void onValidateSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onValidateSuccess();
      MetricsReporterTestUtils.checkCounter(registry, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "validate-success"), 1);
    });
  }
}