package com.volvo.tisp.vehiclepingservice.domain.v1;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNException;
import com.volvo.tisp.vehiclepingservice.swap.common.PERStream;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessage;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Ping;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Pong;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.TestServicePdu;

class PingCoderTest {

  private final PingCoder pingCoder = new PingCoder();

  @Test
  void decodeWhenReceivingPingMessageShouldReturnDecodedPing() throws ASNException {
    Ping ping = new Ping();

    PERStream perStream = new PERStream();
    TestServicePdu testServicePdu1 = new TestServicePdu();
    testServicePdu1.setPing(ping);
    testServicePdu1.encode(perStream);
    byte[] buffer = perStream.getBuffer();

    DecodedV1 result = pingCoder.decode(buffer);

    assertNotNull(result);
    assertNotNull(result.getPing());
    assertNull(result.getPong());
    assertNull(result.getBeefyMessage());
  }

  @Test
  void decodeWhenReceivingPongMessageShouldReturnDecodedPong() throws ASNException {
    Pong pong = new Pong();

    PERStream perStream = new PERStream();
    TestServicePdu testServicePdu1 = new TestServicePdu();
    testServicePdu1.setPong(pong);
    testServicePdu1.encode(perStream);
    byte[] buffer = perStream.getBuffer();

    DecodedV1 result = pingCoder.decode(buffer);

    assertNotNull(result);
    assertNotNull(result.getPong());
    assertNull(result.getPing());
    assertNull(result.getBeefyMessage());
  }

  @Test
  void decodeWhenReceivingBeefyMessageShouldReturnDecodedBeefyMessage() throws ASNException {
    BeefyMessage beefyMessage = new BeefyMessage();
    beefyMessage.setPayload("payload");

    PERStream perStream = new PERStream();
    TestServicePdu testServicePdu1 = new TestServicePdu();
    testServicePdu1.setBeefyMessage(beefyMessage);
    testServicePdu1.encode(perStream);
    byte[] buffer = perStream.getBuffer();

    DecodedV1 result = pingCoder.decode(buffer);

    assertNotNull(result);
    assertNotNull(result.getBeefyMessage());
    assertNull(result.getPing());
    assertNull(result.getPong());
  }
}
