package com.volvo.tisp.vehiclepingservice.domain.v2;

import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.util.ChecksumCalculator;
import com.volvo.tisp.vps.api.Data;
import com.volvo.tisp.vps.api.PingResponse;
import com.volvo.tisp.vps.api.TestService;

@ExtendWith(MockitoExtension.class)
class ReceivePongStepV2Test {

  @Mock
  private PingEntityService pingEntityService;
  @InjectMocks
  private ReceivePongStepV2 receivePongStep;
  @Mock
  private StateRepository stateRepository;

  private static MoDtoV2 createMoDtoV2(String correlationId) {
    return createMoDtoV2(correlationId, null, null);
  }

  private static MoDtoV2 createMoDtoV2(String correlationId, String payloadValue, String checksum) {
    Data payload = new Data();
    payload.setPayloadValue(payloadValue);
    payload.setChecksum(checksum);

    PingResponse pingResponse = new PingResponse();
    pingResponse.setPayload(payload);
    pingResponse.setCorrelationId(UUID.fromString(correlationId));

    TestService testService = new TestService();
    testService.setPingResponse(pingResponse);

    MoDtoV2 dto = new MoDtoV2();
    dto.setDecoded(testService);
    return dto;
  }

  private static PingEntity createPingEntity(String correlationId, boolean copyPayload) {
    PingEntity pingEntity = new PingEntity();
    pingEntity.setCorrelationId(correlationId);
    pingEntity.setStartTimeInMillis(Instant.now().minusSeconds(1).toEpochMilli());
    pingEntity.setCopyPayload(copyPayload);
    pingEntity.setPayloadSent("payload".getBytes());
    return pingEntity;
  }

  @Test
  void actionWithNullPingEntityTest() {
    String correlationId = UUID.randomUUID().toString();
    MoDtoV2 moDtoV2 = createMoDtoV2(correlationId);

    Mockito.when(pingEntityService.findByCorrelationId(correlationId)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receivePongStep.action(moDtoV2);

    Assertions.assertNotNull(result);
    Mockito.verify(pingEntityService).findByCorrelationId(correlationId);
    Mockito.verifyNoMoreInteractions(pingEntityService, stateRepository);
  }

  @Test
  void actionWithValidPingEntityTest() {
    String correlationId = UUID.randomUUID().toString();
    PingEntity pingEntity = createPingEntity(correlationId, false);
    MoDtoV2 dto = createMoDtoV2(correlationId);

    Mockito.when(pingEntityService.findByCorrelationId(correlationId)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(pingEntity)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(stateRepository.pop(correlationId)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receivePongStep.action(dto);

    Assertions.assertNotNull(result);
    Mockito.verify(pingEntityService).findByCorrelationId(correlationId);
    Mockito.verify(pingEntityService)
        .upsert(Mockito.argThat(argument -> argument.getStatus() == Status.SUCCESS && argument.getStopTimeInMillis() != null && argument.getDuration() > 0));
    Mockito.verify(stateRepository).pop(correlationId);
    Mockito.verifyNoMoreInteractions(pingEntityService, stateRepository);
  }

  @Test
  void actionWithMissingPayloadPingEntityTest() {
    String correlationId = UUID.randomUUID().toString();
    PingEntity pingEntity = createPingEntity(correlationId, true);
    MoDtoV2 dto = createMoDtoV2(correlationId);

    Mockito.when(pingEntityService.findByCorrelationId(correlationId)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(pingEntity)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(stateRepository.pop(correlationId)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receivePongStep.action(dto);

    Assertions.assertNotNull(result);
    Mockito.verify(pingEntityService).findByCorrelationId(correlationId);
    Mockito.verify(pingEntityService).upsert(
        Mockito.argThat(argument -> argument.getStatus() == Status.ERROR
            && argument.getError().equals("Missing Payload")
            && argument.getStopTimeInMillis() != null
            && argument.getDuration() > 0));
    Mockito.verify(stateRepository).pop(correlationId);
    Mockito.verifyNoMoreInteractions(pingEntityService, stateRepository);
  }

  @Test
  void actionWithPayloadChecksumNotMatchTest() {
    String correlationId = UUID.randomUUID().toString();
    PingEntity pingEntity = createPingEntity(correlationId, true);
    MoDtoV2 dto = createMoDtoV2(correlationId, "payload", "checksum");

    Mockito.when(pingEntityService.findByCorrelationId(correlationId)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(pingEntity)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(stateRepository.pop(correlationId)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receivePongStep.action(dto);

    Assertions.assertNotNull(result);
    Mockito.verify(pingEntityService).findByCorrelationId(correlationId);
    Mockito.verify(pingEntityService).upsert(
        Mockito.argThat(argument -> argument.getStatus() == Status.ERROR
            && argument.getError().equals("Payload and checksum does not match")
            && argument.getStopTimeInMillis() != null
            && argument.getDuration() > 0));
    Mockito.verify(stateRepository).pop(correlationId);
    Mockito.verifyNoMoreInteractions(pingEntityService, stateRepository);
  }

  @Test
  void actionWithPayloadNotMatchTest() {
    String correlationId = UUID.randomUUID().toString();
    PingEntity pingEntity = createPingEntity(correlationId, true);
    String payload = "payload1";
    MoDtoV2 dto = createMoDtoV2(correlationId, payload, ChecksumCalculator.getSHA256Hash(payload));

    Mockito.when(pingEntityService.findByCorrelationId(correlationId)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(pingEntity)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(stateRepository.pop(correlationId)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receivePongStep.action(dto);

    Assertions.assertNotNull(result);
    Mockito.verify(pingEntityService).findByCorrelationId(correlationId);
    Mockito.verify(pingEntityService).upsert(
        Mockito.argThat(argument -> argument.getStatus() == Status.ERROR
            && argument.getError().equals("Mismatched Payload")
            && argument.getStopTimeInMillis() != null
            && argument.getDuration() > 0));
    Mockito.verify(stateRepository).pop(correlationId);
    Mockito.verifyNoMoreInteractions(pingEntityService, stateRepository);
  }
}
