package com.volvo.tisp.vehiclepingservice.converters;

import static org.mockito.Mockito.mock;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.domain.v2.PingV2Coder;
import com.volvo.tisp.vps.api.TestService;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.Property;
import com.wirelesscar.tce.api.v2.SrpOption;

public class TceMoMessageV2ConverterTest {
  private static final String HANDLE = "someHandle";
  private static final String PAYLOAD = "testPayload";
  private static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  public static MoMessage createMoMessage(int serviceVersion) {
    SrpOption srpOption = new SrpOption();
    srpOption.setDstService(256);
    srpOption.setDstVersion(serviceVersion);

    MoMessage moMessage = new MoMessage();
    moMessage.setPayload(PAYLOAD.getBytes());
    moMessage.setVehiclePlatformId(VPI.toString());
    moMessage.setHandle(HANDLE);
    moMessage.setSrpOption(srpOption);
    Property property = new Property();
    property.setKey("TransportType");
    property.setValue("UDP");
    moMessage.getSolutionSpecificProperties().add(property);

    return moMessage;
  }

  @Test
  void convertWithValidMoMessageAndReceivingChannelTest() {
    PingV2Coder mockPingV2Coder = mock(PingV2Coder.class);
    TceMoMessageV2Converter converter = new TceMoMessageV2Converter(mockPingV2Coder);

    MoMessage moMessage = createMoMessage(256);
    TestService mockTestService = Mockito.mock(TestService.class);
    Mockito.when(mockPingV2Coder.decode(PAYLOAD.getBytes())).thenReturn(mockTestService);

    MoDtoV2 result = converter.convert(moMessage);

    Assertions.assertNotNull(result);
    Assertions.assertEquals(VPI, result.getVpi());
    Assertions.assertSame(mockTestService, result.getDecoded());
    Assertions.assertEquals("UDP", result.getReceivingChannel());
  }

  @Test
  void tceMoMessageV2Converter_constructorTest() {
    Assertions.assertNotNull(new TceMoMessageV2Converter(Mockito.mock(PingV2Coder.class)));
  }
}
