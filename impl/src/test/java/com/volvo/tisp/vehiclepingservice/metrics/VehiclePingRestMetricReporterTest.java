package com.volvo.tisp.vehiclepingservice.metrics;

import java.util.Locale;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class VehiclePingRestMetricReporterTest {
  @Test
  void onDataFailure() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onDataFailure();
      MetricsReporterTestUtils.checkCounter(registry, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.DATA_REQUEST, VehiclePingRestMetricReporter.TYPE, "failure"), 1);
    });
  }

  @Test
  void onDataNotFound() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onDataNotFound();
      MetricsReporterTestUtils.checkCounter(registry, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.DATA_REQUEST, VehiclePingRestMetricReporter.TYPE, "not-found"), 1);
    });
  }

  @Test
  void onDataRequest() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onDataRequest();
      MetricsReporterTestUtils.checkCounter(registry, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.DATA_REQUEST, VehiclePingRestMetricReporter.TYPE, "request"), 1);
    });
  }

  @Test
  void onDataSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onDataSuccess();
      MetricsReporterTestUtils.checkCounter(registry, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.DATA_REQUEST, VehiclePingRestMetricReporter.TYPE, "success"), 1);
    });
  }

  @Test
  void onPingBadRequest() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onPingBadRequest();
      MetricsReporterTestUtils.checkCounter(registry, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.PING_TO_VEHICLE, VehiclePingRestMetricReporter.TYPE, "bad-request"), 1);
    });
  }

  @Test
  void onPingFailure() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onPingFailure();
      MetricsReporterTestUtils.checkCounter(registry, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.PING_TO_VEHICLE, VehiclePingRestMetricReporter.TYPE, "failure"), 1);
    });
  }

  @Test
  void onPingNotFound() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onPingNotFound();
      MetricsReporterTestUtils.checkCounter(registry, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.PING_TO_VEHICLE, VehiclePingRestMetricReporter.TYPE, "not-found"), 1);
    });
  }

  @Test
  void onPingRequest() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      Channel channel = Channel.UDP;
      reporter.onPingRequest(channel);
      MetricsReporterTestUtils.checkCounter(registry, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of("request.channel", channel.getValue().toLowerCase(Locale.ROOT)), 1);
    });
  }

  @Test
  void onPingSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onPingSuccess();
      MetricsReporterTestUtils.checkCounter(registry, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.PING_TO_VEHICLE, VehiclePingRestMetricReporter.TYPE, "success"), 1);
    });
  }
}