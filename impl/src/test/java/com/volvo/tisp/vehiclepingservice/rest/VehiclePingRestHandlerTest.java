package com.volvo.tisp.vehiclepingservice.rest;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;

import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.vehiclepingservice.converters.PingJsonOutputConverter;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.BadRequestException;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NotFoundException;
import com.volvo.tisp.vehiclepingservice.metrics.LegacyMetricsReporter;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;

@ExtendWith(SpringExtension.class)
public class VehiclePingRestHandlerTest {
  @Mock
  private PingJsonOutputConverter jsonOutputConverter;

  @Mock
  private LegacyMetricsReporter legacyMetricsReporter;

  @Mock
  private PingManager pingManager;
  @InjectMocks
  private VehiclePingRestHandler vehiclePingRestHandler;

  @Test
  void getDataFromCorrelationIdEmptyCorrelationIdTest() {
    PingEntity pingEntity = new PingEntity();
    Mockito.when(pingManager.getData("")).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(jsonOutputConverter.apply(pingEntity)).thenReturn("");

    CompletableFuture<String> result = vehiclePingRestHandler.getDataFromCorrelationId("");

    Assertions.assertNotNull(result);
    Assertions.assertEquals("", result.join());
    Mockito.verify(jsonOutputConverter).apply(pingEntity);
    Mockito.verify(pingManager).getData(Mockito.anyString());
    Mockito.verify(legacyMetricsReporter).legacyGetDataRequest();
    Mockito.verify(legacyMetricsReporter).legacyGetDataSuccess();
    Mockito.verifyNoMoreInteractions(jsonOutputConverter, pingManager, legacyMetricsReporter);
  }

  @Test
  void getDataFromCorrelationIdGeneralExceptionTest() {
    String correlationId = "errorCorrelationId";
    CompletableFuture<PingEntity> failedFuture = CompletableFuture.failedFuture(new RuntimeException("General error"));

    Mockito.when(pingManager.getData(correlationId)).thenReturn(failedFuture);

    CompletableFuture<String> result = vehiclePingRestHandler.getDataFromCorrelationId(correlationId);

    Assertions.assertNotNull(result);
    Assertions.assertThrows(RuntimeException.class, result::join);

    Mockito.verify(legacyMetricsReporter).legacyGetDataRequest();
    Mockito.verify(legacyMetricsReporter).legacyGetDataFailure();
    Mockito.verify(pingManager).getData(correlationId);
    Mockito.verifyNoMoreInteractions(jsonOutputConverter, pingManager, legacyMetricsReporter);
  }

  @Test
  void getDataFromCorrelationIdNotFoundExceptionTest() {
    String correlationId = "notFoundCorrelationId";
    CompletableFuture<PingEntity> failedFuture = CompletableFuture.failedFuture(new NotFoundException("Data not found"));

    Mockito.when(pingManager.getData(correlationId)).thenReturn(failedFuture);

    CompletableFuture<String> result = vehiclePingRestHandler.getDataFromCorrelationId(correlationId);

    Assertions.assertNotNull(result);
    Assertions.assertThrows(RuntimeException.class, result::join);

    Mockito.verify(legacyMetricsReporter).legacyGetDataRequest();
    Mockito.verify(legacyMetricsReporter).legacyGetDataNotFound();
    Mockito.verify(pingManager).getData(correlationId);
    Mockito.verifyNoMoreInteractions(jsonOutputConverter, pingManager, legacyMetricsReporter);
  }

  @Test
  void getDataFromCorrelationIdReturnsExpectedCompletableFutureTest() {
    String correlationId = "testCorrelationId";
    String convertedData = "convertedData";
    PingEntity pingEntity = new PingEntity();
    Mockito.when(pingManager.getData(correlationId)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(jsonOutputConverter.apply(pingEntity)).thenReturn(convertedData);

    CompletableFuture<String> result = vehiclePingRestHandler.getDataFromCorrelationId(correlationId);

    Mockito.verify(legacyMetricsReporter).legacyGetDataRequest();
    Mockito.verify(legacyMetricsReporter).legacyGetDataSuccess();
    Mockito.verify(pingManager).getData(correlationId);
    Mockito.verify(jsonOutputConverter).apply(pingEntity);

    Assertions.assertEquals(convertedData, result.join());
    Mockito.verifyNoMoreInteractions(jsonOutputConverter, pingManager, legacyMetricsReporter);
  }

  @Test
  void requestPingBadRequestExceptionTest() {
    String vpi = "invalidVpi";
    RequestData expectedRequestData = new RequestData(vpi, false, null, Channel.UDP, 30);
    CompletableFuture<String> failedFuture = CompletableFuture.failedFuture(new BadRequestException("Bad request"));

    Mockito.when(pingManager.pingRest(expectedRequestData)).thenReturn(failedFuture);

    CompletableFuture<String> result = vehiclePingRestHandler.requestPing(vpi);

    Assertions.assertNotNull(result);
    Assertions.assertThrows(RuntimeException.class, result::join);

    Mockito.verify(legacyMetricsReporter).legacyPingRequest();
    Mockito.verify(legacyMetricsReporter).legacyPingBadRequest();
    Mockito.verify(pingManager).pingRest(expectedRequestData);
    Mockito.verifyNoMoreInteractions(pingManager, legacyMetricsReporter);
  }

  @Test
  void requestPingEmptyVpiTest() {
    String emptyVpi = "   ";
    RequestData expectedRequestData = new RequestData("", false, null, Channel.UDP, 30);

    Mockito.when(pingManager.pingRest(any(RequestData.class))).thenReturn(CompletableFuture.completedFuture("success"));

    CompletableFuture<String> result = vehiclePingRestHandler.requestPing(emptyVpi);

    Mockito.verify(legacyMetricsReporter).legacyPingRequest();
    Mockito.verify(legacyMetricsReporter).legacyPingSuccess();
    Mockito.verify(pingManager).pingRest(eq(expectedRequestData));

    Assertions.assertNotNull(result);
    Assertions.assertEquals("success", result.join());
  }

  @Test
  void requestPingGeneralExceptionTest() {
    String vpi = "errorVpi";
    RequestData expectedRequestData = new RequestData(vpi, false, null, Channel.UDP, 30);
    CompletableFuture<String> failedFuture = CompletableFuture.failedFuture(new RuntimeException("General error"));

    Mockito.when(pingManager.pingRest(expectedRequestData)).thenReturn(failedFuture);

    CompletableFuture<String> result = vehiclePingRestHandler.requestPing(vpi);

    Assertions.assertNotNull(result);
    Assertions.assertThrows(RuntimeException.class, result::join);

    Mockito.verify(legacyMetricsReporter).legacyPingRequest();
    Mockito.verify(legacyMetricsReporter).legacyPingFailure();
    Mockito.verify(pingManager).pingRest(expectedRequestData);
    Mockito.verifyNoMoreInteractions(pingManager, legacyMetricsReporter);
  }

  @Test
  void requestPingNotFoundExceptionTest() {
    String vpi = "notFoundVpi";
    RequestData expectedRequestData = new RequestData(vpi, false, null, Channel.UDP, 30);
    CompletableFuture<String> failedFuture = CompletableFuture.failedFuture(new NotFoundException("Vehicle not found"));

    Mockito.when(pingManager.pingRest(expectedRequestData)).thenReturn(failedFuture);

    CompletableFuture<String> result = vehiclePingRestHandler.requestPing(vpi);

    Assertions.assertNotNull(result);
    Assertions.assertThrows(RuntimeException.class, result::join);

    Mockito.verify(legacyMetricsReporter).legacyPingRequest();
    Mockito.verify(legacyMetricsReporter).legacyPingNotFound();
    Mockito.verify(pingManager).pingRest(expectedRequestData);
    Mockito.verifyNoMoreInteractions(pingManager, legacyMetricsReporter);
  }

  @Test
  void requestPingValidVpiTest() {
    String vpi = " ABC123 ";
    String expectedTrimmedVpi = "ABC123";
    RequestData expectedRequestData = new RequestData(expectedTrimmedVpi, false, null, Channel.UDP, 30);
    CompletableFuture<String> expectedResult = CompletableFuture.completedFuture("Ping successful");

    Mockito.when(pingManager.pingRest(any(RequestData.class))).thenReturn(expectedResult);

    CompletableFuture<String> result = vehiclePingRestHandler.requestPing(vpi);

    Mockito.verify(legacyMetricsReporter).legacyPingRequest();
    Mockito.verify(legacyMetricsReporter).legacyPingSuccess();
    Mockito.verify(pingManager).pingRest(expectedRequestData);
    Assertions.assertEquals(expectedResult, result);
  }

  @Test
  void vehiclePingRestHandlerConstructorTest() {
    Assertions.assertNotNull(new VehiclePingRestHandler(jsonOutputConverter, pingManager, legacyMetricsReporter));
  }
}
