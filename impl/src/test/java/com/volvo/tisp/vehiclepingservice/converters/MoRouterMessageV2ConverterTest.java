package com.volvo.tisp.vehiclepingservice.converters;

import static org.mockito.Mockito.any;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.domain.v2.PingV2Coder;
import com.volvo.tisp.vps.api.TestService;

@ExtendWith(SpringExtension.class)
class MoRouterMessageV2ConverterTest {
  private static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  @InjectMocks
  private MoRouterMessageV2Converter moRouterMessageV2Converter;
  @Mock
  private PingV2Coder pingV2Coder;

  private static MoMessage createV1MoMessage() {
    MoMessage moMessage = new MoMessage();
    moMessage.withServiceAccessToken("testToken");
    moMessage.withVehiclePlatformId(VPI.toString());
    moMessage.withServiceVersion(1);
    moMessage.withServiceId(256);
    moMessage.withPayload("dGVzdFBheWxvYWQ=");//base 64 encoded character of string "testPayload"
    moMessage.withOnboardTimestamp(1234567890L);
    moMessage.withServerTimestamp(9876543210L);
    moMessage.withKeyId("testKeyId");
    return moMessage;
  }

  private static void verifyMoMessage(MoDtoV2 result, TestService mockDecoded) {
    Assertions.assertNotNull(result);
    Assertions.assertEquals(VPI, result.getVpi());
    Assertions.assertEquals(mockDecoded, result.getDecoded());
    Assertions.assertEquals(1234567890L, result.getOnBoardTimeStamp());
    Assertions.assertEquals(9876543210L, result.getServerTimestamp());
    Assertions.assertEquals("testToken", result.getServiceAccessToken());
    Assertions.assertEquals("testKeyId", result.getKeyId());
  }

  @Test
  void convertSuccessfulConversionTest() {
    MoMessage moMessage = createV1MoMessage();

    TestService mockDecoded = Mockito.mock(TestService.class);
    Mockito.when(pingV2Coder.decode(any(byte[].class))).thenReturn(mockDecoded);

    MoDtoV2 result = moRouterMessageV2Converter.convert(moMessage);

    verifyMoMessage(result, mockDecoded);

    Mockito.verify(pingV2Coder).decode("testPayload".getBytes());
    Mockito.verifyNoMoreInteractions(pingV2Coder, mockDecoded);
  }

  @Test
  void convertWithInvalidBase64PayloadTest() {
    MoMessage mockMessage = Mockito.mock(MoMessage.class);
    Mockito.when(mockMessage.getPayload()).thenReturn("Invalid Base64 String");

    Assertions.assertThrows(IllegalArgumentException.class, () -> moRouterMessageV2Converter.convert(mockMessage));
    Mockito.verifyNoMoreInteractions(pingV2Coder);
  }
}
