package com.volvo.tisp.vehiclepingservice.domain;

import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ResponseStatusException;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.database.repository.MessageIdRepo;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.BadRequestException;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NotFoundException;
import com.volvo.tisp.vehiclepingservice.domain.v1.ReceiveBeefyResponseStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.ReceiveMoBeefySendMtBeefyResponseStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.ReceiveMoPingSendMtPongStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.ReceivePongStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.SendMtBeefyStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.SendMtPingStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.domain.v2.ReceiveMoPingSendMtPongStepV2;
import com.volvo.tisp.vehiclepingservice.domain.v2.ReceivePongStepV2;
import com.volvo.tisp.vehiclepingservice.domain.v2.SendMtPingStepV2;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.reporter.PingManagerMetricReporter;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessage;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessageResponse;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Ping;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Pong;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;
import com.volvo.tisp.vps.api.PingRequest;
import com.volvo.tisp.vps.api.PingResponse;
import com.volvo.tisp.vps.api.TestService;
import com.wirelesscar.telematicunitservice.TelematicUnitService;
import com.wirelesscar.telematicunitservice.telematicunit._1_0.TelematicUnit;

@ExtendWith(MockitoExtension.class)
class PingManagerTest {

  @Mock
  private MessageIdRepo messageIdRepo;
  @Mock
  private PingEntityService pingEntityService;
  @InjectMocks
  private PingManager pingManager;
  @Mock
  private PingManagerMetricReporter pingManagerMetricReporter;
  @Mock
  private ReceiveBeefyResponseStepV1 receiveBeefyResponseStepV1;
  @Mock
  private ReceiveMoBeefySendMtBeefyResponseStepV1 receiveMoBeefySendMtBeefyResponseStepV1;
  @Mock
  private ReceiveMoPingSendMtPongStepV1 receiveMoPingSendMtPongStepV1;
  @Mock
  private ReceiveMoPingSendMtPongStepV2 receiveMoPingSendMtPongStepV2;
  @Mock
  private ReceivePongStepV1 receivePongStepV1;
  @Mock
  private ReceivePongStepV2 receivePongStepV2;
  @Mock
  private SendMtBeefyStepV1 sendMtBeefyStepV1;
  @Mock
  private SendMtPingStepV1 sendMtPingStepV1;
  @Mock
  private SendMtPingStepV2 sendMtPingStepV2;
  @Mock
  private StateRepository stateRepository;
  @Mock
  private TelematicUnitService telematicUnitService;

  @Test
  void getDataWithValidCorrelationIdTest() {
    String correlationId = "testCorrelationId";
    PingEntity expectedEntity = new PingEntity();

    Mockito.when(pingEntityService.findByCorrelationId(correlationId)).thenReturn(CompletableFuture.completedFuture(expectedEntity));

    CompletableFuture<PingEntity> result = pingManager.getData(correlationId);

    Assertions.assertNotNull(result);
    result.thenAccept(entity -> Assertions.assertEquals(expectedEntity, entity));
    Mockito.verify(pingEntityService).findByCorrelationId(correlationId);
    Mockito.verifyNoMoreInteractions(pingEntityService);
  }

  @Test
  void pingRestV1WithEmptyDataTest() {
    long swapCorrelationId = 112231L;
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), true, "", Channel.UDP, 0);
    TelematicUnit telematicUnit = new TelematicUnit();
    telematicUnit.setVersion(1L);
    telematicUnit.setSystem("testSystem");

    Mockito.when(telematicUnitService.getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID)))
        .thenReturn(CompletableFuture.completedFuture(telematicUnit));
    Mockito.when(messageIdRepo.getNextMessageIdSequence()).thenReturn(CompletableFuture.completedFuture(swapCorrelationId));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.completedFuture(new PingEntity()));
    Mockito.when(
            sendMtPingStepV1.action(Mockito.eq(requestData), Mockito.any(UUID.class), Mockito.eq(swapCorrelationId), Mockito.eq(telematicUnit.getSystem())))
        .thenReturn(CompletableFuture.completedFuture(null));

    String correlationId = pingManager.pingRest(requestData).join();

    Mockito.verify(telematicUnitService).getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID));
    Mockito.verify(pingEntityService).upsert(
        Mockito.argThat(argument -> argument.getStatus() == Status.PENDING
            && argument.getCorrelationId().equals(correlationId)
            && argument.getStartTimeInMillis() != null
            && argument.getServiceVersion() == 1
            && Arrays.equals(argument.getPayloadSent(), requestData.body().getBytes())
            && argument.getMessageId().equals(swapCorrelationId)
            && argument.getCopyPayload() == requestData.copyPayload()
            && argument.getVpi().equals(requestData.vpi())));
    Mockito.verify(sendMtPingStepV1).action(requestData, UUID.fromString(correlationId), swapCorrelationId, telematicUnit.getSystem());
    Mockito.verifyNoMoreInteractions(telematicUnitService, pingEntityService, sendMtBeefyStepV1);
  }

  @Test
  void pingRestV1WithErrorTest() {
    long swapCorrelationId = 112231L;
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), true, "", Channel.UDP, 0);
    TelematicUnit telematicUnit = new TelematicUnit();
    telematicUnit.setVersion(1L);
    telematicUnit.setSystem("testSystem");
    RuntimeException runtimeException = new RuntimeException("Error");

    Mockito.when(telematicUnitService.getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID)))
        .thenReturn(CompletableFuture.completedFuture(telematicUnit));
    Mockito.when(messageIdRepo.getNextMessageIdSequence()).thenReturn(CompletableFuture.completedFuture(swapCorrelationId));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.completedFuture(new PingEntity()));
    Mockito.when(
            sendMtPingStepV1.action(Mockito.eq(requestData), Mockito.any(UUID.class), Mockito.eq(swapCorrelationId), Mockito.eq(telematicUnit.getSystem())))
        .thenReturn(CompletableFuture.failedFuture(runtimeException));
    Mockito.when(pingEntityService.findByMessageId(swapCorrelationId)).thenReturn(CompletableFuture.completedFuture(new PingEntity()));

    pingManager.pingRest(requestData).exceptionally(throwable -> {
      Assertions.assertInstanceOf(ResponseStatusException.class, throwable.getCause());
      return null;
    }).join();

    Mockito.verify(telematicUnitService).getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID));
    ArgumentCaptor<PingEntity> pingEntityArgumentCaptor = ArgumentCaptor.forClass(PingEntity.class);
    Mockito.verify(pingEntityService, Mockito.times(2)).upsert(pingEntityArgumentCaptor.capture());
    PingEntity pingEntity = pingEntityArgumentCaptor.getValue();
    Assertions.assertEquals(Status.CANCELLED, pingEntity.getStatus());
    Assertions.assertEquals(runtimeException.toString(), pingEntity.getError());
    Mockito.verify(sendMtPingStepV1).action(Mockito.eq(requestData), Mockito.any(), Mockito.eq(swapCorrelationId), Mockito.eq(telematicUnit.getSystem()));
    Mockito.verifyNoMoreInteractions(telematicUnitService, pingEntityService, sendMtBeefyStepV1);
  }

  @Test
  void pingRestV1WithValidRequestDataTest() {
    long swapCorrelationId = 112231L;
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), true, "body", Channel.UDP, 0);
    TelematicUnit telematicUnit = new TelematicUnit();
    telematicUnit.setVersion(1L);
    telematicUnit.setSystem("testSystem");

    Mockito.when(telematicUnitService.getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID)))
        .thenReturn(CompletableFuture.completedFuture(telematicUnit));
    Mockito.when(messageIdRepo.getNextMessageIdSequence()).thenReturn(CompletableFuture.completedFuture(swapCorrelationId));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.completedFuture(new PingEntity()));
    Mockito.when(
            sendMtBeefyStepV1.action(Mockito.eq(requestData), Mockito.any(UUID.class), Mockito.eq(swapCorrelationId), Mockito.eq(telematicUnit.getSystem())))
        .thenReturn(CompletableFuture.completedFuture(null));

    String correlationId = pingManager.pingRest(requestData).join();

    Mockito.verify(telematicUnitService).getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID));
    Mockito.verify(pingEntityService).upsert(
        Mockito.argThat(argument -> argument.getStatus() == Status.PENDING
            && argument.getCorrelationId().equals(correlationId)
            && argument.getStartTimeInMillis() != null
            && argument.getServiceVersion() == 1
            && Arrays.equals(argument.getPayloadSent(), requestData.body().getBytes())
            && argument.getMessageId().equals(swapCorrelationId)
            && argument.getCopyPayload() == requestData.copyPayload()
            && argument.getVpi().equals(requestData.vpi())));
    Mockito.verify(sendMtBeefyStepV1).action(requestData, UUID.fromString(correlationId), swapCorrelationId, telematicUnit.getSystem());
    Mockito.verifyNoMoreInteractions(telematicUnitService, pingEntityService, sendMtBeefyStepV1);
  }

  @Test
  void pingRestV2WithValidRequestDataTest() {
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), true, "body", Channel.UDP, 0);

    TelematicUnit telematicUnit = new TelematicUnit();
    telematicUnit.setVersion(2L);
    telematicUnit.setSystem("testSystem");
    Mockito.when(telematicUnitService.getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID)))
        .thenReturn(CompletableFuture.completedFuture(telematicUnit));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.completedFuture(new PingEntity()));
    Mockito.when(sendMtPingStepV2.action(Mockito.eq(requestData), Mockito.any(UUID.class), Mockito.eq(telematicUnit.getSystem())))
        .thenReturn(CompletableFuture.completedFuture(null));

    String correlationId = pingManager.pingRest(requestData).join();

    Mockito.verify(telematicUnitService).getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID));
    Mockito.verify(pingEntityService).upsert(
        Mockito.argThat(argument -> argument.getStatus() == Status.PENDING
            && argument.getCorrelationId().equals(correlationId)
            && argument.getStartTimeInMillis() != null
            && argument.getServiceVersion() == 2
            && Arrays.equals(argument.getPayloadSent(), requestData.body().getBytes())
            && argument.getMessageId() == null
            && argument.getCopyPayload() == requestData.copyPayload()
            && argument.getVpi().equals(requestData.vpi())));
    Mockito.verify(sendMtPingStepV2).action(requestData, UUID.fromString(correlationId), telematicUnit.getSystem());
    Mockito.verifyNoMoreInteractions(telematicUnitService, pingEntityService, sendMtPingStepV2);
  }

  @Test
  void pingRestWithBadRequestExceptionTest() {
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), true, "body", Channel.UDP, 0);

    Mockito.when(telematicUnitService.getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID)))
        .thenReturn(CompletableFuture.failedFuture(new BadRequestException()));

    CompletableFuture<String> badRequestResult = pingManager.pingRest(requestData);

    Assertions.assertNotNull(badRequestResult);
    badRequestResult.exceptionally(throwable -> {
      Assertions.assertInstanceOf(BadRequestException.class, throwable.getCause());
      return null;
    }).join();

    Mockito.when(telematicUnitService.getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID)))
        .thenReturn(CompletableFuture.failedFuture(new RuntimeException(new BadRequestException())));

    CompletableFuture<String> badRequestCauseResult = pingManager.pingRest(requestData);

    badRequestCauseResult.exceptionally(throwable -> {
      Assertions.assertInstanceOf(BadRequestException.class, throwable.getCause());
      return null;
    }).join();

    Mockito.when(telematicUnitService.getVehicleServiceVersion(requestData.vpi(), Integer.toString(PingManager.SWAP_SERVICE_ID)))
        .thenReturn(CompletableFuture.failedFuture(WebClientResponseException.create(404, "", null, null, null)));

    pingManager.pingRest(requestData).exceptionally(throwable -> {
      Assertions.assertInstanceOf(NotFoundException.class, throwable.getCause());
      return null;
    }).join();
  }

  @Test
  void processBeefyResponseV1Test() {
    DecodedV1 decoded = new DecodedV1();
    decoded.setBeefyMessageResponse(new BeefyMessageResponse());
    MoDtoV1 dto = new MoDtoV1();
    dto.setDecoded(decoded);

    Mockito.when(receiveBeefyResponseStepV1.action(dto)).thenReturn(CompletableFuture.completedFuture(null));

    pingManager.processMoMessageV1(dto);

    Mockito.verify(pingManagerMetricReporter).onBeefyResponseFromVehicle();
    Mockito.verify(receiveBeefyResponseStepV1).action(dto);
    Mockito.verifyNoMoreInteractions(pingManagerMetricReporter, receiveBeefyResponseStepV1);
  }

  @ParameterizedTest
  @CsvSource({
      "TIMEOUT, TIMEOUT",
      "TIMEOUT, TIMEOUT",
      "OVERRIDDEN, OVERRIDDEN",
      "CANCELLED, CANCELED",
      "ERROR, FAILED",
      "ERROR, INVALID_TOKEN",
      "ERROR, UNSUPPORTED_VERSION"
  })
  void processBeefyResponseV1Test(String expectedStatus, String messageStatus) {
    String correlationId = UUID.randomUUID().toString();
    PingEntity pingEntity = new PingEntity();
    pingEntity.setCorrelationId(correlationId);
    pingEntity.setStatus(Status.PENDING);

    Mockito.when(pingEntityService.findByCorrelationId(correlationId)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(stateRepository.pop(Mockito.anyString())).thenReturn(CompletableFuture.completedFuture(null));

    pingManager.processMtStatus(correlationId, com.volvo.tisp.vc.mt.message.client.json.v1.Status.valueOf(messageStatus));

    Mockito.verify(pingEntityService).findByCorrelationId(correlationId);
    Mockito.verify(stateRepository).pop(Mockito.anyString());
    Mockito.verify(pingEntityService).upsert(Mockito.argThat(argument -> {
      Assertions.assertEquals(messageStatus, argument.getMtStatus());
      Assertions.assertEquals(Status.valueOf(expectedStatus), argument.getStatus());
      return true;
    }));
    Mockito.verifyNoMoreInteractions(pingManagerMetricReporter, stateRepository, pingEntityService);
  }

  @Test
  void processBeefyV1Test() {
    DecodedV1 decoded = new DecodedV1();
    decoded.setBeefyMessage(new BeefyMessage());
    MoDtoV1 dto = new MoDtoV1();
    dto.setDecoded(decoded);

    Mockito.when(receiveMoBeefySendMtBeefyResponseStepV1.action(dto)).thenReturn(CompletableFuture.completedFuture(null));

    pingManager.processMoMessageV1(dto);

    Mockito.verify(pingManagerMetricReporter).onBeefyFromVehicle();
    Mockito.verify(receiveMoBeefySendMtBeefyResponseStepV1).action(dto);
    Mockito.verifyNoMoreInteractions(pingManagerMetricReporter, receiveMoBeefySendMtBeefyResponseStepV1);
  }

  @Test
  void processIncorrectMessageV1Test() {
    MoDtoV1 dto = new MoDtoV1();
    dto.setDecoded(new DecodedV1());

    Assertions.assertThrows(BadRequestException.class, () -> pingManager.processMoMessageV1(dto));

    Mockito.verifyNoInteractions(pingManagerMetricReporter);
  }

  @Test
  void processPingV1Test() {
    DecodedV1 decoded = new DecodedV1();
    decoded.setPing(new Ping());
    MoDtoV1 dto = new MoDtoV1();
    dto.setDecoded(decoded);

    Mockito.when(receiveMoPingSendMtPongStepV1.action(dto)).thenReturn(CompletableFuture.completedFuture(null));

    pingManager.processMoMessageV1(dto);

    Mockito.verify(pingManagerMetricReporter).onPingFromVehicleV1();
    Mockito.verify(receiveMoPingSendMtPongStepV1).action(dto);
    Mockito.verifyNoMoreInteractions(pingManagerMetricReporter, receiveMoPingSendMtPongStepV1);
  }

  @Test
  void processPingV2Test() {
    TestService testService = new TestService();
    testService.setPingRequest(new PingRequest());
    MoDtoV2 dto = new MoDtoV2();
    dto.setDecoded(testService);

    Mockito.when(receiveMoPingSendMtPongStepV2.action(dto)).thenReturn(CompletableFuture.completedFuture(null));

    pingManager.processMoMessageV2(dto);

    Mockito.verify(pingManagerMetricReporter).onPingFromVehicleV2();
    Mockito.verify(receiveMoPingSendMtPongStepV2).action(dto);
    Mockito.verifyNoMoreInteractions(pingManagerMetricReporter, receiveMoPingSendMtPongStepV2);
  }

  @Test
  void processPongV1Test() {
    Pong pong = new Pong();
    pong.setId(1);
    DecodedV1 decoded = new DecodedV1();
    decoded.setPong(pong);
    MoDtoV1 dto = new MoDtoV1();
    dto.setVpi(TestUtils.VPI);
    dto.setDecoded(decoded);

    Mockito.when(receivePongStepV1.action(pong.getId(), dto.getVpi())).thenReturn(CompletableFuture.completedFuture(null));

    pingManager.processMoMessageV1(dto);

    Mockito.verify(pingManagerMetricReporter).onPongFromVehicleV1();
    Mockito.verify(receivePongStepV1).action(pong.getId(), dto.getVpi());
    Mockito.verifyNoMoreInteractions(pingManagerMetricReporter, receivePongStepV1);
  }

  @Test
  void processPongV2Test() {
    TestService testService = new TestService();
    testService.setPingResponse(new PingResponse());
    MoDtoV2 dto = new MoDtoV2();
    dto.setDecoded(testService);

    Mockito.when(receivePongStepV2.action(dto)).thenReturn(CompletableFuture.completedFuture(null));

    pingManager.processMoMessageV2(dto);

    Mockito.verify(pingManagerMetricReporter).onPongFromVehicleV2();
    Mockito.verify(receivePongStepV2).action(dto);
    Mockito.verifyNoMoreInteractions(pingManagerMetricReporter, receivePongStepV2);
  }
}
