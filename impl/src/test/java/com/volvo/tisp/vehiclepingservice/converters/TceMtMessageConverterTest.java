package com.volvo.tisp.vehiclepingservice.converters;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;
import com.wirelesscar.tce.api.v2.MtMessage;

public class TceMtMessageConverterTest {
  private static void assertMtMessage(MtMessage result, String payload, String vpi, String sendSchemaHint, int correlationIdLength) {
    Assertions.assertNotNull(result.getMtStatusReplyOption().getCorrelationId());
    Assertions.assertEquals(correlationIdLength, result.getMtStatusReplyOption().getCorrelationId().length()); // UUID length
    Assertions.assertNotNull(result);
    Assertions.assertEquals(payload, new String(result.getPayload()));
    Assertions.assertEquals(vpi, result.getVehiclePlatformId());
    Assertions.assertNotNull(result.getMtStatusReplyOption().getCorrelationId());
    Assertions.assertEquals(sendSchemaHint, result.getSchedulerOption().getHint());
  }

  @Test
  void createMtMessageNullChannelLongTimeoutTest() {

    MtMessage result = TceMtMessageConverter.createMtMessage("testPayload".getBytes(), TestUtils.VPI, 1, "testCorrId", Channel.UDP, 301);

    assertMtMessage(result, "testPayload", TestUtils.VPI.toString(), "low", 10);
  }

  @Test
  void createMtMessageNullChannelShortTimeoutTest() {
    MtMessage result = TceMtMessageConverter.createMtMessage("testPayload".getBytes(), TestUtils.VPI, 1, "testCorrId", null, 300);

    assertMtMessage(result, "testPayload", TestUtils.VPI.toString(), "dfol-mid-plus", 10);
  }

  @Test
  void createMtMessageNullCorrelationIdTest() {
    MtMessage result = TceMtMessageConverter.createMtMessage("testPayload".getBytes(), TestUtils.VPI, 1, "testCorrId", Channel.UDP, 100);

    assertMtMessage(result, "testPayload", TestUtils.VPI.toString(), "dfol-mid-plus", 10);
  }

  @Test
  void createMtMessageWithDefaultParametersTest() {
    MtMessage result = TceMtMessageConverter.createMtMessage("testPayload".getBytes(), TestUtils.VPI, 1, "testCorrId", Channel.UDP, 200);

    assertMtMessage(result, "testPayload", TestUtils.VPI.toString(), "dfol-mid-plus", 10);
  }
}
