package com.volvo.tisp.vehiclepingservice.rest;

import static org.junit.jupiter.api.Assertions.assertFalse;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;

@ExtendWith(SpringExtension.class)
public class TestRestHandlerTest {
  @Mock
  private MtMessagePublisher mtMessagePublisher;
  @Mock
  private PingEntityService pingEntityService;
  @Mock
  private ServiceAccessTokenService serviceAccessTokenService;
  @InjectMocks
  private TestRestHandler testRestHandler;

  @Test
  void clearDbCallsRemoveAllOnPingRepoTest() {

    TestRestHandler testRestHandler = new TestRestHandler(pingEntityService, null, null);
    CompletableFuture<Void> expectedFuture = CompletableFuture.completedFuture(null);
    Mockito.when(pingEntityService.removeAll()).thenReturn(expectedFuture);

    CompletableFuture<Void> result = testRestHandler.clearDb();
    Assertions.assertTrue(result.isDone());

    Mockito.verify(pingEntityService).removeAll();
    Assertions.assertTrue(expectedFuture.isDone());

    Mockito.verifyNoMoreInteractions(pingEntityService, mtMessagePublisher, serviceAccessTokenService);
  }

  @Test
  void constructorTest() {
    Assertions.assertNotNull(new TestRestHandler(pingEntityService, mtMessagePublisher, serviceAccessTokenService));
  }

  @Test
  void countReturnsRepositoryCountTest() {
    TestRestHandler testRestHandler = new TestRestHandler(pingEntityService, null, null);

    Long expectedCount = 5L;
    Mockito.when(pingEntityService.count()).thenReturn(CompletableFuture.completedFuture(expectedCount));

    CompletableFuture<Long> result = testRestHandler.count();

    Assertions.assertEquals(expectedCount, result.join());
    Mockito.verify(pingEntityService).count();
    Mockito.verifyNoMoreInteractions(pingEntityService, mtMessagePublisher, serviceAccessTokenService);
  }

  @Test
  void countWhenRepoThrowsExceptionTest() {
    Mockito.when(pingEntityService.count()).thenReturn(CompletableFuture.failedFuture(new RuntimeException("Database error")));

    CompletableFuture<Long> result = testRestHandler.count();

    Assertions.assertThrows(ExecutionException.class, result::get);
    Mockito.verify(pingEntityService).count();
    Mockito.verifyNoMoreInteractions(pingEntityService, mtMessagePublisher, serviceAccessTokenService);
  }

  @Test
  void getMtDispatcherFeatureReturnsCorrectFeatureValueTest() {
    Mockito.when(mtMessagePublisher.getMtDispatcherFeature()).thenReturn(true);

    CompletableFuture<Boolean> result = testRestHandler.getMtDispatcherFeature();

    Assertions.assertTrue(result.join());
    Mockito.verify(mtMessagePublisher).getMtDispatcherFeature();
    Mockito.verifyNoMoreInteractions(pingEntityService, mtMessagePublisher, serviceAccessTokenService);
  }

  @Test
  void getOBSServiceAccessTokenValidationFeatureReturnsCorrectValueTest() throws ExecutionException, InterruptedException {
    Mockito.when(serviceAccessTokenService.isServiceAccessTokenValidationEnabled()).thenReturn(true);

    CompletableFuture<Boolean> result = testRestHandler.getOBSServiceAccessTokenValidationFeature();

    Assertions.assertTrue(result.get());
    Mockito.verify(serviceAccessTokenService).isServiceAccessTokenValidationEnabled();
    Mockito.verifyNoMoreInteractions(pingEntityService, mtMessagePublisher, serviceAccessTokenService);
  }

  @Test
  void getOBSServiceAccessTokenValidationFeatureWhenDisabledTest() throws ExecutionException, InterruptedException {
    Mockito.when(serviceAccessTokenService.isServiceAccessTokenValidationEnabled()).thenReturn(false);

    CompletableFuture<Boolean> result = testRestHandler.getOBSServiceAccessTokenValidationFeature();

    assertFalse(result.get());
    Mockito.verify(serviceAccessTokenService).isServiceAccessTokenValidationEnabled();
    Mockito.verifyNoMoreInteractions(pingEntityService, mtMessagePublisher, serviceAccessTokenService);
  }

  @Test
  void setMtDispatcherCallsPublisherAndReturnsCompletedFutureTest() {
    CompletableFuture<Void> result = testRestHandler.setMtDispatcher(true);

    Mockito.verify(mtMessagePublisher).setMtDispatcherFeature(true);
    Assertions.assertTrue(result.isDone());
    Assertions.assertNull(result.join());
    Mockito.verifyNoMoreInteractions(pingEntityService, mtMessagePublisher, serviceAccessTokenService);
  }

  @Test
  void setOBSServiceAccessTokenValidationFeatureEnablesFeatureTest() {

    CompletableFuture<Void> result = testRestHandler.setOBSServiceAccessTokenValidationFeature(true);

    Mockito.verify(serviceAccessTokenService).setServiceAccessTokenValidationEnabled(true);
    Assertions.assertTrue(result.isDone());
    Mockito.verifyNoMoreInteractions(pingEntityService, mtMessagePublisher, serviceAccessTokenService);
  }
}
