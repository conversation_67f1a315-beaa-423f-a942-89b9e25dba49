package com.volvo.tisp.vehiclepingservice.compression;

import java.nio.charset.StandardCharsets;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.util.TestUtils;

class ZstdCompressionHandlerTest {
  private CompressionHandler compressionHandler;

  @Test
  void compressEmptyByteArrayTest() {
    byte[] emptyInput = new byte[0];
    byte[] compressedOutput = compressionHandler.compress(emptyInput);

    Assertions.assertNotNull(compressedOutput);
    Assertions.assertTrue(compressedOutput.length > 0);
  }

  @Test
  void compressInvalidParameterTest() {
    TestUtils.assertThrowIllegalArgumentException(() -> compressionHandler.compress(null), "bytes must not be null");
  }

  @Test
  void compressionDecompressionTest() {
    final String expectedPayload = "Testar 123!";

    byte[] compressed = compressionHandler.compress(expectedPayload.getBytes(StandardCharsets.UTF_8));

    byte[] decompressed = compressionHandler.decompress(compressed);

    String actualPayload = new String(decompressed, StandardCharsets.UTF_8);
    Assertions.assertEquals(expectedPayload, actualPayload);
  }

  @Test
  void decompressEmptyByteArrayTest() {
    byte[] emptyInput = new byte[0];

    Assertions.assertThrows(RuntimeException.class, () -> {
      compressionHandler.decompress(emptyInput);
    });
  }

  @Test
  void decompressInvalidCompressedDataTest() {
    byte[] invalidInput = {1, 2, 3, 4, 5}; // Non-Zstd compressed data

    Assertions.assertThrows(RuntimeException.class, () -> {
      compressionHandler.decompress(invalidInput);
    });
  }

  @Test
  void decompressInvalidParameterTest() {
    TestUtils.assertThrowIllegalArgumentException(() -> compressionHandler.decompress(null), "bytes must not be null");
  }

  @BeforeEach
  void setUp() {
    compressionHandler = new ZstdCompressionHandler();
  }
}
