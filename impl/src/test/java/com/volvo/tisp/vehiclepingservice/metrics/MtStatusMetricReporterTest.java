package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.mt.message.client.json.v1.Status;
import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class MtStatusMetricReporterTest {

  @Test
  void onMultipleAssetsConflict() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.logStatus(Status.MULTIPLE_ASSETS_CONFLICT);
      MetricsReporterTestUtils.checkCounter(registry, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "multiple_assets_conflict"), 1);
    });
  }

  @Test
  void onProcessMtStatusDelivered() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.logStatus(Status.DELIVERED);
      MetricsReporterTestUtils.checkCounter(registry, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "delivered"), 1);
    });
  }

  @Test
  void onProcessMtStatusFailed() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.logStatus(Status.FAILED);
      MetricsReporterTestUtils.checkCounter(registry, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "failed"), 1);
    });
  }

  @Test
  void onProcessMtStatusOverridden() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.logStatus(Status.OVERRIDDEN);
      MetricsReporterTestUtils.checkCounter(registry, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "overridden"), 1);
    });
  }

  @Test
  void onProcessMtStatusTimeout() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.logStatus(Status.TIMEOUT);
      MetricsReporterTestUtils.checkCounter(registry, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "timeout"), 1);
    });
  }
}