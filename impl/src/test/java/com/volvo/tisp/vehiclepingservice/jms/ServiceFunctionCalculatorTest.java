package com.volvo.tisp.vehiclepingservice.jms;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.rest.model.Channel;

public class ServiceFunctionCalculatorTest {

  @Test
  void CalculateServiceFunctionNullChannelTest() {
    ServiceFunctionCalculator calculator = new ServiceFunctionCalculator();
    String result = calculator.calculateServiceFunction(null);
    Assertions.assertEquals("ping-udp-only", result);
  }

  @Test
  void calculateServiceFunctionNonNullChannelTest() {
    ServiceFunctionCalculator calculator = new ServiceFunctionCalculator();

    String result = calculator.calculateServiceFunction(Channel.UDP);
    Assertions.assertEquals("ping-udp-only", result);

    result = calculator.calculateServiceFunction(Channel.SAT);
    Assertions.assertEquals("ping-sat-only", result);

    result = calculator.calculateServiceFunction(Channel.SMS);
    Assertions.assertEquals("ping-sms-only", result);
  }
}
