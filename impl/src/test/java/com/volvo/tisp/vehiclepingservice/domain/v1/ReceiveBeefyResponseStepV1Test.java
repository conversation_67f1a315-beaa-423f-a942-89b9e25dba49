package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.time.Instant;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessageResponse;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;

@ExtendWith(MockitoExtension.class)
class ReceiveBeefyResponseStepV1Test {

  @Mock
  private PingEntityService pingEntityService;
  @InjectMocks
  private ReceiveBeefyResponseStepV1 receiveBeefyResponseStep;
  @Mock
  private StateRepository stateRepository;

  private static MoDtoV1 createMockMoDtoV1() {
    BeefyMessageResponse beefyResponse = new BeefyMessageResponse();
    beefyResponse.setId(TestUtils.MESSAGE_ID);
    beefyResponse.setPayload(TestUtils.PAYLOAD);

    DecodedV1 decodedMessage = new DecodedV1();
    decodedMessage.setBeefyMessageResponse(beefyResponse);

    MoDtoV1 moDtoV1 = new MoDtoV1();
    moDtoV1.setDecoded(decodedMessage);
    moDtoV1.setVpi(TestUtils.VPI);

    return moDtoV1;
  }

  private static PingEntity createMockPingEntity() {
    PingEntity pingEntity = new PingEntity();
    pingEntity.setCorrelationId(TestUtils.CORRELATION_ID);
    pingEntity.setVpi(TestUtils.VPI.toString());
    pingEntity.setStartTimeInMillis(Instant.now().toEpochMilli() - 5000); // 5 seconds ago
    return pingEntity;
  }

  @Test
  void actionShouldUpdatePingEntityWithCorrectValues() {
    MoDtoV1 moDtoV1 = createMockMoDtoV1();
    PingEntity pingEntity = createMockPingEntity();

    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(stateRepository.pop(TestUtils.CORRELATION_ID)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receiveBeefyResponseStep.action(moDtoV1);
    result.join();

    Assertions.assertEquals(Status.SUCCESS, pingEntity.getStatus());
    Assertions.assertArrayEquals(TestUtils.PAYLOAD, pingEntity.getPayloadReceived());
    Assertions.assertNotNull(pingEntity.getStopTimeInMillis());
    Assertions.assertTrue(pingEntity.getDuration() > 0);
    Mockito.verify(pingEntityService).findByMessageId(TestUtils.MESSAGE_ID);
    Mockito.verify(pingEntityService).upsert(Mockito.any(PingEntity.class));
    Mockito.verify(stateRepository).pop(TestUtils.CORRELATION_ID);
    Mockito.verifyNoMoreInteractions(pingEntityService, stateRepository);
  }

  @Test
  void actionWhenPingEntityExistsShouldProcessAndSave() {
    MoDtoV1 moDtoV1 = createMockMoDtoV1();
    PingEntity pingEntity = createMockPingEntity();

    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(stateRepository.pop(TestUtils.CORRELATION_ID)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receiveBeefyResponseStep.action(moDtoV1);

    Assertions.assertNotNull(result);
    result.join();

    Mockito.verify(pingEntityService).findByMessageId(TestUtils.MESSAGE_ID);
    Mockito.verify(pingEntityService).upsert(Mockito.any(PingEntity.class));
    Mockito.verify(stateRepository).pop(TestUtils.CORRELATION_ID);
    Mockito.verifyNoMoreInteractions(pingEntityService, stateRepository);
  }

  @Test
  void actionWhenPingEntityNotFoundShouldReturnCompletedFuture() {
    MoDtoV1 moDtoV1 = createMockMoDtoV1();

    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receiveBeefyResponseStep.action(moDtoV1);

    Assertions.assertNotNull(result);
    result.join();
    Mockito.verify(pingEntityService).findByMessageId(TestUtils.MESSAGE_ID);
    Mockito.verifyNoMoreInteractions(pingEntityService);
    Mockito.verifyNoInteractions(stateRepository);
  }

  @Test
  void actionWhenServiceThrowsExceptionShouldPropagateException() {
    MoDtoV1 moDtoV1 = createMockMoDtoV1();

    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID))
        .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Service error")));

    CompletableFuture<Void> result = receiveBeefyResponseStep.action(moDtoV1);

    Assertions.assertThrows(RuntimeException.class, result::join);
    Mockito.verify(pingEntityService).findByMessageId(TestUtils.MESSAGE_ID);
    Mockito.verifyNoMoreInteractions(pingEntityService);
    Mockito.verifyNoInteractions(stateRepository);
  }

  @Test
  void actionWhenUpsertFailsShouldPropagateException() {
    MoDtoV1 moDtoV1 = createMockMoDtoV1();
    PingEntity pingEntity = createMockPingEntity();

    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.failedFuture(new RuntimeException("Upsert failed")));

    CompletableFuture<Void> result = receiveBeefyResponseStep.action(moDtoV1);

    Assertions.assertThrows(RuntimeException.class, result::join);
    Mockito.verify(pingEntityService).findByMessageId(TestUtils.MESSAGE_ID);
    Mockito.verify(pingEntityService).upsert(Mockito.any(PingEntity.class));
    Mockito.verifyNoMoreInteractions(pingEntityService);
    Mockito.verifyNoInteractions(stateRepository);
  }
}
