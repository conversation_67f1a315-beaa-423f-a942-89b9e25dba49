package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class LegacyMetricsReporterTest {

  @Test
  void legacyGetDataRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGetDataRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "getdata"), 1);
    });
  }

  @Test
  void legacyGuiBeefyPermission() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGuiBeefyPermission();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "guibeffypermission"), 1);
    });
  }

  @Test
  void legacyGuiCustomPermission() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGuiCustomPermission();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "guicustompermission"), 1);
    });
  }

  @Test
  void legacyPingRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyPingRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "authorized"), 1);
    });
  }

  @Test
  void unauthorizedPingRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.unauthorizedPingRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "unauthorized"), 1);
    });
  }

  @Test
  void legacyGetDataSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGetDataSuccess();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "getdata", "success"), 1);
    });
  }

  @Test
  void legacyGetDataNotFound() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGetDataNotFound();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "getdata", "notfound"), 1);
    });
  }

  @Test
  void legacyGetDataFailure() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGetDataFailure();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "getdata", "failure"), 1);
    });
  }

  @Test
  void legacyPingSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyPingSuccess();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "ping", "success"), 1);
    });
  }

  @Test
  void legacyPingBadRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyPingBadRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "ping", "badrequest"), 1);
    });
  }

  @Test
  void legacyPingNotFound() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyPingNotFound();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "ping", "notfound"), 1);
    });
  }

  @Test
  void legacyPingFailure() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyPingFailure();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "ping", "failure"), 1);
    });
  }
}
