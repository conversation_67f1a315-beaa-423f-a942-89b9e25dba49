package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class LegacyMetricsReporterTest {

  @Test
  void legacyGetDataRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGetDataRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "getdata"), 1);
    });
  }

  @Test
  void legacyGuiBeefyPermission() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGuiBeefyPermission();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "guibeffypermission"), 1);
    });
  }

  @Test
  void legacyGuiCustomPermission() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGuiCustomPermission();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "guicustompermission"), 1);
    });
  }

  @Test
  void legacyPingRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyPingRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "authorized"), 1);
    });
  }

  @Test
  void unauthorizedPingRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.unauthorizedPingRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "unauthorized"), 1);
    });
  }
}
