package com.volvo.tisp.vehiclepingservice.domain.v2;

import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.jms.MtDto;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;

@ExtendWith(MockitoExtension.class)
class SendMtPingStepV2Test {

  @Mock
  private MtMessagePublisher mtMessagePublisher;
  @Mock
  private PingV2Coder pingV2Coder;
  @InjectMocks
  private SendMtPingStepV2 sendMtPingStepV2;
  @Mock
  private StateRepository stateRepository;

  @Test
  void actionWithSuccessfulMessageSendTest() {
    UUID correlationId = UUID.randomUUID();
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), true, "body", Channel.UDP, 0);

    Mockito.when(pingV2Coder.encodePing(correlationId, requestData.body(), requestData.copyPayload())).thenReturn(new byte[0]);
    Mockito.when(mtMessagePublisher.publishMtMessage(Mockito.any(MtDto.class))).thenReturn(CompletableFuture.completedFuture(null));

    String system = "system";
    CompletableFuture<Void> result = sendMtPingStepV2.action(requestData, correlationId, system);

    Assertions.assertNotNull(result);
    String correlationIdString = correlationId.toString();
    Mockito.verify(mtMessagePublisher).publishMtMessage(Mockito.argThat(argument -> {
      Assertions.assertEquals(correlationIdString, argument.getCorrelationId());
      Assertions.assertEquals(requestData.vpi(), argument.getVpi().toString());
      Assertions.assertEquals(requestData.channel(), argument.getChannel());
      Assertions.assertEquals(requestData.timeoutInSeconds(), argument.getTimeout());
      Assertions.assertEquals(system, argument.getSystem());
      Assertions.assertEquals(2, argument.getServiceVersion());
      return true;
    }));
    Mockito.verify(stateRepository).push(Mockito.eq(correlationIdString), Mockito.eq(correlationIdString), Mockito.any(Instant.class));
    Mockito.verifyNoMoreInteractions(mtMessagePublisher, stateRepository);
  }
}
