package com.volvo.tisp.vehiclepingservice.jms;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.wirelesscar.config.mock.MockConfiguration;

class LegacyUriSchemeTest {
  private static MockConfiguration getEmptyMockConfiguration() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.deleteAllProperties();
    return mockConfiguration;
  }

  @Test
  void createJMSDestinationNameTest() {
    MockConfiguration mockConfiguration = getEmptyMockConfiguration();

    mockConfiguration.setSolution("SOLUTION");
    mockConfiguration.setSite("SITE");
    mockConfiguration.setEnvironment("ENVIRONMENT_ID");

    String postfix = "TEST_POSTFIX";

    String result = LegacyUriScheme.createJMSDestinationName(postfix);
    Assertions.assertEquals("SOLUTION.SITE.ENVIRONMENT_ID.COMPSHRT.TEST_POSTFIX", result);

    result = LegacyUriScheme.createJMSDestinationName(null);
    Assertions.assertEquals("SOLUTION.SITE.ENVIRONMENT_ID.COMPSHRT.NULL", result);
  }
}
