package com.volvo.tisp.vehiclepingservice.converters;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingDataResponse;

class VehiclePingOutputConverterTest {
  private static PingEntity createPingEntity(Status status, long startTimeInMillis, Long stopTimeInMillis) {
    PingEntity pingEntity = new PingEntity();
    pingEntity.setStatus(status);
    pingEntity.setVpi("testVpi");
    pingEntity.setStartTimeInMillis(startTimeInMillis);
    pingEntity.setStopTimeInMillis(stopTimeInMillis);
    return pingEntity;
  }

  @Test
  void convertValidStopTimeWithPendingStatusTest() {
    VehiclePingOutputConverter converter = new VehiclePingOutputConverter();
    PingEntity pingEntity = createPingEntity(Status.PENDING, 1000L, 2000L);

    VehiclePingDataResponse response = converter.convert(pingEntity);

    Assertions.assertNotNull(response);
    Assertions.assertEquals("testVpi", response.getVpi());
    Assertions.assertEquals(VehiclePingDataResponse.StatusEnum.PENDING, response.getStatus());
    Assertions.assertEquals(1000L, response.getStartTime());
    Assertions.assertEquals(2000L, response.getStopTime());
  }

  @Test
  void convertValidStopTimeWithSuccessStatusTest() {
    VehiclePingOutputConverter converter = new VehiclePingOutputConverter();
    PingEntity pingEntity = createPingEntity(Status.SUCCESS, 2000L, 3000L);

    VehiclePingDataResponse response = converter.convert(pingEntity);

    Assertions.assertNotNull(response);
    Assertions.assertEquals("testVpi", response.getVpi());
    Assertions.assertEquals(VehiclePingDataResponse.StatusEnum.SUCCESS, response.getStatus());
    Assertions.assertEquals(2000L, response.getStartTime());
    Assertions.assertEquals(3000L, response.getStopTime());
  }

  @Test
  void convertValidStopTimeWithTimeoutStatusTest() {
    VehiclePingOutputConverter converter = new VehiclePingOutputConverter();
    PingEntity pingEntity = createPingEntity(Status.TIMEOUT, 1000L, 2000L);

    VehiclePingDataResponse response = converter.convert(pingEntity);

    Assertions.assertNotNull(response);
    Assertions.assertEquals("testVpi", response.getVpi());
    Assertions.assertEquals(VehiclePingDataResponse.StatusEnum.FAILED, response.getStatus());
    Assertions.assertEquals(1000L, response.getStartTime());
    Assertions.assertEquals(2000L, response.getStopTime());
  }

  @Test
  void convertWhenStopTimeIsNullOrZeroTest() {
    VehiclePingOutputConverter converter = new VehiclePingOutputConverter();
    PingEntity pingEntity = createPingEntity(Status.SUCCESS, 1000L, null);

    VehiclePingDataResponse response = converter.convert(pingEntity);

    Assertions.assertEquals("testVpi", response.getVpi());
    Assertions.assertEquals(VehiclePingDataResponse.StatusEnum.SUCCESS, response.getStatus());
    Assertions.assertEquals(1000L, response.getStartTime());
    Assertions.assertNull(response.getStopTime());
    Assertions.assertNull(response.getStopTime());
  }
}
