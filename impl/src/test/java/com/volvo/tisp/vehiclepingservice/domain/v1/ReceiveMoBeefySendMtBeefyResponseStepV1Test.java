package com.volvo.tisp.vehiclepingservice.domain.v1;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessage;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;

@ExtendWith(MockitoExtension.class)
class ReceiveMoBeefySendMtBeefyResponseStepV1Test {

  @Mock
  private PingCoder pingCoder;
  @InjectMocks
  private ReceiveMoBeefySendMtBeefyResponseStepV1 receiveMoBeefyStep;


  private static MoDtoV1 createMoDtoV1(boolean copyPayload) {
    BeefyMessage beefyMessage = new BeefyMessage();
    beefyMessage.setId(TestUtils.MESSAGE_ID);
    beefyMessage.setCopyPayload(copyPayload);
    beefyMessage.setPayload(TestUtils.PAYLOAD);

    DecodedV1 decodedV1 = new DecodedV1();
    decodedV1.setBeefyMessage(beefyMessage);

    MoDtoV1 requestDto = new MoDtoV1();
    requestDto.setVpi(TestUtils.VPI);
    requestDto.setCorrelationId(TestUtils.CORRELATION_ID);
    requestDto.setDecoded(decodedV1);

    return requestDto;
  }

  @Test
  void getPayloadShouldEncodeBeefyResponseCopyPayloadFalse() {
    MoDtoV1 moDtoV1 = createMoDtoV1(false);

    byte[] expectedEncodedPayload = "encoded payload".getBytes();
    Mockito.when(pingCoder.encodeBeefyResponse(Mockito.eq(TestUtils.MESSAGE_ID), Mockito.any())).thenReturn(expectedEncodedPayload);

    byte[] result = receiveMoBeefyStep.getPayload(moDtoV1);

    Mockito.verify(pingCoder).encodeBeefyResponse(Mockito.eq(TestUtils.MESSAGE_ID), Mockito.argThat(payload -> payload != null && payload.length == 0));
    Assertions.assertArrayEquals(expectedEncodedPayload, result);
  }

  @Test
  void getPayloadShouldEncodeBeefyResponseCopyPayloadTrue() {
    MoDtoV1 moDtoV1 = createMoDtoV1(true);

    byte[] expectedEncodedPayload = "encoded payload".getBytes();
    Mockito.when(pingCoder.encodeBeefyResponse(TestUtils.MESSAGE_ID, TestUtils.PAYLOAD)).thenReturn(expectedEncodedPayload);

    byte[] result = receiveMoBeefyStep.getPayload(moDtoV1);

    Mockito.verify(pingCoder).encodeBeefyResponse(TestUtils.MESSAGE_ID, TestUtils.PAYLOAD);
    Assertions.assertArrayEquals(expectedEncodedPayload, result);
  }

  @Test
  void getPayloadShouldHandleEncodingException() {
    MoDtoV1 moDtoV1 = createMoDtoV1(true);

    Mockito.when(pingCoder.encodeBeefyResponse(TestUtils.MESSAGE_ID, TestUtils.PAYLOAD)).thenThrow(new RuntimeException("Encoding failed"));

    Assertions.assertThrows(RuntimeException.class, () -> receiveMoBeefyStep.getPayload(moDtoV1));
    Mockito.verify(pingCoder).encodeBeefyResponse(TestUtils.MESSAGE_ID, TestUtils.PAYLOAD);
  }
}
