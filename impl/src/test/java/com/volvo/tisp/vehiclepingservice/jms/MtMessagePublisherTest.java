package com.volvo.tisp.vehiclepingservice.jms;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.metrics.MtMessagePublisherMetricReporter;

public class MtMessagePublisherTest {
  @Mock
  private MtDispatcherMessagePublisher mtDispatcherMessagePublisher;
  @InjectMocks
  private MtMessagePublisher mtMessagePublisher;
  @Mock
  private MtMessagePublisherMetricReporter mtMessagePublisherMetricReporter;
  @Mock
  private TceMessagePublisher tceMessagePublisher;

  private static MtDto createMtDto() {
    MtDto dto = new MtDto();
    dto.setServiceVersion(1);
    dto.setVpi(Vpi.ofString("1234567890ABCDEF1234567890ABCDEF"));
    dto.setCorrelationId("testCorrelationId");
    return dto;
  }

  @Test
  void mtMessagePublisherConstructorAndSetterTest() {
    MtMessagePublisher publisher = new MtMessagePublisher(true, null, null, null);
    Assertions.assertTrue(publisher.getMtDispatcherFeature());

    publisher = new MtMessagePublisher(true, mtDispatcherMessagePublisher, tceMessagePublisher, mtMessagePublisherMetricReporter);
    Assertions.assertTrue(publisher.getMtDispatcherFeature());

    publisher = new MtMessagePublisher(false, mtDispatcherMessagePublisher, tceMessagePublisher, mtMessagePublisherMetricReporter);
    Assertions.assertFalse(publisher.getMtDispatcherFeature());

    publisher.setMtDispatcherFeature(true);
    Assertions.assertTrue(publisher.getMtDispatcherFeature());
    publisher.setMtDispatcherFeature(false);
    Assertions.assertFalse(publisher.getMtDispatcherFeature());
  }

  @Test
  void publishMtMessageNoSubscribersTest() {
    MtDto mockDto = createMtDto();

    MtDispatcherMessagePublisher mtDispatcherMessagePublisher = Mockito.mock(MtDispatcherMessagePublisher.class);
    TceMessagePublisher tceMessagePublisher = Mockito.mock(TceMessagePublisher.class);
    MtMessagePublisherMetricReporter mtMessagePublisherMetricReporter = Mockito.mock(MtMessagePublisherMetricReporter.class);

    MtMessagePublisher mtMessagePublisher = new MtMessagePublisher(false, mtDispatcherMessagePublisher, tceMessagePublisher, mtMessagePublisherMetricReporter);

    Mockito.when(tceMessagePublisher.publish(mockDto)).thenReturn(CompletableFuture.completedFuture(0));

    CompletionException thrown = Assertions.assertThrows(CompletionException.class, () -> mtMessagePublisher.publishMtMessage(mockDto).join());

    Throwable originalException = thrown.getCause();

    Assertions.assertEquals(
        "java.util.concurrent.CompletionException: com.volvo.tisp.vehiclepingservice.domain.exceptions.NoSubscriberException: No subscribers for TCE_MT_MESSAGE 2.0 for Ping/Beefy",
        originalException.getMessage());
    Mockito.verify(tceMessagePublisher).publish(mockDto);
    Mockito.verifyNoMoreInteractions(tceMessagePublisher, mtDispatcherMessagePublisher);
  }

  @Test
  void publishMtMessageWhenMtDispatcherFeatureEnabledTest() {
    boolean mtDispatcherFeature = true;
    MtDispatcherMessagePublisher mtDispatcherMessagePublisher = Mockito.mock(MtDispatcherMessagePublisher.class);
    TceMessagePublisher tceMessagePublisher = Mockito.mock(TceMessagePublisher.class);
    MtMessagePublisherMetricReporter mtMessagePublisherMetricReporter = Mockito.mock(MtMessagePublisherMetricReporter.class);

    MtMessagePublisher mtMessagePublisher = new MtMessagePublisher(mtDispatcherFeature, mtDispatcherMessagePublisher, tceMessagePublisher,
        mtMessagePublisherMetricReporter);

    MtDto dto = Mockito.mock(MtDto.class);
    Mockito.when(mtDispatcherMessagePublisher.publish(dto)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = mtMessagePublisher.publishMtMessage(dto);
    Assertions.assertTrue(result.isDone());
    Mockito.verify(mtDispatcherMessagePublisher).publish(dto);
    Mockito.verifyNoMoreInteractions(tceMessagePublisher, mtDispatcherMessagePublisher, mtMessagePublisherMetricReporter);
  }

  @Test
  void publishMtMessageWhenTcePublishingTest() {
    MtDto dto = createMtDto();

    MtDispatcherMessagePublisher mtDispatcherMessagePublisher = Mockito.mock(MtDispatcherMessagePublisher.class);
    TceMessagePublisher tceMessagePublisher = Mockito.mock(TceMessagePublisher.class);
    MtMessagePublisherMetricReporter mtMessagePublisherMetricReporter = Mockito.mock(MtMessagePublisherMetricReporter.class);

    mtMessagePublisher = new MtMessagePublisher(false, mtDispatcherMessagePublisher, tceMessagePublisher, mtMessagePublisherMetricReporter);

    Mockito.when(tceMessagePublisher.publish(dto)).thenReturn(CompletableFuture.completedFuture(1));
    CompletableFuture<Void> result = mtMessagePublisher.publishMtMessage(dto);

    assertDoesNotThrow(() -> result.get());

    Mockito.verify(tceMessagePublisher).publish(dto);
    Mockito.verify(mtMessagePublisherMetricReporter).onMtPublishSuccess();
    Mockito.verifyNoMoreInteractions(tceMessagePublisher, mtDispatcherMessagePublisher, mtMessagePublisherMetricReporter);
  }
}
