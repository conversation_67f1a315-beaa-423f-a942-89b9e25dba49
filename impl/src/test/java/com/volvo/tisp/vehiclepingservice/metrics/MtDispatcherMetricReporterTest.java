package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class MtDispatcherMetricReporterTest {
  @Test
  void onPublishFailure() {
    MetricsReporterTestUtils.initReporterAndTest(MtDispatcherMetricReporter::new, (registry, reporter) -> {
      reporter.onPublishFailure();
      MetricsReporterTestUtils.checkCounter(registry, MtDispatcherMetricReporter.MT_DISPATCHER, Tags.of(MtDispatcherMetricReporter.TYPE, "failure"), 1);
    });
  }

  @Test
  void onPublishNoSubscriber() {
    MetricsReporterTestUtils.initReporterAndTest(MtDispatcherMetricReporter::new, (registry, reporter) -> {
      reporter.onPublishNoSubscriber();
      MetricsReporterTestUtils.checkCounter(registry, MtDispatcherMetricReporter.MT_DISPATCHER, Tags.of(MtDispatcherMetricReporter.TYPE, "no-subscriber"), 1);
    });
  }

  @Test
  void onPublishSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(MtDispatcherMetricReporter::new, (registry, reporter) -> {
      reporter.onPublishSuccess();
      MetricsReporterTestUtils.checkCounter(registry, MtDispatcherMetricReporter.MT_DISPATCHER, Tags.of(MtDispatcherMetricReporter.TYPE, "success"), 1);
    });
  }
}