package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.jms.MtDto;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;

@ExtendWith(MockitoExtension.class)
class SendMtStepTest {

  @Mock
  private MtMessagePublisher mtMessagePublisher;
  private SendMtStep sendMtStep;
  @Mock
  private StateRepository stateRepository;

  @Test
  void actionShouldHandlePublishFailure() {
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), false, new String(TestUtils.PAYLOAD), null, 0);

    UUID correlationId = UUID.randomUUID();
    long swapCorrelationId = 12345L;
    String system = "TEST_SYSTEM";

    CompletableFuture<Void> failedFuture = new CompletableFuture<>();
    failedFuture.completeExceptionally(new RuntimeException("Publish failed"));

    Mockito.when(mtMessagePublisher.publishMtMessage(Mockito.any(MtDto.class))).thenReturn(failedFuture);

    CompletableFuture<Void> result = sendMtStep.action(requestData, correlationId, swapCorrelationId, system);

    Assertions.assertThrows(RuntimeException.class, result::join);
    Mockito.verifyNoInteractions(stateRepository);
  }

  @Test
  void actionShouldHandleStateUpdateFailure() {
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), false, new String(TestUtils.PAYLOAD), null, 0);

    UUID correlationId = UUID.randomUUID();
    long swapCorrelationId = 12345L;
    String system = "TEST_SYSTEM";

    Mockito.when(mtMessagePublisher.publishMtMessage(Mockito.any(MtDto.class))).thenReturn(CompletableFuture.completedFuture(null));
    Mockito.when(stateRepository.push(Mockito.anyString(), Mockito.anyString(), Mockito.any(Instant.class)))
        .thenThrow(new RuntimeException("State update failed"));

    CompletableFuture<Void> result = sendMtStep.action(requestData, correlationId, swapCorrelationId, system);

    Assertions.assertThrows(RuntimeException.class, result::join);
  }

  @Test
  void actionShouldPublishMessageAndUpdateState() {
    int timeout = 120;
    UUID correlationId = UUID.randomUUID();
    long swapCorrelationId = 12345L;
    String system = "TEST_SYSTEM";
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), false, new String(TestUtils.PAYLOAD), Channel.SAT, timeout);


    Mockito.when(mtMessagePublisher.publishMtMessage(Mockito.any(MtDto.class))).thenReturn(CompletableFuture.completedFuture(null));
    Mockito.when(stateRepository.push(
            Mockito.anyString(),
            Mockito.anyString(),
            Mockito.any(Instant.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    sendMtStep.action(requestData, correlationId, swapCorrelationId, system).join();

    Mockito.verify(mtMessagePublisher).publishMtMessage(Mockito.argThat(mtDto ->
        mtDto.getVpi().toString().equals(requestData.vpi()) &&
            mtDto.getCorrelationId().equals(correlationId.toString()) &&
            mtDto.getChannel().equals(requestData.channel()) &&
            mtDto.getTimeout() == timeout &&
            mtDto.getSystem().equals(system) &&
            mtDto.getServiceVersion() == 1
    ));
    Mockito.verify(stateRepository).push(Mockito.eq(correlationId.toString()), Mockito.eq(correlationId.toString()), Mockito.any(Instant.class)
    );
  }

  @BeforeEach
  void setUp() {
    sendMtStep = new SendMtStep(mtMessagePublisher, stateRepository) {
      @Override
      protected byte[] getPayload(RequestData requestData, long swapCorrelationId) {
        return TestUtils.PAYLOAD;
      }
    };
  }
}

