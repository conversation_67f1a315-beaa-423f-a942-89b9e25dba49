package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class PingManagerMetricReporterTest {
  @Test
  void onBeefyFromVehicle() {
    MetricsReporterTestUtils.initReporterAndTest(PingManagerMetricReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.onBeefyFromVehicle();
      MetricsReporterTestUtils.checkCounter(meterRegistry, PingManagerMetricReporter.PING_MANAGER, Tags.of("beefy", PingManagerMetricReporter.VEHICLE), 1);
    });
  }

  @Test
  void onBeefyResponseFromVehicle() {
    MetricsReporterTestUtils.initReporterAndTest(PingManagerMetricReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.onBeefyResponseFromVehicle();
      MetricsReporterTestUtils.checkCounter(meterRegistry, PingManagerMetricReporter.PING_MANAGER,
          Tags.of("beefyresponse", PingManagerMetricReporter.VEHICLE), 1);
    });
  }

  @Test
  void onPingFromVehicleV1() {
    MetricsReporterTestUtils.initReporterAndTest(PingManagerMetricReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.onPingFromVehicleV1();
      MetricsReporterTestUtils.checkCounter(meterRegistry, PingManagerMetricReporter.PING_MANAGER,
          Tags.of(PingManagerMetricReporter.PING, PingManagerMetricReporter.VEHICLE, PingManagerMetricReporter.VERSION, "v1"), 1);
    });
  }

  @Test
  void onPingFromVehicleV2() {
    MetricsReporterTestUtils.initReporterAndTest(PingManagerMetricReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.onPingFromVehicleV2();
      MetricsReporterTestUtils.checkCounter(meterRegistry, PingManagerMetricReporter.PING_MANAGER,
          Tags.of(PingManagerMetricReporter.PING, PingManagerMetricReporter.VEHICLE, PingManagerMetricReporter.VERSION, "v2"), 1);
    });
  }

  @Test
  void onPongFromVehicleV1() {
    MetricsReporterTestUtils.initReporterAndTest(PingManagerMetricReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.onPongFromVehicleV1();
      MetricsReporterTestUtils.checkCounter(meterRegistry, PingManagerMetricReporter.PING_MANAGER,
          Tags.of(PingManagerMetricReporter.PONG, PingManagerMetricReporter.VEHICLE, PingManagerMetricReporter.VERSION, "v1"), 1);
    });
  }

  @Test
  void onPongFromVehicleV2() {
    MetricsReporterTestUtils.initReporterAndTest(PingManagerMetricReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.onPongFromVehicleV2();
      MetricsReporterTestUtils.checkCounter(meterRegistry, PingManagerMetricReporter.PING_MANAGER,
          Tags.of(PingManagerMetricReporter.PONG, PingManagerMetricReporter.VEHICLE, PingManagerMetricReporter.VERSION, "v2"), 1);
    });
  }
}
