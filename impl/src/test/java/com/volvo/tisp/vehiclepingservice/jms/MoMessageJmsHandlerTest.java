package com.volvo.tisp.vehiclepingservice.jms;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.idpm2m.client.model.UnauthorizedTokenException;
import com.volvo.tisp.vehiclepingservice.converters.MoRouterMessageV1Converter;
import com.volvo.tisp.vehiclepingservice.converters.MoRouterMessageV2Converter;
import com.volvo.tisp.vehiclepingservice.converters.TceMoMessageV1Converter;
import com.volvo.tisp.vehiclepingservice.converters.TceMoMessageV2Converter;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.reporter.MoMessageHandlerMetricReporter;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.SrpOption;

public class MoMessageJmsHandlerTest {
  private static final String HANDLE = "someHandle";
  private static final String PAYLOAD = "payload";
  private static final String VPI = "1234567890ABCDEF1234567890ABCDEF";
  private MoMessageHandlerMetricReporter moMessageHandlerMetricReporter;
  private MoMessageJmsHandler moMessageJmsHandler;
  private MoRouterMessageV1Converter moRouterMessageV1Converter;
  private MoRouterMessageV2Converter moRouterMessageV2Converter;
  private PingManager pingManager;
  private ServiceAccessTokenService serviceAccessTokenService;
  private TceMoMessageV1Converter tceMoMessageV1Converter;
  private TceMoMessageV2Converter tceMoMessageV2Converter;

  public static MoMessage createMoMessage(int serviceVersion) {
    SrpOption srpOption = new SrpOption();
    srpOption.setDstService(256);
    srpOption.setDstVersion(serviceVersion);

    MoMessage moMessage = new MoMessage();
    moMessage.setPayload(PAYLOAD.getBytes());
    moMessage.setVehiclePlatformId(VPI);
    moMessage.setHandle(HANDLE);
    moMessage.setSrpOption(srpOption);

    return moMessage;
  }

  private static com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage getV1MoMessage() {
    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = new com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage();
    moMessage.withServiceAccessToken("invalid_token");
    moMessage.withVehiclePlatformId(VPI);
    moMessage.withServiceVersion(1);
    moMessage.withServiceId(256);
    moMessage.withPayload("test_payload");
    return moMessage;
  }

  private static JmsMessage<com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage> mockJmsMessage(
      com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage) {
    JmsMessage<com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage> jmsMessage = Mockito.mock(JmsMessage.class);
    Mockito.when(jmsMessage.payload()).thenReturn(moMessage);
    return jmsMessage;
  }

  private static JmsMessage<MoMessage> mockJmsMessage(MoMessage moMessage) {
    JmsMessage<MoMessage> jmsMessage = Mockito.mock(JmsMessage.class);
    Mockito.when(jmsMessage.payload()).thenReturn(moMessage);
    return jmsMessage;
  }

  @Test
  void moMessageJmsHandlerConstructortTest() {
    PingManager pingManager = Mockito.mock(PingManager.class);
    TceMoMessageV1Converter tceMoMessageV1Converter = Mockito.mock(TceMoMessageV1Converter.class);
    TceMoMessageV2Converter tceMoMessageV2Converter = Mockito.mock(TceMoMessageV2Converter.class);
    MoRouterMessageV1Converter moRouterMessageV1Converter = Mockito.mock(MoRouterMessageV1Converter.class);
    MoRouterMessageV2Converter moRouterMessageV2Converter = Mockito.mock(MoRouterMessageV2Converter.class);
    ServiceAccessTokenService serviceAccessTokenService = Mockito.mock(ServiceAccessTokenService.class);
    MoMessageHandlerMetricReporter moMessageHandlerMetricReporter = Mockito.mock(MoMessageHandlerMetricReporter.class);

    MoMessageJmsHandler handler = new MoMessageJmsHandler(pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter, serviceAccessTokenService, moMessageHandlerMetricReporter);

    Assertions.assertNotNull(handler, "MoMessageJmsHandler should be instantiated");
  }

  @Test
  void receiveMoRouterMoMessageInvalidServiceAccessTokenTest() throws Exception {
    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = getV1MoMessage();
    JmsMessage<com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage> jmsMessage = mockJmsMessage(moMessage);

    Mockito.doThrow(new UnauthorizedTokenException("Invalid token")).when(serviceAccessTokenService).validateToken("invalid_token");

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = moMessageJmsHandler.receiveMoRouterMoMessage(jmsMessage);
      Assertions.assertTrue(result.isDone());
      Assertions.assertFalse(result.isCompletedExceptionally());
    });

    Mockito.verify(moMessageHandlerMetricReporter).onTokenValidationFailure();

    Mockito.verify(serviceAccessTokenService).validateToken("invalid_token");
    Mockito.verify(pingManager, Mockito.never()).processMoMessageV2(Mockito.any());
    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @Test
  void receiveMoRouterMoMessageInvalidServiceAccessTokenTest1() throws Exception {
    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = getV1MoMessage();

    JmsMessage<com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage> jmsMessage = mockJmsMessage(moMessage);

    Mockito.when(jmsMessage.correlationId()).thenReturn(Optional.empty());

    Mockito.doThrow(UnauthorizedTokenException.class).when(serviceAccessTokenService).validateToken("invalidToken");

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = moMessageJmsHandler.receiveMoRouterMoMessage(jmsMessage);
      Assertions.assertTrue(result.isDone());
    });
    Mockito.verify(serviceAccessTokenService).validateToken("invalid_token");
    Mockito.verify(moRouterMessageV1Converter).convert(moMessage);
    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @Test
  void receiveMoRouterMoMessageUnsupportedServiceVersionTest() throws Exception {
    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = getV1MoMessage();
    moMessage.withServiceVersion(3);//Unsupported version
    moMessage.withServiceAccessToken("valid_token");

    JmsMessage<com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage> jmsMessage = mockJmsMessage(moMessage);
    Mockito.when(jmsMessage.correlationId()).thenReturn(Optional.empty());

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = moMessageJmsHandler.receiveMoRouterMoMessage(jmsMessage);
      Assertions.assertTrue(result.isDone());
    });

    Mockito.verify(serviceAccessTokenService).validateToken("valid_token");
    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @Test
  void receiveMoRouterMoMessageWithValidTrackingIdTest() throws Exception {
    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = new com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage();
    moMessage.withTrackingId("0123456789abcdef0123456789abcdef");
    moMessage.withVehiclePlatformId(VPI);
    moMessage.withServiceVersion(1);
    moMessage.withServiceAccessToken("valid-token");
    moMessage.withServiceId(256);

    JmsMessage<com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage> jmsMessage = mockJmsMessage(moMessage);

    Mockito.when(jmsMessage.payload()).thenReturn(moMessage);
    Mockito.when(jmsMessage.correlationId()).thenReturn(Optional.of("test-correlation-id"));

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = moMessageJmsHandler.receiveMoRouterMoMessage(jmsMessage);
      Assertions.assertTrue(result.isDone());
    });

    Mockito.verify(serviceAccessTokenService).validateToken("valid-token");
    Mockito.verify(moRouterMessageV1Converter).convert(moMessage);
    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @Test
  void receiveMoRouterMoMessage_emptyTrackingIdTest() throws UnauthorizedTokenException {
    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = getV1MoMessage();

    JmsMessage<com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage> jmsMessage = mockJmsMessage(moMessage);
    Mockito.when(jmsMessage.correlationId()).thenReturn(java.util.Optional.empty());

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = moMessageJmsHandler.receiveMoRouterMoMessage(jmsMessage);
      Assertions.assertTrue(result.isDone());
    });
    Mockito.verify(serviceAccessTokenService).validateToken(Mockito.any());
    Mockito.verify(moRouterMessageV1Converter).convert(moMessage);
    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @Test
  void receiveTceMoMessageInvalidSrpOptionTest() {
    MoMessage moMessage = createMoMessage(1);
    moMessage.getSrpOption().setDstService(257); //invalid service id
    JmsMessage<MoMessage> jmsMessage = mockJmsMessage(moMessage);

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = moMessageJmsHandler.receiveTceMoMessage(jmsMessage);
      Assertions.assertTrue(result.isDone());
    });

    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @Test
  void receiveTceMoMessageUnsupportedServiceVersionTest() {

    MoMessage moMessage = createMoMessage(3);
    JmsMessage<MoMessage> jmsMessage = mockJmsMessage(moMessage);

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = moMessageJmsHandler.receiveTceMoMessage(jmsMessage);
      Assertions.assertTrue(result.isDone());
    });
    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @Test
  void receiveTceMoMessageWhenServiceVersionIsOneTest() {
    MoMessage moMessage = createMoMessage(1);

    JmsMessage<MoMessage> message = mockJmsMessage(moMessage);
    Mockito.when(message.correlationId()).thenReturn(Optional.of("testCorrelationId"));

    MoDtoV1 convertedMessage = new MoDtoV1();
    Mockito.when(tceMoMessageV1Converter.convert(moMessage, "testCorrelationId")).thenReturn(convertedMessage);

    Mockito.when(pingManager.processMoMessageV1(convertedMessage)).thenReturn(CompletableFuture.completedFuture(null));

    TispContext.runInContext(() -> moMessageJmsHandler.receiveTceMoMessage(message));

    Mockito.verify(tceMoMessageV1Converter).convert(moMessage, "testCorrelationId");
    Mockito.verify(pingManager).processMoMessageV1(convertedMessage);
    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @Test
  void receiveTceMoMessage_whenServiceVersionIs2Test() {
    MoMessage moMessage = createMoMessage(2);

    JmsMessage<MoMessage> message = mockJmsMessage(moMessage);

    MoDtoV2 moDto = Mockito.mock(MoDtoV2.class);

    Mockito.when(message.correlationId()).thenReturn(Optional.of("testCorrelationId"));
    Mockito.when(tceMoMessageV2Converter.convert(message.payload())).thenReturn(moDto);
    Mockito.when(pingManager.processMoMessageV2(moDto)).thenReturn(CompletableFuture.completedFuture(null));

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = moMessageJmsHandler.receiveTceMoMessage(message);
      Assertions.assertTrue(result.isDone());
    });

    Mockito.verify(tceMoMessageV2Converter).convert(moMessage);
    Mockito.verify(moDto).setCorrelationId("testCorrelationId");
    Mockito.verify(pingManager).processMoMessageV2(moDto);
    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @Test
  void receiveTceMoMessage_withInvalidSrpOptionTest() {
    MoMessage moMessage = createMoMessage(257);
    JmsMessage<MoMessage> jmsMessage = mockJmsMessage(moMessage);

    TispContext.runInContext(() -> {
      CompletableFuture<Void> result = moMessageJmsHandler.receiveTceMoMessage(jmsMessage);
      Assertions.assertTrue(result.isDone());
    });

    Mockito.verifyNoMoreInteractions(serviceAccessTokenService, pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter);
  }

  @BeforeEach
  void setUp() {
    pingManager = Mockito.mock(PingManager.class);
    tceMoMessageV1Converter = Mockito.mock(TceMoMessageV1Converter.class);
    tceMoMessageV2Converter = Mockito.mock(TceMoMessageV2Converter.class);
    moRouterMessageV1Converter = Mockito.mock(MoRouterMessageV1Converter.class);
    moRouterMessageV2Converter = Mockito.mock(MoRouterMessageV2Converter.class);
    serviceAccessTokenService = Mockito.mock(ServiceAccessTokenService.class);
    moMessageHandlerMetricReporter = Mockito.mock(MoMessageHandlerMetricReporter.class);

    moMessageJmsHandler = new MoMessageJmsHandler(pingManager, tceMoMessageV1Converter, tceMoMessageV2Converter, moRouterMessageV1Converter,
        moRouterMessageV2Converter, serviceAccessTokenService, moMessageHandlerMetricReporter);
  }
}
