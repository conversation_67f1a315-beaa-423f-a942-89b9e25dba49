package com.volvo.tisp.vehiclepingservice.converters;

import java.util.Optional;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vehiclepingservice.domain.v1.PingCoder;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.Property;

@Component
public class TceMoMessageV1Converter {
  private static final String RECEIVING_CHANNEL = "TransportType";

  private final PingCoder pingCoder;

  public TceMoMessageV1Converter(PingCoder pingCoder) {
    this.pingCoder = pingCoder;
  }

  private static MoDtoV1 createMoDtoV1(MoMessage moMessage, String correlationId, DecodedV1 decoded) {
    MoDtoV1 dto = new MoDtoV1();

    dto.setVpi(Vpi.ofString(moMessage.getVehiclePlatformId()));
    dto.setCorrelationId(correlationId);
    dto.setDecoded(decoded);
    return dto;
  }

  private static Optional<String> getReceivingChannel(MoMessage moMessage) {
    return moMessage.getSolutionSpecificProperties()
        .stream()
        .filter(p -> p.getKey().equalsIgnoreCase(RECEIVING_CHANNEL))
        .map(Property::getValue)
        .findFirst();
  }

  public MoDtoV1 convert(MoMessage moMessage, String correlationId) {
    Validate.notNull(moMessage, "moMessage");

    DecodedV1 decoded = pingCoder.decode(moMessage.getPayload());
    MoDtoV1 dto = createMoDtoV1(moMessage, correlationId, decoded);
    Optional<String> receivingChannel = getReceivingChannel(moMessage);
    // possible values are: UDP,SMS,SATELLITE
    receivingChannel.ifPresent(dto::setReceivingChannel);

    return dto;
  }
}
