package com.volvo.tisp.vehiclepingservice.jms;

import static com.volvo.tisp.vehiclepingservice.jms.MoMessageJmsHandler.MO_MESSAGE_IN_QUEUE;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.context.Vehicle;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.identifier.VehicleIdentifier;
import com.volvo.tisp.idpm2m.client.model.UnauthorizedTokenException;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.mo.message.client.json.v1.MessageTypes;
import com.volvo.tisp.vehiclepingservice.converters.MoRouterMessageV1Converter;
import com.volvo.tisp.vehiclepingservice.converters.MoRouterMessageV2Converter;
import com.volvo.tisp.vehiclepingservice.converters.TceMoMessageV1Converter;
import com.volvo.tisp.vehiclepingservice.converters.TceMoMessageV2Converter;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.reporter.MoMessageHandlerMetricReporter;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

@JmsController(destination = MO_MESSAGE_IN_QUEUE)
public class MoMessageJmsHandler {
  public static final String MO_MESSAGE_IN_QUEUE = "MOMESSAGE.IN";
  private static final Logger logger = LoggerFactory.getLogger(MoMessageJmsHandler.class);

  private final MoMessageHandlerMetricReporter moMessageHandlerMetricReporter;
  private final MoRouterMessageV1Converter moRouterMessageV1Converter;
  private final MoRouterMessageV2Converter moRouterMessageV2Converter;
  private final PingManager pingManager;
  private final ServiceAccessTokenService serviceAccessTokenService;
  private final TceMoMessageV1Converter tceMoMessageV1Converter;
  private final TceMoMessageV2Converter tceMoMessageV2Converter;

  public MoMessageJmsHandler(PingManager pingManager,
      TceMoMessageV1Converter tceMoMessageV1Converter, TceMoMessageV2Converter tceMoMessageV2Converter, MoRouterMessageV1Converter moRouterMessageV1Converter,
      MoRouterMessageV2Converter moRouterMessageV2Converter, ServiceAccessTokenService serviceAccessTokenService,
      MoMessageHandlerMetricReporter moMessageHandlerMetricReporter) {
    this.pingManager = pingManager;
    this.tceMoMessageV1Converter = tceMoMessageV1Converter;
    this.tceMoMessageV2Converter = tceMoMessageV2Converter;
    this.moRouterMessageV1Converter = moRouterMessageV1Converter;
    this.moRouterMessageV2Converter = moRouterMessageV2Converter;
    this.serviceAccessTokenService = serviceAccessTokenService;
    this.moMessageHandlerMetricReporter = moMessageHandlerMetricReporter;
  }

  private static TispContext.Scope createTispContext(Vpi vpi) {
    return TispContext.current()
        .newScope()
        .vehicle(Vehicle.builder().vpi(VehicleIdentifier.fromString(vpi.toString())))
        .activate();
  }

  private static TispContext.Scope createTispContext(Vpi vpi, TrackingIdentifier tid) {
    return TispContext.current()
        .newScope()
        .vehicle(Vehicle.builder().vpi(VehicleIdentifier.fromString(vpi.toString())))
        .tid(tid)
        .activate();
  }

  @JmsMessageMapping(consumesType = MessageTypes.MO_MESSAGE, consumesVersion = MessageTypes.VERSION_1_0)
  public CompletableFuture<Void> receiveMoRouterMoMessage(final JmsMessage<com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage> jmsMessage) {
    logger.info("FLOW_TRACE: MoMessageJmsHandler.receiveMoRouterMoMessage called");
    Validate.notNull(jmsMessage, "jmsMessage");

    com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage message = jmsMessage.payload();
    Vpi vpi = Vpi.ofString(jmsMessage.payload().getVehiclePlatformId());
    int serviceVersion = jmsMessage.payload().getServiceVersion();
    String correlationId = jmsMessage.correlationId().orElse(null);

    TrackingIdentifier tid = StringUtils.hasLength(message.getTrackingId()) ?
        TrackingIdentifier.fromString(message.getTrackingId()) :
        TrackingIdentifier.create();

    try (final TispContext.Scope ignored = createTispContext(vpi, tid)) {

      logger.info(Retention.SHORT, "Received T3 {} version {} for VPI: {} with ServiceVersion: {}",
          MessageTypes.MO_MESSAGE, MessageTypes.VERSION_1_0, vpi, serviceVersion);
      try {
        serviceAccessTokenService.validateToken(message.getServiceAccessToken());
      } catch (UnauthorizedTokenException e) {
        logger.error("Invalid/Expired service access token from Vehicle", e);
        moMessageHandlerMetricReporter.onTokenValidationFailure();
        // Consume since no reason to retry
        return CompletableFuture.completedFuture(null);
        // TODO: marker here for when they decide how to handle this
      }

      if (message.getServiceId() != PingManager.SWAP_SERVICE_ID) {
        logger.info("Received a message with invalid service id {}, {}", message.getServiceId(), message);
        return CompletableFuture.completedFuture(null);
      }

      switch (serviceVersion) {
        case 1 -> {
          return processMoRouterMoMessageServiceVersion1(message, correlationId);
        }
        case 2 -> {
          return processMoRouterMoMessageServiceVersion2(message, correlationId);
        }
        default -> {
          logger.error("Service version {} of Service 256 is not supported! (vpi: {})", message.getServiceVersion(), vpi);
          moMessageHandlerMetricReporter.onUnsupportedVersion();
          return CompletableFuture.completedFuture(null);
        }
      }
    }
  }

  @JmsMessageMapping(consumesType = MessageTypesJms.TCE_MO_MESSAGE_TYPE, consumesVersion = MessageTypesJms.VERSION_2_0)
  public CompletableFuture<Void> receiveTceMoMessage(final JmsMessage<MoMessage> jmsMessage) {
    logger.info("FLOW_TRACE: MoMessageJmsHandler.receiveTceMoMessage called");
    Validate.notNull(jmsMessage, "jmsMessage");

    String correlationId = jmsMessage.correlationId().orElse(null);
    final Vpi vpi = Vpi.ofString(jmsMessage.payload().getVehiclePlatformId());

    if (jmsMessage.payload().getSrpOption().getDstService() != PingManager.SWAP_SERVICE_ID) {
      logger.info("Received a jmsMessage, correlationID {} with invalid SRP option: {} for vpi: {}", correlationId,
          jmsMessage.payload().getSrpOption().getDstService(), jmsMessage.payload().getVehiclePlatformId());
      moMessageHandlerMetricReporter.onInvalidSrpOption();
      return CompletableFuture.completedFuture(null);
    }

    try (final TispContext.Scope ignored = createTispContext(vpi)) {
      int serviceVersion = jmsMessage.payload().getSrpOption().getDstVersion();
      logger.info(Retention.SHORT, "Received TGW {} version {} for VPI: {} with ServiceVersion: {}", MessageTypesJms.TCE_MO_MESSAGE_TYPE,
          MessageTypesJms.VERSION_2_0, jmsMessage.payload().getVehiclePlatformId(), serviceVersion);

      switch (serviceVersion) {
        case 1 -> {
          return pingManager.processMoMessageV1(tceMoMessageV1Converter.convert(jmsMessage.payload(), correlationId));
        }
        case 2 -> {
          return processTceMoMessageServiceVersion2(jmsMessage, correlationId);
        }
        default -> {
          logger.error("Service version {} of Service 256 is not supported! (vpi: {})", serviceVersion, jmsMessage.payload().getVehiclePlatformId());
          moMessageHandlerMetricReporter.onInvalidServiceVersion();
          return CompletableFuture.completedFuture(null);
        }
      }
    }
  }

  private CompletableFuture<Void> processMoRouterMoMessageServiceVersion1(com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage message, String correlationId) {
    return CompletableFuture.completedFuture(message)
        .thenApply(moRouterMessageV1Converter::convert)
        .thenApply(
            moDtoV1 -> {
              moDtoV1.setCorrelationId(correlationId);
              return moDtoV1;
            })
        .thenCompose(pingManager::processMoMessageV1)
        .thenApply(v -> {
          moMessageHandlerMetricReporter.onProcessingV1Success();
          return v;
        })
        .exceptionally(throwable -> {
          logger.error("exception: ", throwable);
          moMessageHandlerMetricReporter.onProcessingV1Failure();
          throw new RuntimeException(throwable);
        });
  }

  private CompletableFuture<Void> processMoRouterMoMessageServiceVersion2(com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage message, String correlationId) {
    MoDtoV2 moDto = moRouterMessageV2Converter.convert(message);
    moDto.setCorrelationId(correlationId);
    return pingManager.processMoMessageV2(moDto)
        .thenApply(result -> {
          moMessageHandlerMetricReporter.onProcessingV2Success();
          return result;
        })
        .exceptionally(e -> {
          moMessageHandlerMetricReporter.onProcessingV2Failure();
          throw new RuntimeException(e);
        });
  }

  private CompletableFuture<Void> processTceMoMessageServiceVersion2(JmsMessage<MoMessage> message, String correlationId) {
    MoDtoV2 moDto = tceMoMessageV2Converter.convert(message.payload());
    moDto.setCorrelationId(correlationId);
    return pingManager.processMoMessageV2(moDto)
        .thenApply(v -> {
          moMessageHandlerMetricReporter.onProcessingV1Success();
          return v;
        })
        .exceptionally(throwable -> {
          moMessageHandlerMetricReporter.onProcessingV1Failure();
          throw new RuntimeException(throwable);
        });
  }
}
