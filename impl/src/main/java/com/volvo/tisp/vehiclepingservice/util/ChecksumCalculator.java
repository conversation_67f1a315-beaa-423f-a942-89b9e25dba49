package com.volvo.tisp.vehiclepingservice.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Locale;

import jakarta.xml.bind.DatatypeConverter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vehiclepingservice.domain.exceptions.InternalServerErrorException;

public class ChecksumCalculator {
  private static final MessageDigest digest;
  private static final Logger logger = LoggerFactory.getLogger(ChecksumCalculator.class);

  static {
    try {
      digest = MessageDigest.getInstance("SHA-256");
    } catch (NoSuchAlgorithmException e) {
      logger.error("Could not get MessageDigest for SHA-256 algorithm", e);
      throw new RuntimeException(e);
    }
  }

  private ChecksumCalculator() {
  }

  public static String getSHA256Hash(String data) {
    try {
      byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
      return bytesToHex(hash); // make it printable
    } catch (Exception ex) {
      logger.error("Couldn't get SHA256 Hash for data", ex);
      throw new InternalServerErrorException("Couldn't get SHA256 Hash for data");
    }
  }

  private static String bytesToHex(byte[] hash) {
    return DatatypeConverter.printHexBinary(hash).toLowerCase(Locale.ROOT);
  }
}
