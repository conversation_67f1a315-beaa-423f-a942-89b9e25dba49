package com.volvo.tisp.vehiclepingservice.converters;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage;
import com.volvo.tisp.vehiclepingservice.domain.v1.PingCoder;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;

@Component
public class MoRouterMessageV1Converter {
  private static final Base64.Decoder BASE64_DECODER = Base64.getDecoder();

  private final PingCoder pingCoder;

  public MoRouterMessageV1Converter(PingCoder pingCoder) {
    this.pingCoder = pingCoder;
  }

  public MoDtoV1 convert(MoMessage moMessage) {
    Validate.notNull(moMessage, "moMessage");

    byte[] unBase64Payload = BASE64_DECODER.decode(moMessage.getPayload().getBytes(StandardCharsets.UTF_8));
    DecodedV1 decoded = pingCoder.decode(unBase64Payload);

    MoDtoV1 dto = new MoDtoV1();
    dto.setVpi(Vpi.ofString(moMessage.getVehiclePlatformId()));
    dto.setDecoded(decoded);

    dto.setOnBoardTimeStamp(moMessage.getOnboardTimestamp());
    dto.setServerTimestamp(moMessage.getServerTimestamp());
    dto.setServiceAccessToken(moMessage.getServiceAccessToken());
    dto.setKeyId(moMessage.getKeyId());

    return dto;
  }
}
