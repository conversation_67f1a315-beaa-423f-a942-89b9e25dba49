package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Metrics for processing MO (mobile-originated) messages from vehicles using V1 protocol.
 */
@Component
public class PingManagerMetricReporter {
  static final String PING = "ping";
  static final String PING_MANAGER = "ping-manager";
  static final String PONG = "pong";
  static final String VEHICLE = "vehicle";
  static final String VERSION = "version";

  private final Counter beefyFromVehicleCounter;
  private final Counter beefyResponseFromVehicleCounter;
  private final Counter pingFromVehicleV1Counter;
  private final Counter pingFromVehicleV2Counter;
  private final Counter pongFromVehicleV1Counter;
  private final Counter pongFromVehicleV2Counter;

  public PingManagerMetricReporter(MeterRegistry meterRegistry) {
    beefyFromVehicleCounter = meterRegistry.counter(PING_MANAGER, "beefy", VEHICLE);
    beefyResponseFromVehicleCounter = meterRegistry.counter(PING_MANAGER, "beefyresponse", VEHICLE);
    pingFromVehicleV1Counter = meterRegistry.counter(PING_MANAGER, PING, VEHICLE, VERSION, "v1");
    pingFromVehicleV2Counter = meterRegistry.counter(PING_MANAGER, PING, VEHICLE, VERSION, "v2");
    pongFromVehicleV1Counter = meterRegistry.counter(PING_MANAGER, PONG, VEHICLE, VERSION, "v1");
    pongFromVehicleV2Counter = meterRegistry.counter(PING_MANAGER, PONG, VEHICLE, VERSION, "v2");
  }

  public void onBeefyFromVehicle() {
    beefyFromVehicleCounter.increment();
  }

  public void onBeefyResponseFromVehicle() {
    beefyResponseFromVehicleCounter.increment();
  }

  public void onPingFromVehicleV1() {
    pingFromVehicleV1Counter.increment();
  }

  public void onPingFromVehicleV2() {
    pingFromVehicleV2Counter.increment();
  }

  public void onPongFromVehicleV1() {
    pongFromVehicleV1Counter.increment();
  }

  public void onPongFromVehicleV2() {
    pongFromVehicleV2Counter.increment();
  }
}
