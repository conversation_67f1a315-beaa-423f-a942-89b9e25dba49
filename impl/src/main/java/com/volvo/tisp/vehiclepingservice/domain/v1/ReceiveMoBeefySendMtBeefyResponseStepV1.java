package com.volvo.tisp.vehiclepingservice.domain.v1;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessage;
import com.volvo.tisp.vehiclepingservice.util.TimeoutCalculator;
import com.wirelesscar.telematicunitservice.TelematicUnitService;

@Component
public class ReceiveMoBeefySendMtBeefyResponseStepV1 extends ReceiveMoStep {
  private final PingCoder pingCoder;

  public ReceiveMoBeefySendMtBeefyResponseStepV1(MtMessagePublisher mtMessagePublisher, PingCoder pingCoder, TimeoutCalculator timeoutCalculator,
      TelematicUnitService telematicUnitService) {
    super(mtMessagePublisher, telematicUnitService, timeoutCalculator);
    this.pingCoder = pingCoder;
  }

  @Override
  protected byte[] getPayload(MoDtoV1 moDtoV1) {
    BeefyMessage beefyMessage = moDtoV1.getDecoded().getBeefyMessage();
    boolean copyPayload = beefyMessage.getCopyPayload();
    return pingCoder.encodeBeefyResponse(beefyMessage.getId(), copyPayload ? beefyMessage.getPayload() : new byte[] {});
  }
}
