package com.volvo.tisp.vehiclepingservice.rest;

import java.util.concurrent.CompletableFuture;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.volvo.tisp.framework.security.annotation.Authorization;
import com.volvo.tisp.vehiclepingservice.reporter.LegacyMetricsReporter;

import nl.talsmasoftware.context.futures.ContextAwareCompletableFuture;

@Deprecated(since = "2024-09-24", forRemoval = true)
@RestController
@RequestMapping(GuiRestHandler.REQUEST_PATH)
public class GuiRestHandler {
  public static final String REQUEST_PATH = "/auth";

  private static final CompletableFuture<Void> COMPLETED_VOID_FUTURE = ContextAwareCompletableFuture.completedFuture(null);
  private static final String PERMISSION_BEEFY = "beefy";
  private static final String PERMISSION_CUSTOM = "custom";
  private static final String PERMISSION_PREFIX = "VPS.";
  private final LegacyMetricsReporter legacyMetricsReporter;

  public GuiRestHandler(LegacyMetricsReporter legacyMetricsReporter) {
    this.legacyMetricsReporter = legacyMetricsReporter;
  }

  @Authorization(permission = PERMISSION_PREFIX + PERMISSION_BEEFY)
  @GetMapping(path = "beefy")
  public CompletableFuture<Void> isAuthenticatedForBeefy() {
    legacyMetricsReporter.legacyGuiBeefyPermission();
    return COMPLETED_VOID_FUTURE;
  }

  @Authorization(permission = PERMISSION_PREFIX + PERMISSION_CUSTOM)
  @GetMapping(path = "custom")
  public CompletableFuture<Void> isAuthenticatedForCustom() {
    legacyMetricsReporter.legacyGuiCustomPermission();
    return COMPLETED_VOID_FUTURE;
  }
}
