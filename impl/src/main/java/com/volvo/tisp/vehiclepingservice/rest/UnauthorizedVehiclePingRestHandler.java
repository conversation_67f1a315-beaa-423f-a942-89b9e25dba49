package com.volvo.tisp.vehiclepingservice.rest;

import java.util.concurrent.CompletableFuture;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.metrics.LegacyMetricsReporter;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;

@Deprecated(since = "2024-09-24", forRemoval = true)
@RestController
@Authentication(required = false)
@RequestMapping(UnauthorizedVehiclePingRestHandler.REQUEST_PATH)
public class UnauthorizedVehiclePingRestHandler {
  public static final String REQUEST_PATH = "unauthorized/vehicleping";

  private final LegacyMetricsReporter legacyMetricsReporter;
  private final PingManager pingManager;

  public UnauthorizedVehiclePingRestHandler(PingManager pingManager, LegacyMetricsReporter legacyMetricsReporter) {
    this.pingManager = pingManager;
    this.legacyMetricsReporter = legacyMetricsReporter;
  }

  @GetMapping(path = "vpi/{vpi}", produces = MediaType.TEXT_PLAIN_VALUE)
  public CompletableFuture<String> unauthorizedRequestPing(@PathVariable("vpi") String vpi) {
    RequestData requestData = new RequestData(vpi.trim(), false, null, Channel.UDP, 30);
    legacyMetricsReporter.unauthorizedPingRequest();
    return pingManager.pingRest(requestData);
  }
}
