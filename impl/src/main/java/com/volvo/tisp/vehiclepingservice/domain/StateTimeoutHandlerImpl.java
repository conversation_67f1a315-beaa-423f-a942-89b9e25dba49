package com.volvo.tisp.vehiclepingservice.domain;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.staterepository.StateTimeoutHandler;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;

@Component
public class StateTimeoutHandlerImpl implements StateTimeoutHandler {
  private static final Logger logger = LoggerFactory.getLogger(StateTimeoutHandlerImpl.class);

  private final PingEntityService pingEntityService;

  public StateTimeoutHandlerImpl(PingEntityService pingEntityService) {
    this.pingEntityService = pingEntityService;
  }

  @Override
  public CompletableFuture<Void> handle(String reference, List<String> states) {
    return pingEntityService.findByCorrelationId(reference).thenApply(ping -> {
          if (ping != null) {
            ping.setStopTimeInMillis(Instant.now().toEpochMilli());
            ping = calculate(ping);
            ping.setStatus(Status.TIMEOUT);
            return pingEntityService.upsert(ping);
          }
          return CompletableFuture.completedFuture(null);
        })
        .thenAccept(e -> logger.info("Ping timed out from StateRepo for vpi: {}, correlationId: {}", states, reference));
  }

  private PingEntity calculate(PingEntity entity) {
    int durationInSeconds = (int) (entity.getStopTimeInMillis() - entity.getStartTimeInMillis()) / 1000;
    entity.setDuration(durationInSeconds);
    return entity;
  }
}
