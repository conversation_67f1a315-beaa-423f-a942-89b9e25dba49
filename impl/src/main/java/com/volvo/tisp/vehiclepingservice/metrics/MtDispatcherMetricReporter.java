package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Metrics for publishing MT (mobile-terminated) messages to vehicles.
 */
@Component
public class MtDispatcherMetricReporter {
  static final String MT_DISPATCHER = "mt-dispatcher";
  static final String TYPE = "type";
  private final Counter publishFailureCounter;
  private final Counter publishNoSubscriberCounter;
  private final Counter publishSuccessCounter;

  public MtDispatcherMetricReporter(MeterRegistry meterRegistry) {
    publishSuccessCounter = meterRegistry.counter(MT_DISPATCHER, TYPE, "success");
    publishNoSubscriberCounter = meterRegistry.counter(MT_DISPATCHER, TYPE, "no-subscriber");
    publishFailureCounter = meterRegistry.counter(MT_DISPATCHER, TYPE, "failure");
  }

  public void onPublishFailure() {
    publishFailureCounter.increment();
  }

  public void onPublishNoSubscriber() {
    publishNoSubscriberCounter.increment();
  }

  public void onPublishSuccess() {
    publishSuccessCounter.increment();
  }
}