package com.volvo.tisp.vehiclepingservice.domain.exceptions;

import java.io.Serial;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class InternalServerErrorException extends RuntimeException {

  @Serial
  private static final long serialVersionUID = -6916057561235643771L;

  public InternalServerErrorException() {
    super();
  }

  public InternalServerErrorException(String msg) {
    super(msg);
  }

  public InternalServerErrorException(Throwable cause) {
    super(cause);
  }

  public InternalServerErrorException(String msg, Throwable cause) {
    super(msg, cause);
  }
}
