package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

/**
 * Metrics for service access token operations (OBS tokens).
 */
@Component
public class ServiceAccessTokenMetricReporter {
  static final String SERVICE_ACCESS_TOKEN = "service-access-token";
  static final String TYPE = "type";
  private final Counter retrievingNewTokenCounter;
  private final Counter reuseTokenCounter;
  private final Counter tokenFailureCounter;
  private final Counter tokenRequestCounter;
  private final Counter tokenSuccessCounter;
  private final Counter validateFailureCounter;
  private final Counter validateSuccessCounter;

  public ServiceAccessTokenMetricReporter(MeterRegistry meterRegistry) {
    retrievingNewTokenCounter = meterRegistry.counter(SERVICE_ACCESS_TOKEN, "bostoken", "new");
    reuseTokenCounter = meterRegistry.counter(SERVICE_ACCESS_TOKEN, "bostoken", "reuse");
    tokenFailureCounter = meterRegistry.counter(SERVICE_ACCESS_TOKEN, Tags.of(TYPE, "failure"));
    tokenRequestCounter = meterRegistry.counter(SERVICE_ACCESS_TOKEN, Tags.of(TYPE, "request"));
    tokenSuccessCounter = meterRegistry.counter(SERVICE_ACCESS_TOKEN, Tags.of(TYPE, "success"));
    validateFailureCounter = meterRegistry.counter(SERVICE_ACCESS_TOKEN, Tags.of(TYPE, "validate-failure"));
    validateSuccessCounter = meterRegistry.counter(SERVICE_ACCESS_TOKEN, Tags.of(TYPE, "validate-success"));
  }

  public void onRetrievingNewToken() {
    retrievingNewTokenCounter.increment();
  }

  public void onReuseToken() {
    reuseTokenCounter.increment();
  }

  public void onTokenFailure() {
    tokenFailureCounter.increment();
  }

  public void onTokenRequest() {
    tokenRequestCounter.increment();
  }

  public void onTokenSuccess() {
    tokenSuccessCounter.increment();
  }

  public void onValidateFailure() {
    validateFailureCounter.increment();
  }

  public void onValidateSuccess() {
    validateSuccessCounter.increment();
  }
}