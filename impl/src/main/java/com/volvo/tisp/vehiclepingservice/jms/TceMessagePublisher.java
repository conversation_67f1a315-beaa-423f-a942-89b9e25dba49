package com.volvo.tisp.vehiclepingservice.jms;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.volvo.tisp.subscriptionrepository.client.DefaultMessagePublisher;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vehiclepingservice.converters.TceMtMessageConverter;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

@Component
public class TceMessagePublisher {
  public static final String CLIENT_ID = "vps";
  public static final String SERVICE_ID = "256";
  public static final String SUBSCRIPTION_OPTION_SRP_DST_SERVICE = "SRP_DST_SERVICE";
  public static final String SUBSCRIPTION_OPTION_TCE_SYSTEM = "SYSTEM";
  
  private static final Logger logger = LoggerFactory.getLogger(TceMessagePublisher.class);
  private final MessagePublisher<MtDto> pingPublisher;

  @Autowired
  public TceMessagePublisher(DefaultMessagePublisher.Builder builder) {
    this.pingPublisher = getPingPublisher(builder);
  }

  public MtMessage convert(MtDto dto) {
    return TceMtMessageConverter.createMtMessage(dto.getPayload(), dto.getVpi(), dto.getServiceVersion(), dto.getCorrelationId(), dto.getChannel(),
        dto.getTimeout());
  }

  public CompletableFuture<Integer> publish(MtDto dto) {
    logger.debug("Publishing TCE message, vpi: {}, corrId: {}", dto.getVpi(), dto.getCorrelationId());
    return pingPublisher
        .newMessage()
        .option(SUBSCRIPTION_OPTION_TCE_SYSTEM, dto.getSystem())
        .option(SUBSCRIPTION_OPTION_SRP_DST_SERVICE, SERVICE_ID)
        .publish(dto);
  }

  private MessagePublisher<MtDto> getPingPublisher(DefaultMessagePublisher.Builder builder) {
    return builder
        .messageType(MessageTypesJms.TCE_MT_MESSAGE_TYPE, MtDto.class)
        .version(MessageTypesJms.VERSION_2_0, this::convert)
        .build();
  }
}
