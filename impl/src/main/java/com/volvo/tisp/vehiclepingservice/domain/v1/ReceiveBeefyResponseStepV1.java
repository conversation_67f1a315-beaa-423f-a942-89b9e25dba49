package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;

@Component
public class ReceiveBeefyResponseStepV1 extends ReceiveStep {
  private static final Logger logger = LoggerFactory.getLogger(ReceiveBeefyResponseStepV1.class);

  public ReceiveBeefyResponseStepV1(PingEntityService pingEntityService, StateRepository stateRepository) {
    super(pingEntityService, stateRepository);
  }

  public CompletableFuture<Void> action(MoDtoV1 moDtoV1) {
    long messageId = moDtoV1.getDecoded().getBeefyMessageResponse().getId();

    return pingEntityService.findByMessageId(messageId)
        .thenCompose(
            pingEntity -> {
              if (pingEntity == null) {
                logger.warn("Unknown V1 BeefyResponse: {}, vpi: {}", messageId, moDtoV1.getVpi());
                return CompletableFuture.completedFuture(null);
              }

              return validateAndSave(pingEntity, moDtoV1.getDecoded().getBeefyMessageResponse().getPayload()).thenAccept(
                  savedPingEntity -> logger.info("Received BeefyResponse V1 after {} seconds for vpi: {}, payload: {}", savedPingEntity.getDuration(),
                      savedPingEntity.getVpi(), new String(savedPingEntity.getPayloadReceived(), StandardCharsets.UTF_8)));
            });
  }
}
