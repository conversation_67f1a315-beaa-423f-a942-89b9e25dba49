package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.jms.MtDto;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;

public abstract class SendMtStep {
  private final MtMessagePublisher mtMessagePublisher;
  private final StateRepository stateRepository;

  protected SendMtStep(MtMessagePublisher mtMessagePublisher, StateRepository stateRepository) {
    this.mtMessagePublisher = mtMessagePublisher;
    this.stateRepository = stateRepository;
  }

  public CompletableFuture<Void> action(RequestData requestData, UUID correlationId, long swapCorrelationId, String system) {
    MtDto mtDto = createMtDto(requestData, correlationId, swapCorrelationId, system);

    return mtMessagePublisher
        .publishMtMessage(mtDto)
        .thenCompose(v -> stateRepository.push(mtDto.getCorrelationId(), mtDto.getCorrelationId(), Instant.now().plus(mtDto.getTimeout(), ChronoUnit.SECONDS)))
        .thenApply(v -> null);
  }

  protected abstract byte[] getPayload(RequestData requestData, long swapCorrelationId);

  private MtDto createMtDto(RequestData requestData, UUID correlationId, long swapCorrelationId, String system) {
    MtDto mtDto = new MtDto();
    mtDto.setVpi(Vpi.ofString(requestData.vpi()));
    mtDto.setCorrelationId(correlationId.toString());
    mtDto.setPayload(getPayload(requestData, swapCorrelationId));
    mtDto.setChannel(requestData.channel());
    mtDto.setTimeout(requestData.timeoutInSeconds());
    mtDto.setSystem(system);
    mtDto.setServiceVersion(1);
    return mtDto;
  }
}
