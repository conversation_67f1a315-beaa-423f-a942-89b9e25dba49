package com.volvo.tisp.vehiclepingservice.converters;

import java.nio.charset.StandardCharsets;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingDataResponse;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingDataResponse.StatusEnum;

@Component
public class VehiclePingOutputConverter {
  private static void setErrorDescription(Status status, VehiclePingDataResponse resp) {
    switch (status) {
      case TIMEOUT:
        resp.setErrorDescription("Timed out before getting response");
        break;
      case OVERRIDDEN:
        resp.setErrorDescription("A new Ping Request was sent out for this vehicle before Pong was received");
        break;
      case CANCELLED:
        resp.setErrorDescription("Was not able to send message to vehicle");
        break;
      case ERROR:
        resp.setErrorDescription("Unknown error");
        break;
    }
  }

  public VehiclePingDataResponse convert(PingEntity pingEntity) {
    Validate.notNull(pingEntity, "pingEntity");

    VehiclePingDataResponse resp = new VehiclePingDataResponse();
    Status status = pingEntity.getStatus();

    resp.setVpi(pingEntity.getVpi());
    resp.setStatus(convertStatus(status));
    resp.setStartTime(pingEntity.getStartTimeInMillis());

    Long stopTime = pingEntity.getStopTimeInMillis();
    if (stopTime != null && stopTime != 0L) {
      resp.setStopTime(stopTime);
    }

    setErrorDescription(status, resp);
    setPayload(pingEntity, resp);

    return resp;
  }

  private StatusEnum convertStatus(Status status) {
    return switch (status) {
      case PENDING -> StatusEnum.PENDING;
      case SUCCESS -> StatusEnum.SUCCESS;
      default -> StatusEnum.FAILED;
    };
  }

  private boolean isValidPayload(byte[] payload) {
    return payload != null && payload.length > 0;
  }

  private void setPayload(PingEntity pingEntity, VehiclePingDataResponse resp) {
    byte[] payloadSent = pingEntity.getPayloadSent();
    if (isValidPayload(payloadSent)) {
      resp.setPayloadSent(new String(payloadSent, StandardCharsets.UTF_8));
    }

    byte[] payloadReceived = pingEntity.getPayloadReceived();
    if (isValidPayload(payloadReceived)) {
      resp.setPayloadReceived(new String(payloadReceived, StandardCharsets.UTF_8));
    }
  }
}
