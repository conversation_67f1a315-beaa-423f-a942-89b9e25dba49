package com.volvo.tisp.vehiclepingservice.util;

import static org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames.GRANT_TYPE;
import static org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames.SCOPE;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.endpoint.DefaultMapOAuth2AccessTokenResponseConverter;
import org.springframework.security.oauth2.core.endpoint.OAuth2AccessTokenResponse;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

// a copy from idpm2mclient
public class TokenFetcher {
  static final String SECRET_PROPERTY_NAME = "oauth2.idpm2m.secret";
  private static final Config config = ConfigFactory.getConfig();

  private final WebClient client;
  private final DefaultMapOAuth2AccessTokenResponseConverter converter;

  public TokenFetcher(WebClient webClient, DefaultMapOAuth2AccessTokenResponseConverter defaultMapOAuth2AccessTokenResponseConverter) {
    client = webClient;
    converter = defaultMapOAuth2AccessTokenResponseConverter;
  }

  private static MultiValueMap<String, String> createRequestBody(String scopes) {
    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add(GRANT_TYPE, AuthorizationGrantType.CLIENT_CREDENTIALS.getValue());
    map.add(SCOPE, scopes);
    return map;
  }

  public CompletableFuture<OAuth2AccessTokenResponse> postAccessTokenRequest(String spaceSeparatedScopes) {
    return client
        .post()
        .uri("/oauth2/token")
        .headers(headers -> headers.setBasicAuth(getBasicAuthentication()))
        .contentType(APPLICATION_FORM_URLENCODED)
        .accept(APPLICATION_JSON)
        .body(BodyInserters.fromFormData(createRequestBody(spaceSeparatedScopes)))
        .retrieve()
        .bodyToMono(Map.class)
        .toFuture()
        .thenApply(converter::convert);
  }

  private String getBasicAuthentication() {
    return config.getString(SECRET_PROPERTY_NAME)
        .orElseThrow(() -> new RuntimeException("No secret provided via config"));
  }
}
