package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Metrics for handling incoming MO (mobile-originated) messages from vehicles.
 */
@Component
public class MtMessagePublisherMetricReporter {
  static final String MT_MESSAGE_PUBLISHER = "mt-message-publisher";
  static final String TYPE = "type";
  private final Counter mtPublishFailureCounter;
  private final Counter mtPublishSuccessCounter;

  public MtMessagePublisherMetricReporter(MeterRegistry meterRegistry) {
    mtPublishSuccessCounter = meterRegistry.counter(MT_MESSAGE_PUBLISHER, TYPE, "success");
    mtPublishFailureCounter = meterRegistry.counter(MT_MESSAGE_PUBLISHER, TYPE, "failure");
  }

  public void onMtPublishFailure() {
    mtPublishFailureCounter.increment();
  }

  public void onMtPublishSuccess() {
    mtPublishSuccessCounter.increment();
  }
}