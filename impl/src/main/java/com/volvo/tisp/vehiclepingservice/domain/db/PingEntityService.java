package com.volvo.tisp.vehiclepingservice.domain.db;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.repository.PingEntityRepository;

@Component
public class PingEntityService {
  private static final Logger logger = LoggerFactory.getLogger(PingEntityService.class);

  private final PingEntityRepository pingEntityRepository;

  public PingEntityService(PingEntityRepository pingEntityRepository) {
    this.pingEntityRepository = pingEntityRepository;
  }

  public CompletableFuture<Long> count() {
    logger.info("Count DB");
    return pingEntityRepository
        .count()
        .doOnSuccess(unused -> logger.info("Count DB successfully"))
        .doOnError(e -> logger.error("Could not count DB", e))
        .toFuture();
  }

  public CompletableFuture<PingEntity> findByCorrelationId(String correlationId) {
    return pingEntityRepository.findByCorrelationId(correlationId).toFuture();
  }

  public CompletableFuture<PingEntity> findByMessageId(Long messageId) {
    return pingEntityRepository.findByMessageId(messageId).toFuture();
  }

  /**
   * Only for test envs
   *
   * @return
   */
  public CompletableFuture<Void> removeAll() {
    logger.info("Clear DB");
    return pingEntityRepository
        .deleteAll()
        .doOnSuccess(unused -> logger.info("DB Cleared successfully"))
        .doOnError(e -> logger.error("Could not clear DB", e))
        .toFuture();
  }

  public CompletionStage<PingEntity> upsert(PingEntity entity) {
    if (entity == null) {
      return CompletableFuture.completedFuture(null);
    }
    logger.debug(
        Retention.SHORT,
        "Thread: {}; Saving to DB id:'{}'; curr version:'{}'",
        Thread.currentThread().getName(),
        entity.getId(),
        entity.getVersion());
    return pingEntityRepository.save(entity).toFuture();
  }
}
