package com.volvo.tisp.vehiclepingservice.domain.exceptions;

import java.io.Serial;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class NotFoundException extends RuntimeException {

  @Serial
  private static final long serialVersionUID = -7186072491007954928L;

  public NotFoundException() {
    super();
  }

  public NotFoundException(String msg) {
    super(msg);
  }

  public NotFoundException(Throwable cause) {
    super(cause);
  }

  public NotFoundException(String msg, Throwable cause) {
    super(msg, cause);
  }
}
