package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.MeterRegistry;

@Component
public class LegacyMetricsReporter {
  static final String LEGACY_TAG = "legacy";
  static final String TOP_LEVEL_METRIC_NAME = "vehicleping";

  private final MeterRegistry meterRegistry;

  public LegacyMetricsReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void legacyGetDataRequest() {
    log(LEGACY_TAG, "getdata");
  }

  public void legacyGuiBeefyPermission() {
    // hopefully never used
    log(LEGACY_TAG, "guibeffypermission");
  }

  public void legacyGuiCustomPermission() {
    // hopefully never used
    log(LEGACY_TAG, "guicustompermission");
  }

  public void legacyPingRequest() {
    log(LEGACY_TAG, "authorized");
  }

  public void unauthorizedPingRequest() {
    log(LEG<PERSON>Y_TAG, "unauthorized");
  }

  private void log(String... tags) {
    this.meterRegistry.counter(TOP_LEVEL_METRIC_NAME, tags).increment();
  }
}
