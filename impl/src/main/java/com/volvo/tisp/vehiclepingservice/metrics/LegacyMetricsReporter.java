package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.MeterRegistry;

@Component
public class LegacyMetricsReporter {
  static final String LEGACY_TAG = "legacy";
  static final String TOP_LEVEL_METRIC_NAME = "vehicleping";

  private final MeterRegistry meterRegistry;

  public LegacyMetricsReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void legacyGetDataRequest() {
    log(LEGACY_TAG, "getdata");
  }

  public void legacyGuiBeefyPermission() {
    // hopefully never used
    log(LEGACY_TAG, "guibeffypermission");
  }

  public void legacyGuiCustomPermission() {
    // hopefully never used
    log(LEGACY_TAG, "guicustompermission");
  }

  public void legacyPingRequest() {
    log(LEGACY_TAG, "authorized");
  }

  public void unauthorizedPingRequest() {
    log(LEG<PERSON>Y_TAG, "unauthorized");
  }

  public void legacyGetDataSuccess() {
    log(LEGACY_TAG, "getdata", "success");
  }

  public void legacyGetDataNotFound() {
    log(LEGACY_TAG, "getdata", "notfound");
  }

  public void legacyGetDataFailure() {
    log(LEGACY_TAG, "getdata", "failure");
  }

  public void legacyPingSuccess() {
    log(LEGACY_TAG, "ping", "success");
  }

  public void legacyPingBadRequest() {
    log(LEGACY_TAG, "ping", "badrequest");
  }

  public void legacyPingNotFound() {
    log(LEGACY_TAG, "ping", "notfound");
  }

  public void legacyPingFailure() {
    log(LEGACY_TAG, "ping", "failure");
  }

  private void log(String... tags) {
    this.meterRegistry.counter(TOP_LEVEL_METRIC_NAME, tags).increment();
  }
}
