package com.volvo.tisp.vehiclepingservice.domain.v1.dto;

import java.util.Objects;

import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessage;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessageResponse;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Ping;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Pong;

public class DecodedV1 {
  private BeefyMessage beefyMessage;
  private BeefyMessageResponse beefyMessageResponse;
  private Ping ping;
  private Pong pong;

  public BeefyMessage getBeefyMessage() {
    return beefyMessage;
  }

  public BeefyMessageResponse getBeefyMessageResponse() {
    return beefyMessageResponse;
  }

  public Ping getPing() {
    return ping;
  }

  public Pong getPong() {
    return pong;
  }

  public void setBeefyMessage(BeefyMessage beefyMessage) {
    this.beefyMessage = beefyMessage;
  }

  public void setBeefyMessageResponse(BeefyMessageResponse beefyMessageResponse) {
    this.beefyMessageResponse = beefyMessageResponse;
  }

  public void setPing(Ping ping) {
    this.ping = ping;
  }

  public void setPong(Pong pong) {
    this.pong = pong;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    DecodedV1 decodedV1 = (DecodedV1) o;
    return Objects.equals(beefyMessage, decodedV1.beefyMessage) &&
        Objects.equals(beefyMessageResponse, decodedV1.beefyMessageResponse) &&
        Objects.equals(ping, decodedV1.ping) &&
        Objects.equals(pong, decodedV1.pong);
  }

  @Override
  public int hashCode() {
    return Objects.hash(beefyMessage, beefyMessageResponse, ping, pong);
  }

  @Override
  public String toString() {
    return "DecodedV1{" +
        "beefyMessage=" + beefyMessage +
        ", beefyMessageResponse=" + beefyMessageResponse +
        ", ping=" + ping +
        ", pong=" + pong +
        '}';
  }
}
