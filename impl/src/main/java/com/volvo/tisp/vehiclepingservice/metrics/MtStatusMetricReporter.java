package com.volvo.tisp.vehiclepingservice.metrics;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.mt.message.client.json.v1.Status;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

/**
 * Metrics for MT status updates received from vehicles.
 */
@Component
public class MtStatusMetricReporter {
  static final String MT_STATUS = "mt-status";
  static final String TYPE = "type";
  private final Map<String, Counter> counters;
  private final MeterRegistry meterRegistry;

  public MtStatusMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
    this.counters = new ConcurrentHashMap<>();
  }

  public void logStatus(Status status) {
    counters.computeIfAbsent(status.name().toLowerCase(), key -> meterRegistry.counter(MT_STATUS, Tags.of(Tag.of(TYPE, key)))).increment();
  }
}