package com.volvo.tisp.vehiclepingservice.domain.v2;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.util.ChecksumCalculator;
import com.volvo.tisp.vps.api.PingResponse;

@Component
public class ReceivePongStepV2 {
  private static final Logger logger = LoggerFactory.getLogger(ReceivePongStepV2.class);

  private final PingEntityService pingEntityService;
  private final StateRepository stateRepository;

  public ReceivePongStepV2(PingEntityService pingEntityService, StateRepository stateRepository) {
    this.pingEntityService = pingEntityService;
    this.stateRepository = stateRepository;
  }

  public CompletableFuture<Void> action(MoDtoV2 dto) {
    return pingEntityService.findByCorrelationId(dto.getDecoded().getPingResponse().getCorrelationId().toString())
        .thenCompose(pingEntity -> {
          if (pingEntity != null) {
            return validateAndSave(dto, pingEntity);
          }
          logger.warn("Unknown V2 Pong: {}, vpi: {}", dto.getDecoded().getPingResponse().getCorrelationId(), dto.getVpi());
          return CompletableFuture.completedFuture(null);
        });
  }

  // please refactor me if you want
  private void payloadCheck(PingResponse pingResponse, PingEntity pingEntity) {
    byte[] sent = pingEntity.getPayloadSent();

    if (pingEntity.getCopyPayload() != null && pingEntity.getCopyPayload()) {
      // Expects payload received to be identical to payload sent

      if (pingResponse.getPayload() != null && StringUtils.hasLength(pingResponse.getPayload().getPayloadValue())) {
        String payloadValue = pingResponse.getPayload().getPayloadValue();
        pingEntity.setPayloadReceived(pingResponse.getPayload().getPayloadValue().getBytes(StandardCharsets.UTF_8));

        String sha256Hash = ChecksumCalculator.getSHA256Hash(payloadValue);

        if (!sha256Hash.equalsIgnoreCase(pingResponse.getPayload().getChecksum())) {
          // Checksum of received payload does not match received payload
          pingEntity.setStatus(Status.ERROR);
          pingEntity.setError("Payload and checksum does not match");
          logger.error("Received Payload does not match received checksum, vpi: {}, correlationID: {}", pingEntity.getVpi(), pingEntity.getCorrelationId());
        } else if (!Arrays.equals(sent, pingResponse.getPayload().getPayloadValue().getBytes(StandardCharsets.UTF_8))) {
          // Payload sent and payload received does not match
          pingEntity.setStatus(Status.ERROR);
          pingEntity.setError("Mismatched Payload");
          logger.error("Received mismatching Payload, vpi: {}, correlationID: {}", pingEntity.getVpi(), pingEntity.getCorrelationId());
          logger.debug(Retention.SHORT, "Payload sent: {}, payload received: {}", pingEntity.getPayloadSent(),
              pingResponse.getPayload().getPayloadValue().getBytes(StandardCharsets.UTF_8));
        }
      } else {
        // No Payload in response (but was expected)
        pingEntity.setStatus(Status.ERROR);
        pingEntity.setError("Missing Payload");
        logger.error("Received Empty Payload when expected copy, vpi: {}, correlationID: {}", pingEntity.getVpi(), pingEntity.getCorrelationId());
      }
    }
  }

  private CompletableFuture<Void> validateAndSave(MoDtoV2 dto, PingEntity pingEntity) {
    PingResponse pingResponse = dto.getDecoded().getPingResponse();

    pingEntity.setStatus(Status.SUCCESS);
    pingEntity.setStopTimeInMillis(Instant.now().toEpochMilli());
    int durationInSeconds = (int) (pingEntity.getStopTimeInMillis() - pingEntity.getStartTimeInMillis()) / 1000;
    pingEntity.setDuration(durationInSeconds);
    payloadCheck(pingResponse, pingEntity); // modifies object

    return pingEntityService.upsert(pingEntity)
        .thenCompose(savedPingEntity -> stateRepository.pop(savedPingEntity.getCorrelationId()))
        .thenAccept(states -> logger.info("Received Pong V2 after {} seconds for vpi: {}", durationInSeconds, pingEntity.getVpi()))
        .toCompletableFuture();
  }
}
