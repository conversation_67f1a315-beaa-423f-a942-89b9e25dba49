package com.volvo.tisp.vehiclepingservice.domain.v1;

import org.springframework.stereotype.Component;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;

@Component
public class SendMtBeefyStepV1 extends SendMtStep {

  private final PingCoder pingCoder;

  public SendMtBeefyStepV1(PingCoder pingCoder, MtMessagePublisher mtMessagePublisher, StateRepository stateClient) {
    super(mtMessagePublisher, stateClient);
    this.pingCoder = pingCoder;
  }

  @Override
  protected byte[] getPayload(RequestData requestData, long swapCorrelationId) {
    return pingCoder.encodeBeefyPing(swapCorrelationId, requestData.copyPayload(), requestData.body());
  }
}
