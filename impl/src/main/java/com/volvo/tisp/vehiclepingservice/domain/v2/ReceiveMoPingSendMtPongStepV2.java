package com.volvo.tisp.vehiclepingservice.domain.v2;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.jms.MtDto;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.util.ChannelUtil;
import com.volvo.tisp.vehiclepingservice.util.TimeoutCalculator;
import com.volvo.tisp.vps.api.TestService;
import com.wirelesscar.telematicunitservice.TelematicUnitService;

@Component
public class ReceiveMoPingSendMtPongStepV2 {
  private static final Logger logger = LoggerFactory.getLogger(ReceiveMoPingSendMtPongStepV2.class);

  private final MtMessagePublisher mtMessagePublisher;
  private final PingV2Coder pingV2Coder;
  private final TimeoutCalculator timeoutCalculator;
  private final TelematicUnitService telematicUnitService;

  public ReceiveMoPingSendMtPongStepV2(MtMessagePublisher mtMessagePublisher, PingV2Coder pingV2Coder, TimeoutCalculator timeoutCalculator,
      TelematicUnitService telematicUnitService) {
    this.mtMessagePublisher = mtMessagePublisher;
    this.pingV2Coder = pingV2Coder;
    this.timeoutCalculator = timeoutCalculator;
    this.telematicUnitService = telematicUnitService;
  }

  public CompletableFuture<Void> action(MoDtoV2 dto) {
    TestService decoded = dto.getDecoded();

    byte[] payload = pingV2Coder.encodePong(dto, null);

    MtDto sendingDto = new MtDto();
    sendingDto.setVpi(dto.getVpi());
    sendingDto.setCorrelationId(decoded.getPingRequest().getCorrelationId().toString());
    sendingDto.setPayload(payload);
    sendingDto.setChannel(ChannelUtil.calculateResponseChannel(dto.getReceivingChannel()));
    sendingDto.setTimeout(timeoutCalculator.getDefaultTimeout(sendingDto.getChannel()));
    sendingDto.setServiceVersion(2);
    sendingDto.setCorrelationId(dto.getCorrelationId());

    return telematicUnitService
        .getSystem(dto.getVpi().toString())
        .thenApply(system -> {
          sendingDto.setSystem(system);
          logger.debug(Retention.SHORT, "Got System: {} for vpi: {}", system, sendingDto.getVpi());
          return sendingDto;
        })
        .thenCompose(mtMessagePublisher::publishMtMessage);
  }
}
