package com.volvo.tisp.vehiclepingservice.metrics;

import java.util.Locale;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vehiclepingservice.rest.model.Channel;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Metrics for REST endpoints handling vehicle ping requests and data retrieval.
 */
@Component
public class VehiclePingRestMetricReporter {
  static final String DATA_REQUEST = "data-request";
  static final String FLOW = "flow";
  static final String PING_TO_VEHICLE = "ping-to-vehicle";
  static final String TYPE = "type";
  static final String VEHICLE_PING = "vehicle-ping";
  private final Counter dataFailureCounter;
  private final Counter dataNotFoundCounter;
  private final Counter dataRequestCounter;
  private final Counter dataSuccessCounter;
  private final MeterRegistry meterRegistry;
  private final Counter pingBadRequestCounter;
  private final Counter pingFailureCounter;
  private final Counter pingNotFoundCounter;
  private final Counter pingSuccessCounter;

  public VehiclePingRestMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
    dataRequestCounter = meterRegistry.counter(VEHICLE_PING, FLOW, DATA_REQUEST, TYPE, "request");
    dataSuccessCounter = meterRegistry.counter(VEHICLE_PING, FLOW, DATA_REQUEST, TYPE, "success");
    dataNotFoundCounter = meterRegistry.counter(VEHICLE_PING, FLOW, DATA_REQUEST, TYPE, "not-found");
    dataFailureCounter = meterRegistry.counter(VEHICLE_PING, FLOW, DATA_REQUEST, TYPE, "failure");

    pingSuccessCounter = meterRegistry.counter(VEHICLE_PING, FLOW, PING_TO_VEHICLE, TYPE, "success");
    pingNotFoundCounter = meterRegistry.counter(VEHICLE_PING, FLOW, PING_TO_VEHICLE, TYPE, "not-found");
    pingBadRequestCounter = meterRegistry.counter(VEHICLE_PING, FLOW, PING_TO_VEHICLE, TYPE, "bad-request");
    pingFailureCounter = meterRegistry.counter(VEHICLE_PING, FLOW, PING_TO_VEHICLE, TYPE, "failure");
  }

  public void onDataFailure() {
    dataFailureCounter.increment();
  }

  public void onDataNotFound() {
    dataNotFoundCounter.increment();
  }

  public void onDataRequest() {
    dataRequestCounter.increment();
  }

  public void onDataSuccess() {
    dataSuccessCounter.increment();
  }

  public void onPingBadRequest() {
    pingBadRequestCounter.increment();
  }

  public void onPingFailure() {
    pingFailureCounter.increment();
  }

  public void onPingNotFound() {
    pingNotFoundCounter.increment();
  }

  public void onPingRequest(Channel channel) {
    meterRegistry.counter(VEHICLE_PING, "request.channel", channel.getValue().toLowerCase(Locale.ROOT)).increment();
  }

  public void onPingSuccess() {
    pingSuccessCounter.increment();
  }
}