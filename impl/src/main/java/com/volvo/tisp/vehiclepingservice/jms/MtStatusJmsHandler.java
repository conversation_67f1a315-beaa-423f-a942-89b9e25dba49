package com.volvo.tisp.vehiclepingservice.jms;

import static com.volvo.tisp.vehiclepingservice.jms.MtStatusJmsHandler.MT_STATUS_IN_QUEUE;

import java.util.Locale;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.context.Vehicle;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.identifier.VehicleIdentifier;
import com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus;
import com.volvo.tisp.vc.mt.message.client.json.v1.Status;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.InternalServerErrorException;
import com.volvo.tisp.vehiclepingservice.reporter.MtStatusMetricReporter;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

@JmsController(destination = MT_STATUS_IN_QUEUE)
public class MtStatusJmsHandler {
  public static final String MT_STATUS_IN_QUEUE = "MTSTATUS.IN";
  private static final Logger logger = LoggerFactory.getLogger(MtStatusJmsHandler.class);
  private final MtStatusMetricReporter mtStatusMetricReporter;
  private final PingManager pingManager;

  @Autowired
  public MtStatusJmsHandler(PingManager pingManager, MtStatusMetricReporter mtStatusMetricReporter) {
    this.pingManager = pingManager;
    this.mtStatusMetricReporter = mtStatusMetricReporter;
  }

  @JmsMessageMapping(consumesType = MessageTypesJms.TCE_MTSTATUS_MESSAGE_TYPE, consumesVersion = MessageTypesJms.VERSION_2_0)
  public CompletableFuture<Void> pingStatus(final JmsMessage<MtStatusMessage> message) {
    logger.info("FLOW_TRACE: MtStatusJmsHandler.pingStatus called");
    return processMtStatusMessage(
        message.payload().getVehiclePlatformId(),
        message.payload().getCorrelationId(),
        convertToNewestStatusType(message)
    );
  }

  @JmsMessageMapping(consumesType = MessageTypes.MT_STATUS, consumesVersion = MessageTypes.VERSION_1_0)
  public CompletableFuture<Void> pingStatusMoRouter(final JmsMessage<MtStatus> message) {
    logger.info("FLOW_TRACE: MtStatusJmsHandler.pingStatusMoRouter called");
    return processMtStatusMessage(
        message.payload().getVehiclePlatformId(),
        message.payload().getCorrelationId(),
        message.payload().getStatus()
    );
  }

  private Status convertToNewestStatusType(JmsMessage<MtStatusMessage> message) {
    String status = message.payload().getStatus();
    String vpi = message.payload().getVehiclePlatformId();
    String correlationId = message.payload().getCorrelationId();

    return switch (status) {
      case "TIMEOUT" -> Status.TIMEOUT;
      case "DELIVERED" -> Status.DELIVERED;
      case "CANCELLED", "CANCELED" -> Status.CANCELED;
      case "FAILED", "ERROR", "REJECTED", "SERVICE_UNSUPPORTED" -> Status.FAILED;
      case "OVERRIDDEN", "OVERRIDEN" -> Status.OVERRIDDEN;
      default -> {
        String err = String.format(Locale.ROOT, "Unknown MtStatus %s for vpi: %s with correlation id %s received", status, vpi, correlationId);
        logger.error(err);
        throw new InternalServerErrorException(err);
      }
    };
  }

  private CompletableFuture<Void> processMtStatusMessage(String vehiclePlatformId, String correlationId, Status status) {
    try (final TispContext.Scope ignored = TispContext.current()
        .newScope()
        .vehicle(Vehicle.builder().vpi(VehicleIdentifier.fromString(vehiclePlatformId)))
        .activate()) {
      return pingManager.processMtStatus(correlationId, status)
          .thenApply(v -> {
            mtStatusMetricReporter.logStatus(status);
            return v;
          })
          .exceptionally(throwable -> {
            mtStatusMetricReporter.logStatus(Status.FAILED);
            throw new RuntimeException(throwable);
          });
    }
  }
}
