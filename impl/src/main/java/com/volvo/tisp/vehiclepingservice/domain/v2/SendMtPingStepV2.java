package com.volvo.tisp.vehiclepingservice.domain.v2;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.springframework.stereotype.Component;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.jms.MtDto;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;

@Component
public class SendMtPingStepV2 {

  private final MtMessagePublisher mtMessagePublisher;
  private final PingV2Coder pingV2Coder;
  private final StateRepository stateRepository;

  public SendMtPingStepV2(MtMessagePublisher mtMessagePublisher, PingV2Coder pingV2Coder, StateRepository stateRepository) {
    this.mtMessagePublisher = mtMessagePublisher;
    this.pingV2Coder = pingV2Coder;
    this.stateRepository = stateRepository;
  }

  public CompletableFuture<Void> action(RequestData requestData, UUID correlationId, String system) {
    byte[] payload = pingV2Coder.encodePing(correlationId, requestData.body(), requestData.copyPayload());

    MtDto dto = new MtDto();
    dto.setVpi(Vpi.ofString(requestData.vpi()));
    dto.setCorrelationId(correlationId.toString());
    dto.setPayload(payload);
    dto.setChannel(requestData.channel());
    dto.setTimeout(requestData.timeoutInSeconds());
    dto.setSystem(system);
    dto.setServiceVersion(2);

    return mtMessagePublisher
        .publishMtMessage(dto)
        .thenCompose(v -> stateRepository.push(dto.getCorrelationId(), dto.getCorrelationId(), Instant.now().plus(dto.getTimeout(), ChronoUnit.SECONDS)))
        .thenApply(v -> null);
  }
}
