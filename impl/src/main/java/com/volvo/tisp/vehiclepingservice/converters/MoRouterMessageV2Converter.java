package com.volvo.tisp.vehiclepingservice.converters;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.domain.v2.PingV2Coder;
import com.volvo.tisp.vps.api.TestService;

@Component
public class MoRouterMessageV2Converter {
  private static final Base64.Decoder BASE64_DECODER = Base64.getDecoder();
  private static final Logger logger = LoggerFactory.getLogger(MoRouterMessageV2Converter.class);
  private final PingV2Coder pingV2Coder;

  public MoRouterMessageV2Converter(PingV2Coder pingV2Coder) {
    this.pingV2Coder = pingV2Coder;
  }

  private static MoDtoV2 createDto(MoMessage moMessage, TestService decodedService) {
    MoDtoV2 dto = new MoDtoV2();

    dto.setVpi(Vpi.ofString(moMessage.getVehiclePlatformId()));
    dto.setDecoded(decodedService);
    dto.setOnBoardTimeStamp(moMessage.getOnboardTimestamp());
    dto.setServerTimestamp(moMessage.getServerTimestamp());
    dto.setServiceAccessToken(moMessage.getServiceAccessToken());
    dto.setKeyId(moMessage.getKeyId());

    return dto;
  }

  public MoDtoV2 convert(MoMessage moMessage) {
    Validate.notNull(moMessage, "moMessage");

    TestService decodedService = decodePayload(moMessage.getPayload());
    return createDto(moMessage, decodedService);
  }

  private TestService decodePayload(String payload) {
    try {
      logger.debug("Attempting to decode payload of length: {}", payload.length());
      byte[] unBase64Payload = BASE64_DECODER.decode(payload.getBytes(StandardCharsets.UTF_8));
      return pingV2Coder.decode(unBase64Payload);
    } catch (IllegalArgumentException e) {
      logger.error("Failed to decode base64 payload: {}", e.getMessage());
      throw new IllegalArgumentException("Invalid base64 encoding in payload", e);
    }
  }
}
