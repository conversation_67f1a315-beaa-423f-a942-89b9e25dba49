package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.nio.charset.StandardCharsets;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNException;
import com.volvo.tisp.vehiclepingservice.swap.common.PERStream;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessage;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessageResponse;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Ping;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Pong;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.TestServicePdu;

@Component
public class PingCoder {
  private static final Logger logger = LoggerFactory.getLogger(PingCoder.class);

  private static byte[] encodePdu(TestServicePdu pdu, PERStream perStream, String errorMessage) {
    byte[] payload;

    try {
      pdu.encode(perStream);
      payload = perStream.getBuffer();
    } catch (ASNException e) {
      logger.error(errorMessage, e);
      throw new RuntimeException(e);
    }

    return payload;
  }

  public DecodedV1 decode(byte[] payload) {
    TestServicePdu pdu = new TestServicePdu();
    PERStream perStream = new PERStream(payload);

    DecodedV1 decoded = new DecodedV1();

    try {
      pdu.decode(perStream);
      logger.debug(
          Retention.SHORT,
          "Recieved message with choice: {}, {}",
          pdu.getChoice(),
          pdu.getChoiceAsString());

      switch (pdu.getChoice()) {
        case TestServicePdu.E_PING: {
          logger.debug(Retention.SHORT, "Decoded a Ping message");
          decoded.setPing(pdu.getPing());
          break;
        }
        case TestServicePdu.E_PONG: {
          logger.debug(Retention.SHORT, "Decoded a Pong message");
          decoded.setPong(pdu.getPong());
          break;
        }
        case TestServicePdu.E_BEEFYMESSAGE: {
          logger.debug(Retention.SHORT, "Decoded a Beefy message");
          decoded.setBeefyMessage(pdu.getBeefyMessage());
          break;
        }

        case TestServicePdu.E_BEEFYMESSAGERESPONSE: {
          logger.debug(Retention.SHORT, "Decoded a BeefyResponse message");
          decoded.setBeefyMessageResponse(pdu.getBeefyMessageResponse());
          break;
        }

        default:
          throw new IllegalStateException("Unexpected value: " + pdu.getChoice());
      }
    } catch (ASNException e) {
      logger.error("Could not decode MoMessage payload", e);
      throw new RuntimeException(e);
    }

    return decoded;
  }

  public byte[] encodeBeefyPing(long swapCorrelationId, Boolean copyPayload, String body) {
    logger.debug(Retention.SHORT, "Encoding a BeefyMessage");
    BeefyMessage beefyMessage = new BeefyMessage();

    beefyMessage.setId(swapCorrelationId);
    beefyMessage.setCopyPayload(copyPayload != null && copyPayload);
    beefyMessage.setResponseExpected(true);
    byte[] pingPayload = body.getBytes(StandardCharsets.UTF_8);
    beefyMessage.setResponseSize(pingPayload.length);
    beefyMessage.setPayload(pingPayload);

    TestServicePdu pdu = new TestServicePdu();
    PERStream perStream = new PERStream();

    pdu.setBeefyMessage(beefyMessage);

    return encodePdu(pdu, perStream, "Could not encode BeefyMessage");
  }

  public byte[] encodeBeefyResponse(long swapCorrelationId, byte[] body) {
    logger.debug(Retention.SHORT, "Encoding a BeefyResponse");
    TestServicePdu pdu = new TestServicePdu();
    PERStream perStream = new PERStream();
    byte[] payload;
    try {
      BeefyMessageResponse beefyMessageResponse = new BeefyMessageResponse();
      beefyMessageResponse.setId(swapCorrelationId);
      beefyMessageResponse.setPayload(body);
      // may be used to send status information of any kind
      beefyMessageResponse.setStatus("SUCCESS"); // can't encode null

      pdu.setBeefyMessageResponse(beefyMessageResponse);

      pdu.encode(perStream);
      payload = perStream.getBuffer();
    } catch (ASNException e) {
      logger.error("Could not encode BeefyMessageResponse, ASNException", e);
      throw new RuntimeException(e);
    } catch (Exception e) {
      logger.error("Could not encode BeefyMessageResponse, unknown error", e);
      throw new RuntimeException(e);
    }

    return payload;
  }

  public byte[] encodePing(long swapCorrelationId) {
    logger.debug(Retention.SHORT, "Encoding a Ping message");
    Ping ping = new Ping();

    ping.setId(swapCorrelationId);
    ping.setResponseExpected(true);

    TestServicePdu pdu = new TestServicePdu();
    PERStream perStream = new PERStream();

    pdu.setPing(ping);

    return encodePdu(pdu, perStream, "Could not encode Ping V1");
  }

  public byte[] encodePong(long swapCorrelationId) {
    logger.debug(Retention.SHORT, "Encoding a Pong message");
    TestServicePdu pdu = new TestServicePdu();
    PERStream perStream = new PERStream();

    Pong pong = new Pong();
    pong.setId(swapCorrelationId);

    pdu.setPong(pong);

    return encodePdu(pdu, perStream, "Could not encode Pong");
  }
}
