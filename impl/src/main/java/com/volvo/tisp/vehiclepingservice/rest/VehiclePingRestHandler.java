package com.volvo.tisp.vehiclepingservice.rest;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.framework.security.annotation.Authorization;
import com.volvo.tisp.vehiclepingservice.converters.PingJsonOutputConverter;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.BadRequestException;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NotFoundException;
import com.volvo.tisp.vehiclepingservice.metrics.LegacyMetricsReporter;
import com.volvo.tisp.vehiclepingservice.metrics.VehiclePingRestMetricReporter;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;
import com.volvo.tisp.vehiclepingservice.util.ExceptionUtil;

@Deprecated(since = "2024-09-24", forRemoval = true)
@RestController
@RequestMapping(VehiclePingRestHandler.REQUEST_PATH)
public class VehiclePingRestHandler {
  public static final String REQUEST_PATH = "/vehicleping";
  private static final Logger logger = LoggerFactory.getLogger(VehiclePingRestHandler.class);

  private final PingJsonOutputConverter jsonOutputConverter;
  private final LegacyMetricsReporter legacyMetricsReporter;
  private final PingManager pingManager;
  private final VehiclePingRestMetricReporter vehiclePingRestMetricReporter;

  public VehiclePingRestHandler(PingJsonOutputConverter jsonOutputConverter, PingManager pingManager,
                               LegacyMetricsReporter legacyMetricsReporter, VehiclePingRestMetricReporter vehiclePingRestMetricReporter) {
    this.jsonOutputConverter = jsonOutputConverter;
    this.pingManager = pingManager;
    this.legacyMetricsReporter = legacyMetricsReporter;
    this.vehiclePingRestMetricReporter = vehiclePingRestMetricReporter;
  }

  @Authentication(required = false)
  @GetMapping(path = "data/correlationid/{correlationId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletableFuture<String> getDataFromCorrelationId(@PathVariable("correlationId") String correlationId) {
    legacyMetricsReporter.legacyGetDataRequest();
    vehiclePingRestMetricReporter.onDataRequest();

    return pingManager.getData(correlationId)
        .thenApply(pingEntity -> {
          vehiclePingRestMetricReporter.onDataSuccess();
          return jsonOutputConverter.apply(pingEntity);
        })
        .handle((result, throwable) -> {
          if (throwable != null) {
            if (ExceptionUtil.isCauseAssignableFrom(throwable, NotFoundException.class)) {
              vehiclePingRestMetricReporter.onDataNotFound();
            } else {
              vehiclePingRestMetricReporter.onDataFailure();
            }
            // Re-throw the exception to maintain original behavior
            if (throwable instanceof RuntimeException) {
              throw (RuntimeException) throwable;
            } else {
              throw new RuntimeException(throwable);
            }
          }
          return result;
        });
  }

  @Authorization(permission = "VPS.ping")
  @GetMapping(path = "vpi/{vpi}", produces = MediaType.TEXT_PLAIN_VALUE)
  public CompletableFuture<String> requestPing(@PathVariable("vpi") String vpi) {
    logger.info("FLOW_TRACE: VehiclePingRestHandler.requestPing called with VPI: {}", vpi);
    RequestData requestData = new RequestData(vpi.trim(), false, null, Channel.UDP, 30);
    legacyMetricsReporter.legacyPingRequest();
    vehiclePingRestMetricReporter.onPingRequest(requestData.channel());

    return pingManager.pingRest(requestData)
        .thenApply(correlationId -> {
          vehiclePingRestMetricReporter.onPingSuccess();
          return correlationId;
        })
        .handle((result, throwable) -> {
          if (throwable != null) {
            if (ExceptionUtil.isCauseAssignableFrom(throwable, BadRequestException.class)) {
              vehiclePingRestMetricReporter.onPingBadRequest();
            } else if (ExceptionUtil.isCauseAssignableFrom(throwable, NotFoundException.class)) {
              vehiclePingRestMetricReporter.onPingNotFound();
            } else {
              vehiclePingRestMetricReporter.onPingFailure();
            }
            // Re-throw the exception to maintain original behavior
            if (throwable instanceof RuntimeException) {
              throw (RuntimeException) throwable;
            } else {
              throw new RuntimeException(throwable);
            }
          }
          return result;
        });
  }
}
