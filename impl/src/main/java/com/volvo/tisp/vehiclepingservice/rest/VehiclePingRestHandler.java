package com.volvo.tisp.vehiclepingservice.rest;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.framework.security.annotation.Authorization;
import com.volvo.tisp.vehiclepingservice.converters.PingJsonOutputConverter;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.reporter.LegacyMetricsReporter;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;

@Deprecated(since = "2024-09-24", forRemoval = true)
@RestController
@RequestMapping(VehiclePingRestHandler.REQUEST_PATH)
public class VehiclePingRestHandler {
  public static final String REQUEST_PATH = "/vehicleping";
  private static final Logger logger = LoggerFactory.getLogger(VehiclePingRestHandler.class);

  private final PingJsonOutputConverter jsonOutputConverter;
  private final LegacyMetricsReporter legacyMetricsReporter;
  private final PingManager pingManager;

  public VehiclePingRestHandler(PingJsonOutputConverter jsonOutputConverter, PingManager pingManager, LegacyMetricsReporter legacyMetricsReporter) {
    this.jsonOutputConverter = jsonOutputConverter;
    this.pingManager = pingManager;
    this.legacyMetricsReporter = legacyMetricsReporter;
  }

  @Authentication(required = false)
  @GetMapping(path = "data/correlationid/{correlationId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletableFuture<String> getDataFromCorrelationId(@PathVariable("correlationId") String correlationId) {
    legacyMetricsReporter.legacyGetDataRequest();
    return pingManager.getData(correlationId).thenApply(jsonOutputConverter::apply);
  }

  @Authorization(permission = "VPS.ping")
  @GetMapping(path = "vpi/{vpi}", produces = MediaType.TEXT_PLAIN_VALUE)
  public CompletableFuture<String> requestPing(@PathVariable("vpi") String vpi) {
    logger.info("FLOW_TRACE: VehiclePingRestHandler.requestPing called with VPI: {}", vpi);
    RequestData requestData = new RequestData(vpi.trim(), false, null, Channel.UDP, 30);
    legacyMetricsReporter.legacyPingRequest();
    return pingManager.pingRest(requestData);
  }
}
