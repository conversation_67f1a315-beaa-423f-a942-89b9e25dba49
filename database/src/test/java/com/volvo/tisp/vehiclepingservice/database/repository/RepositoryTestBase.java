package com.volvo.tisp.vehiclepingservice.database.repository;

import org.springframework.boot.test.autoconfigure.data.mongo.DataMongoTest;
import org.springframework.boot.testcontainers.service.connection.ServiceConnection;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

@Testcontainers
@DataMongoTest
@ContextConfiguration(classes = MongoTestConfig.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class RepositoryTestBase {
  private static final String SHARED_SERVICES_DOCKER_PUBLIC = "artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/";

  @Container
  @ServiceConnection
  static MongoDBContainer mongoDBContainer = new MongoDBContainer(getDockerImageName("mongo:4.4.6"));

  @DynamicPropertySource
  private static void setProperties(DynamicPropertyRegistry registry) {
    registry.add("spring.data.mongodb.database", () -> "test");
  }

  private static DockerImageName getDockerImageName(String imageName) {
    return DockerImageName.parse(SHARED_SERVICES_DOCKER_PUBLIC + imageName).asCompatibleSubstituteFor(imageName);
  }

}
