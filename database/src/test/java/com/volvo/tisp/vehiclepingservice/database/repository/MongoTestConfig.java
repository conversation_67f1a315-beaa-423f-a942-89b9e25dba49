package com.volvo.tisp.vehiclepingservice.database.repository;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.repository.config.EnableReactiveMongoRepositories;

@Configuration
@EnableReactiveMongoRepositories(basePackages = "com.volvo.tisp.vehiclepingservice.database.repository")
@EntityScan("com.volvo.tisp.vehiclepingservice.database.model")
@ComponentScan(basePackages = "com.volvo.tisp.vehiclepingservice.database.repository")
public class MongoTestConfig {
}
