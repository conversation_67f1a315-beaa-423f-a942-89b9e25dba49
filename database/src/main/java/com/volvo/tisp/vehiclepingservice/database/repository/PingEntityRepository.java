package com.volvo.tisp.vehiclepingservice.database.repository;

import org.springframework.data.mongodb.repository.ReactiveMongoRepository;

import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;

import reactor.core.publisher.Mono;

public interface PingEntityRepository extends ReactiveMongoRepository<PingEntity, String> {

  Mono<PingEntity> findByCorrelationId(String correlationId);

  Mono<PingEntity> findByMessageId(Long messageId);
}
