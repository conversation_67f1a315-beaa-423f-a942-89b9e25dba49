package com.volvo.tisp.vehiclepingservice.database.entity;

import java.time.Instant;
import java.util.Arrays;
import java.util.Objects;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Version;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

@Document("pings")
public class PingEntity {

  private Boolean copyPayload;
  private String correlationId;
  @CreatedDate
  private Instant createdAt;
  private int duration;
  private String error;
  @Id
  @Field(targetType = FieldType.OBJECT_ID)
  private String id;
  private Long messageId;
  private String mtStatus;
  private byte[] payloadReceived;
  private byte[] payloadSent;
  private int serviceVersion;
  private Long startTimeInMillis;
  private Status status;
  private Long stopTimeInMillis;
  private String system;
  @Version
  private Long version;
  private String vpi;

  public PingEntity() {
    this.createdAt = Instant.now();
  }

  @Override
  public boolean equals(Object o) {
    if (this == o)
      return true;
    if (!(o instanceof PingEntity))
      return false;
    PingEntity that = (PingEntity) o;
    return duration == that.duration &&
        serviceVersion == that.serviceVersion &&
        Objects.equals(correlationId, that.correlationId) &&
        Objects.equals(messageId, that.messageId) &&
        Arrays.equals(payloadReceived, that.payloadReceived) &&
        Arrays.equals(payloadSent, that.payloadSent);
  }

  public Boolean getCopyPayload() {
    return copyPayload;
  }

  public String getCorrelationId() {
    return correlationId;
  }

  public Instant getCreatedAt() {
    return createdAt;
  }

  public int getDuration() {
    return duration;
  }

  public String getError() {
    return error;
  }

  public String getId() {
    return id;
  }

  public Long getMessageId() {
    return messageId;
  }

  public String getMtStatus() {
    return mtStatus;
  }

  public byte[] getPayloadReceived() {
    return payloadReceived;
  }

  public byte[] getPayloadSent() {
    return payloadSent;
  }

  public int getServiceVersion() {
    return serviceVersion;
  }

  public Long getStartTimeInMillis() {
    return startTimeInMillis;
  }

  public Status getStatus() {
    return status;
  }

  public Long getStopTimeInMillis() {
    return stopTimeInMillis;
  }

  public String getSystem() {
    return system;
  }

  public Long getVersion() {
    return version;
  }

  public String getVpi() {
    return vpi;
  }

  @Override
  public int hashCode() {
    int result = Objects.hash(correlationId, messageId, duration, serviceVersion);
    result = 31 * result + Arrays.hashCode(payloadReceived);
    result = 31 * result + Arrays.hashCode(payloadSent);
    return result;
  }

  public void setCopyPayload(Boolean copyPayload) {
    this.copyPayload = copyPayload;
  }

  public void setCorrelationId(String correlationId) {
    this.correlationId = correlationId;
  }

  public void setCreatedAt(Instant createdAt) {
    this.createdAt = createdAt;
  }

  public void setDuration(int duration) {
    this.duration = duration;
  }

  public void setError(String error) {
    this.error = error;
  }

  public void setId(String id) {
    this.id = id;
  }

  public void setMessageId(Long messageId) {
    this.messageId = messageId;
  }

  public void setMtStatus(String mtStatus) {
    this.mtStatus = mtStatus;
  }

  public void setPayloadReceived(byte[] payloadReceived) {
    this.payloadReceived = payloadReceived;
  }

  public void setPayloadSent(byte[] payloadSent) {
    this.payloadSent = payloadSent;
  }

  public void setServiceVersion(int serviceVersion) {
    this.serviceVersion = serviceVersion;
  }

  public void setStartTimeInMillis(Long startTimeInMillis) {
    this.startTimeInMillis = startTimeInMillis;
  }

  public void setStatus(Status status) {
    this.status = status;
  }

  public void setStopTimeInMillis(Long stopTimeInMillis) {
    this.stopTimeInMillis = stopTimeInMillis;
  }

  public void setSystem(String system) {
    this.system = system;
  }

  public void setVersion(Long version) {
    this.version = version;
  }

  public void setVpi(String vpi) {
    this.vpi = vpi;
  }

  @Override
  public String toString() {
    return "PingEntity{" +
        "correlationId='" + correlationId + '\'' +
        ", messageId=" + messageId +
        ", status=" + status +
        ", duration=" + duration +
        ", serviceVersion=" + serviceVersion +
        // Exclude large payload arrays from toString
        '}';
  }

}
