package com.volvo.tisp.mtdisps.integration.tests;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.JmsUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.ServiceAccessTokenUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage.Priority;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage.Ttl;

class VcmMtMessageJmsIntegrationTest {
  public static final AssetMtSchedulerMtMessageProtobuf.Priority PRIORITY = AssetMtSchedulerMtMessageProtobuf.Priority.PRIORITY_LOW;
  private static final MtMessage.AcceptedCost ACCEPTED_COST = MtMessage.AcceptedCost.LOW;
  private static final CorrelationId CORRELATION_ID = CorrelationId.ofString("f739a254-0560-11ee-be56-0242ac120002");
  private static final String DELIMITER = "-";
  private static final boolean OVERRIDE = true;
  private static final String PAYLOAD = Base64.getEncoder().encodeToString(new byte[] {1, -2, 3, -4, 5});
  private static final Priority PRIO = Priority.LOW;
  private static final String SERVICE_ACCESS_TOKEN = ServiceAccessTokenUtil.createValidServiceAccessToken(Duration.of(1, ChronoUnit.DAYS));
  private static final String SERVICE_FUNCTION = "some_service_function";
  private static final int SERVICE_ID = 1;
  private static final int SERVICE_VERSION = 1;
  private static final TrackingIdentifier TRACKING_ID = TrackingIdentifier.fromString("3DA910BCFCD54FED87BE73A5A2171C91");
  private static final Ttl TTL = Ttl.ONE_MINUTE;
  private static final Vpi VPI = Vpi.ofString("1FAC35BF1EDB1DFF91AAC0BEBC56F22B");

  private static MtMessage createMtMessage() {
    return new MtMessage()
        .withAcceptedCost(ACCEPTED_COST)
        .withCorrelationId(CORRELATION_ID.toString())
        .withServiceFunction(SERVICE_FUNCTION)
        .withOverride(OVERRIDE)
        .withPayload(PAYLOAD)
        .withPriority(PRIO)
        .withServiceAccessToken(SERVICE_ACCESS_TOKEN)
        .withServiceId(SERVICE_ID)
        .withServiceVersion(SERVICE_VERSION)
        .withTtl(TTL)
        .withVehiclePlatformId(VPI.toString())
        .withTrackingId(TRACKING_ID.toString());
  }

  private static void verifyPublishedVcmMtMessage(AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage publishedMtMessage,
      VehicleInformationEntity vehicleInformationEntity) {
    Assertions.assertEquals(vehicleInformationEntity.getPartNumber() + DELIMITER + vehicleInformationEntity.getSerialNumber(),
        publishedMtMessage.getAssetHardwareId());
    Assertions.assertEquals(CORRELATION_ID.toString(), publishedMtMessage.getCorrelationId());
    Assertions.assertFalse(publishedMtMessage.getIsSoftcar());
    Assertions.assertArrayEquals(Base64.getDecoder().decode(PAYLOAD), publishedMtMessage.getPayload().toByteArray());
    Assertions.assertEquals(PRIORITY.name(), publishedMtMessage.getPriority().toString());
    Assertions.assertEquals(SERVICE_ACCESS_TOKEN, publishedMtMessage.getServiceAccessToken());
    Assertions.assertEquals(SERVICE_ID, publishedMtMessage.getServiceId());
    Assertions.assertEquals(SERVICE_VERSION, publishedMtMessage.getServiceVersion());
    Assertions.assertEquals(60, publishedMtMessage.getTtl());
    Assertions.assertNotNull(publishedMtMessage.getTrackingId());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        LocalStackContainer localStackContainerSqsKinesis = IntegrationTestHelper.getLocalStackContainerSqsKinesis();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      String streamArn = IntegrationTestHelper.createStream(localStackContainerSqsKinesis);
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, localStackContainerSqsKinesis, Map.of("streamArn", streamArn), List.of(
          FlowType.VCM));
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        VehicleInformationRepository vehicleInformationRepository = configurableApplicationContext.getBean(VehicleInformationRepository.class);
        VehicleInformationEntity vehicleInformationEntity = vehicleInformationRepository.save(
            IntegrationTestHelper.createVehicleInformationEntity(TelematicUnitType.VCM));

        IntegrationTestHelper.awaitVPIExistsInDB(vehicleInformationRepository, vehicleInformationEntity.getVpi());

        ObjectMapper objectMapper = configurableApplicationContext.getBean(ObjectMapper.class);
        JmsUtil.publishMtMessage(objectMapper.writeValueAsString(createMtMessage()));

        verifyPublishedVcmMtMessage(
            JmsUtil.consumeAndParseAssetMtScheduledMtMessage(JmsUtil.VCM_MT_MESSAGE_OUT_QUEUE, Duration.ofSeconds(10)).orElseThrow(),
            vehicleInformationEntity);

        vehicleInformationRepository.deleteByVpi(Objects.requireNonNull(vehicleInformationEntity.getVpi()));
      }
    }
  }
}
