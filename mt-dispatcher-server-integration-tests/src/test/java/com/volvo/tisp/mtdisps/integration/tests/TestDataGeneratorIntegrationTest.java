package com.volvo.tisp.mtdisps.integration.tests;

import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;

import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.RestUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.WireMockServerWrapper;

class TestDataGeneratorIntegrationTest {
  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper);
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        RestUtil.verifyLoadGenerateHttpResponse(
            "http://localhost:48000/api/v1/vehicle-information/generate?numberOfVehicles={numberOfVehicles}&vcmPercentage={vcmPercentage}&outputFilePath={outputFilePath}",
            100, 10);
      }
    }
  }
}
