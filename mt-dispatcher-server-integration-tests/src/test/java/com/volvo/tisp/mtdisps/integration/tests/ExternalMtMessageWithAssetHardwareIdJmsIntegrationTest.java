package com.volvo.tisp.mtdisps.integration.tests;

import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.external.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.VehicleInformation;
import com.volvo.tisp.mtdisps.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.JmsUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;

class ExternalMtMessageWithAssetHardwareIdJmsIntegrationTest extends IntegrationTestBase {
  private static MtMessage createExternalMtMessage() {
    return new MtMessage()
        .withAcceptedCost(ACCEPTED_COST_EXTERNAL)
        .withAssetIdentifier(IntegrationTestHelper.ASSET_HARDWARE_ID.toString())
        .withAssetIdentifierType(MtMessage.AssetIdentifierType.ASSET_HARDWARE_ID)
        .withCorrelationId(CORRELATION_ID.toString())
        .withPayload(PAYLOAD)
        .withPriority(PRIO_EXTERNAL)
        .withServiceAccessToken(SERVICE_ACCESS_TOKEN)
        .withServiceId(SERVICE_ID)
        .withServiceVersion(SERVICE_VERSION)
        .withTtl(TTL_EXTERNAL)
        .withTrackingId(TRACKING_ID.toString());
  }

  private static void verifyExternalMtInMessageTransactionParameter(MessageTransactionParametersEntity messageTransactionParametersEntity) {
    Assertions.assertEquals(SERVICE_VERSION, messageTransactionParametersEntity.getServiceVersion());
    Assertions.assertEquals(SERVICE_ID, messageTransactionParametersEntity.getServiceId());
    Assertions.assertEquals(CORRELATION_ID.toString(), messageTransactionParametersEntity.getCorrelationId());
  }

  private static void verifyPublishedVcmMtMessage(AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage publishedMtMessage,
      VehicleInformationEntity vehicleInformationEntity) {
    Assertions.assertEquals(VehicleInformation.fromEntity(vehicleInformationEntity).assetHardwareId().toString(), publishedMtMessage.getAssetHardwareId());
    Assertions.assertEquals(CORRELATION_ID.toString(), publishedMtMessage.getCorrelationId());
    Assertions.assertFalse(publishedMtMessage.getIsSoftcar());
    Assertions.assertArrayEquals(Base64.getDecoder().decode(PAYLOAD), publishedMtMessage.getPayload().toByteArray());
    Assertions.assertEquals(PRIORITY.name(), publishedMtMessage.getPriority().toString());
    Assertions.assertEquals(SERVICE_ACCESS_TOKEN, publishedMtMessage.getServiceAccessToken());
    Assertions.assertEquals(SERVICE_ID, publishedMtMessage.getServiceId());
    Assertions.assertEquals(SERVICE_VERSION, publishedMtMessage.getServiceVersion());
    Assertions.assertEquals(600, publishedMtMessage.getTtl());
    Assertions.assertNotNull(publishedMtMessage.getTrackingId());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer();
        LocalStackContainer localStackContainerSqsKinesis = IntegrationTestHelper.getLocalStackContainerSqsKinesis()) {
      WireMock.configureFor(wireMockServerWrapper.wireMockServer().port());
      String streamArn = IntegrationTestHelper.createStream(localStackContainerSqsKinesis);

      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, localStackContainerSqsKinesis, Map.of("streamArn", streamArn),
          List.of(FlowType.VCM));
      IntegrationTestHelper.stubExternalMtStatusSubRepo();
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        VehicleInformationRepository vehicleInformationRepository = configurableApplicationContext.getBean(VehicleInformationRepository.class);
        MessageTransactionParametersRepository messageTransactionParametersRepository = configurableApplicationContext.getBean(
            MessageTransactionParametersRepository.class);
        //PRE-TEST MOCKING VQV FLOW
        VehicleInformationEntity vehicleInformationEntity = vehicleInformationRepository.save(IntegrationTestHelper.createVehicleInformationEntity(
            TelematicUnitType.VCM));
        IntegrationTestHelper.awaitVPIExistsInDB(vehicleInformationRepository, vehicleInformationEntity.getVpi());
        IntegrationTestHelper.awaitAssetIdExistsInDB(vehicleInformationRepository,
            VehicleInformation.fromEntity(vehicleInformationEntity).assetHardwareId().toString());
        ObjectMapper objectMapper = configurableApplicationContext.getBean(ObjectMapper.class);

        //EXTERNAL-MT-MESSAGE VCM Publish flow
        JmsUtil.publishExternalMtMessage(objectMapper.writeValueAsString(createExternalMtMessage()));

        verifyPublishedVcmMtMessage(JmsUtil.consumeAndParseAssetMtScheduledMtMessage(JmsUtil.VCM_MT_MESSAGE_OUT_QUEUE, Duration.ofSeconds(10)).orElseThrow(),
            vehicleInformationEntity);
        verifyExternalMtInMessageTransactionParameter(messageTransactionParametersRepository.findById(CORRELATION_ID.toString()).get());

        //External MT-STATUS FLOW
        JmsUtil.publishMtStatusMessage(createAssetMtSchedulerMtStatus());
        verifyPublishedExternalMtStatus(JmsUtil.consumeAndParseExternalMtStatus(JmsUtil.EXTERNAL_MT_STATUS_QUEUE, Duration.ofSeconds(10), objectMapper).get());
        Awaitility.await()
            .pollDelay(200, TimeUnit.MILLISECONDS)
            .atMost(3, TimeUnit.SECONDS)
            .untilAsserted(() -> verifyMessageTransactionUpdatedExpiry(messageTransactionParametersRepository.findById(CORRELATION_ID.toString())));

        //CLEANUP
        vehicleInformationRepository.deleteByVpi(vehicleInformationEntity.getVpi());
      }
    }
  }
}
