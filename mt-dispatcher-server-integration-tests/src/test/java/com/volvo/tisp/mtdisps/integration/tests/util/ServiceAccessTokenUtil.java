package com.volvo.tisp.mtdisps.integration.tests.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.nio.file.Path;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;

import org.bouncycastle.util.io.pem.PemReader;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.crypto.ECDSASigner;
import com.nimbusds.jose.jwk.Curve;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;

public class ServiceAccessTokenUtil {
  public static final Path ES384_TOKEN_ISSUER_PRIVATE_KEY_PATH = Path.of("src/test/resources/es384-private.pem");
  public static final Path ES384_TOKEN_ISSUER_PUBLIC_KEY_PATH = Path.of("src/test/resources/es384-public.pem");
  public static final URI IDP_JWKS_URI = new File("src/test/resources/jwks.json").toURI();
  public static final String KID = "test";
  public static final Path TOKEN_ISSUER_PRIVATE_KEY_PATH = Path.of("src/test/resources/tokenIssuerPrivateKey.pem");

  private ServiceAccessTokenUtil() {
    throw new IllegalStateException();
  }

  public static String createEs384ValidServiceAccessToken(Duration validityDuration, String scope, String issuer) {
    Date expiration = Date.from(Instant.now().plusMillis(validityDuration.toMillis()));
    SignedJWT signedJWT = new SignedJWT(
        new JWSHeader.Builder(JWSAlgorithm.ES384).type(JOSEObjectType.JWT).keyID(KID).build(),
        createClaimSet(scope, issuer, expiration));
    try {
      signedJWT.sign(createEs384JwsSigner());
    } catch (JOSEException e) {
      throw new RuntimeException(e);
    }
    return signedJWT.serialize();
  }

  public static String createValidServiceAccessToken(Duration validityDuration, String scope, String issuer) {
    Date expiration = Date.from(Instant.now().plusMillis(validityDuration.toMillis()));
    SignedJWT signedJWT = new SignedJWT(
        new JWSHeader.Builder(JWSAlgorithm.ES256).type(JOSEObjectType.JWT).keyID(KID).build(),
        createClaimSet(scope, issuer, expiration));
    try {
      signedJWT.sign(createJwsSigner());
    } catch (JOSEException e) {
      throw new RuntimeException(e);
    }
    return signedJWT.serialize();
  }

  public static String createValidServiceAccessToken(Duration validUntil) {
    return createValidServiceAccessToken(validUntil, "msg.read msg.write", "some-issuer");
  }

  private static JWTClaimsSet createClaimSet(String scope, String issuer, Date expiration) {
    return new JWTClaimsSet.Builder()
        .issueTime(Date.from(Instant.now()))
        .expirationTime(expiration)
        .claim("exp_mh", expiration)
        .notBeforeTime(Date.from(Instant.now()))
        .claim("scope", scope)
        .issuer(issuer)
        .build();
  }

  private static JWSSigner createEs384JwsSigner() {
    try {
      KeyFactory keyFactory = KeyFactory.getInstance("EC");
      PrivateKey privateKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(
          new PemReader(new InputStreamReader(new FileInputStream(ES384_TOKEN_ISSUER_PRIVATE_KEY_PATH.toFile()))).readPemObject().getContent()));

      return new ECDSASigner(privateKey, Curve.P_384);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private static JWSSigner createJwsSigner() {
    try {
      KeyFactory keyFactory = KeyFactory.getInstance("EC");
      PrivateKey tokenIssuerPrivateKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(
          new PemReader(new InputStreamReader(new FileInputStream(TOKEN_ISSUER_PRIVATE_KEY_PATH.toFile()))).readPemObject().getContent()));
      return new ECDSASigner(tokenIssuerPrivateKey, Curve.P_256);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}
