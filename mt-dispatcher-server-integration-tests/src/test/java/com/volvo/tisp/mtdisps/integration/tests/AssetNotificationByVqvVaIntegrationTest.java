package com.volvo.tisp.mtdisps.integration.tests;

import java.nio.file.Path;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.cache.CacheManager;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;

import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.impl.model.ChassisId;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.OperationalStatus;
import com.volvo.tisp.mtdisps.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.JmsUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.va.VnnMessageTypesV1;
import com.wirelesscar.va.VonMessageTypesV1;

class AssetNotificationByVqvVaIntegrationTest {
  public static final Path REMOVE_PATH = Path.of("src/test/resources/vqv/vqv-remove.json");
  public static final Path UPDATE_NULL_STATUS_PATH = Path.of("src/test/resources/vqv/vqv-update-null-status.json");
  public static final Path UPDATE_PATH = Path.of("src/test/resources/vqv/vqv-update.json");
  private static final ChassisId CHASSIS_ID = ChassisId.fromComponents("SCAR", "205045");
  private static final Vpi VPI = Vpi.ofString("1FAC35BF1EDB1DFF91AAC0BEBC56F22B");

  private static void awaitAndVerifyPersistedVehicleInformation(CacheManager cacheManager, VehicleInformationRepository vehicleInformationRepository,
      String vpi, boolean operationalStatus, long operationalVersion) {
    VehicleInformationEntity vehicleInformationEntity = awaitDataToBePersisted(vehicleInformationRepository, vpi);

    Assertions.assertEquals(CHASSIS_ID.toString(), vehicleInformationEntity.getChassisId());
    Assertions.assertEquals(VPI.toString(), vehicleInformationEntity.getVpi());
    Assertions.assertNotNull(vehicleInformationEntity.getCreatedAt());
    Assertions.assertEquals(operationalVersion, vehicleInformationEntity.getOperationalVersion());
    Assertions.assertEquals(operationalStatus, vehicleInformationEntity.isOperational());
    Assertions.assertEquals("456", vehicleInformationEntity.getPartNumber());
    Assertions.assertEquals("123", vehicleInformationEntity.getSerialNumber());
    Assertions.assertTrue(vehicleInformationEntity.isSoftCar());
    Assertions.assertEquals("TGW3", vehicleInformationEntity.getTelematicUnitSubType());
    Assertions.assertEquals(TelematicUnitType.TGW, vehicleInformationEntity.getTelematicUnitType());
    Assertions.assertNotNull(vehicleInformationEntity.getUpdatedAt());

    verifyNoCacheForVpi(cacheManager, vpi);//Verify cache evicted if present
  }

  private static void awaitAndVerifyVehicleInformationIsDeleted(CacheManager cacheManager, VehicleInformationRepository vehicleInformationRepository,
      String vpi) {
    Awaitility.await().atMost(Duration.ofSeconds(3)).until(() -> vehicleInformationRepository.findByVpi(vpi).isEmpty());

    verifyNoCacheForVpi(cacheManager, vpi);//Verify cached evicted if present
  }

  private static VehicleInformationEntity awaitDataToBePersisted(VehicleInformationRepository vehicleInformationRepository, String vpi) {
    Awaitility.await().atMost(Duration.ofSeconds(3)).until(() -> vehicleInformationRepository.findByVpi(vpi).isPresent());
    return vehicleInformationRepository.findByVpi(vpi).get();
  }

  private static Optional<VehicleInformationEntity> getCachedVehicleInformation(CacheManager cacheManager, String vpi) {
    return Optional.ofNullable(cacheManager.getCache(VehicleInformationRepository.OPERATIONAL_VEHICLE_INFORMATION_BY_VPI_CACHE))
        .map(cache -> cache.get(vpi, VehicleInformationEntity.class));
  }

  private static void verifyNoCacheForVpi(CacheManager cacheManager, String vpi) {
    Assertions.assertEquals(Optional.empty(), getCachedVehicleInformation(cacheManager, vpi));
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, null, Map.of(), List.of(FlowType.VCM, FlowType.TGW));
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        CacheManager cacheManager = configurableApplicationContext.getBean(CacheManager.class);
        VehicleInformationRepository vehicleInformationRepository = configurableApplicationContext.getBean(VehicleInformationRepository.class);

        JmsUtil.publishVqvNotification(IntegrationTestHelper.getInputString(UPDATE_PATH)); //evicts Cache if present
        awaitAndVerifyPersistedVehicleInformation(cacheManager, vehicleInformationRepository, VPI.toString(),
            OperationalStatus.OPERATIONAL.getOperationalStatus(), 0);

        JmsUtil.publishVqvNotification(IntegrationTestHelper.getInputString(UPDATE_NULL_STATUS_PATH)); //evicts Cache if present
        awaitAndVerifyPersistedVehicleInformation(cacheManager, vehicleInformationRepository, VPI.toString(),
            OperationalStatus.OPERATIONAL.getOperationalStatus(), 0);//OperationalStatus should not be updated via VQV UPDATE rather by VA Notification.

        JmsUtil.publishOperationalNotification(IntegrationTestHelper.createVehicleNonOperationalNotification(),
            VnnMessageTypesV1.VEHICLE_NON_OPERATIONAL_NOTIFICATION_JMS_TYPE, VnnMessageTypesV1.MAJOR_VERSION);
        awaitAndVerifyPersistedVehicleInformation(cacheManager, vehicleInformationRepository, VPI.toString(),
            OperationalStatus.NON_OPERATIONAL.getOperationalStatus(), 1);

        JmsUtil.publishOperationalNotification(IntegrationTestHelper.createVehicleOperationalNotification(),
            VonMessageTypesV1.VEHICLE_OPERATIONAL_NOTIFICATION_JMS_TYPE, VonMessageTypesV1.MAJOR_VERSION);
        awaitAndVerifyPersistedVehicleInformation(cacheManager, vehicleInformationRepository, VPI.toString(),
            OperationalStatus.OPERATIONAL.getOperationalStatus(), 2);

        JmsUtil.publishVqvNotification(IntegrationTestHelper.getInputString(REMOVE_PATH)); //evicts Cache if present
        awaitAndVerifyVehicleInformationIsDeleted(cacheManager, vehicleInformationRepository, VPI.toString());
      }
    }
  }
}
