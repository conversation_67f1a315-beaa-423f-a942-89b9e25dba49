package com.volvo.tisp.mtdisps.integration.tests;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.JmsUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus;

class TgwMtMessageJmsIntegrationTest extends IntegrationTestBase {
  private static MtMessage createMtMessage() {
    return new MtMessage()
        .withAcceptedCost(ACCEPTED_COST)
        .withCorrelationId(CORRELATION_ID.toString())
        .withServiceFunction(SERVICE_FUNCTION)
        .withOverride(OVERRIDE)
        .withPayload(PAYLOAD)
        .withPriority(PRIO)
        .withServiceAccessToken(SERVICE_ACCESS_TOKEN)
        .withServiceId(SERVICE_ID)
        .withServiceVersion(SERVICE_VERSION)
        .withTtl(TTL)
        .withVehiclePlatformId(IntegrationTestHelper.VPI.toString())
        .withTrackingId(TRACKING_ID.toString());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, null, Map.of(), List.of(FlowType.TGW));
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);
      IntegrationTestHelper.stubMtStatusSubRepo();

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        VehicleInformationRepository vehicleInformationRepository = configurableApplicationContext.getBean(VehicleInformationRepository.class);
        MessageTransactionParametersRepository messageTransactionParametersRepository = configurableApplicationContext.getBean(
            MessageTransactionParametersRepository.class);
        //PRE-TEST MOCKING VQV FLOW
        VehicleInformationEntity vehicleInformationEntity = vehicleInformationRepository.save(IntegrationTestHelper.createVehicleInformationEntity(
            TelematicUnitType.TGW));
        IntegrationTestHelper.awaitVPIExistsInDB(vehicleInformationRepository, vehicleInformationEntity.getVpi());
        IntegrationTestHelper.awaitChassisIdExistsInDB(vehicleInformationRepository, vehicleInformationEntity.getChassisId());
        ObjectMapper objectMapper = configurableApplicationContext.getBean(ObjectMapper.class);

        //MT-MESSAGE FlOW
        JmsUtil.publishMtMessage(objectMapper.writeValueAsString(createMtMessage()));
        verifyPublishedTgwMtMessage(
            JmsUtil.consumeAndUnmarshalMessage(JmsUtil.TGW_MT_MESSAGE_IN_QUEUE, Duration.ofSeconds(10), com.wirelesscar.tce.api.v2.MtMessage.class)
                .orElseThrow(),
            false);

        //MT-STATUS FLOW
        JmsUtil.publishMtStatusMessage(createTGWMtStatusMessage());
        verifyPublishedMtStatus(
            JmsUtil.consumeAndParseInternalMtStatus(JmsUtil.INTERNAL_MT_STATUS_QUEUE, Duration.ofSeconds(10), objectMapper, MtStatus.class).orElseThrow());
        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
          verifyMessageTransactionUpdatedExpiry(messageTransactionParametersRepository.findById(CORRELATION_ID.toString()));
        });

        //CLEANUP
        vehicleInformationRepository.deleteByVpi(Objects.requireNonNull(vehicleInformationEntity.getVpi()));
      }
    }
  }
}
