package com.volvo.tisp.mtdisps.integration.tests;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.impl.model.AssetHardwareId;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.JmsUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.ServiceAccessTokenUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage.Priority;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage.Ttl;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessageWithAssetHardwareId;

import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;

class VcmMtMessageWithAssetHardwareIdIntegrationTest {
  public static final AssetMtSchedulerMtMessageProtobuf.Priority PRIORITY = AssetMtSchedulerMtMessageProtobuf.Priority.PRIORITY_LOW;
  private static final MtMessage.AcceptedCost ACCEPTED_COST = MtMessage.AcceptedCost.LOW;
  private static final AssetHardwareId ASSET_HARDWARE_ID = AssetHardwareId.fromString("12345678-87654321");
  private static final CorrelationId CORRELATION_ID = CorrelationId.ofString("f739a254-0560-11ee-be56-0242ac120002");
  private static final String DELIMITER = "-";
  private static final boolean OVERRIDE = true;
  private static final String PAYLOAD = Base64.getEncoder().encodeToString(new byte[] {1, -2, 3, -4, 5});
  private static final Priority PRIO = Priority.LOW;
  private static final String SERVICE_ACCESS_TOKEN = ServiceAccessTokenUtil.createValidServiceAccessToken(Duration.of(1, ChronoUnit.DAYS));
  private static final String SERVICE_FUNCTION = "some_service_function";
  private static final int SERVICE_ID = 1;
  private static final int SERVICE_VERSION = 1;
  private static final TrackingIdentifier TRACKING_ID = TrackingIdentifier.fromString("3DA910BCFCD54FED87BE73A5A2171C91");
  private static final Ttl TTL = Ttl.ONE_MINUTE;

  private static MtMessageWithAssetHardwareId createMtMessageWithAssetHardwareId() {
    return new MtMessageWithAssetHardwareId()
        .withAcceptedCost(ACCEPTED_COST)
        .withCorrelationId(CORRELATION_ID.toString())
        .withServiceFunction(SERVICE_FUNCTION)
        .withOverride(OVERRIDE)
        .withPayload(PAYLOAD)
        .withPriority(PRIO)
        .withServiceAccessToken(SERVICE_ACCESS_TOKEN)
        .withServiceId(SERVICE_ID)
        .withServiceVersion(SERVICE_VERSION)
        .withTtl(TTL)
        .withAssetHardwareId(ASSET_HARDWARE_ID.toString())
        .withTrackingId(TRACKING_ID.toString());
  }

  private static void verifyPublishedVcmMtMessage(AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage publishedMtMessage) {
    Assertions.assertEquals(
        VcmMtMessageWithAssetHardwareIdIntegrationTest.ASSET_HARDWARE_ID.partNumber().value() + DELIMITER
            + VcmMtMessageWithAssetHardwareIdIntegrationTest.ASSET_HARDWARE_ID.serialNumber().value(), publishedMtMessage.getAssetHardwareId());
    Assertions.assertEquals(CORRELATION_ID.toString(), publishedMtMessage.getCorrelationId());
    Assertions.assertFalse(publishedMtMessage.getIsSoftcar());
    Assertions.assertArrayEquals(Base64.getDecoder().decode(PAYLOAD), publishedMtMessage.getPayload().toByteArray());
    Assertions.assertEquals(PRIORITY.name(), publishedMtMessage.getPriority().toString());
    Assertions.assertEquals(SERVICE_ACCESS_TOKEN, publishedMtMessage.getServiceAccessToken());
    Assertions.assertEquals(SERVICE_ID, publishedMtMessage.getServiceId());
    Assertions.assertEquals(SERVICE_VERSION, publishedMtMessage.getServiceVersion());
    Assertions.assertEquals(60, publishedMtMessage.getTtl());
    Assertions.assertNotNull(publishedMtMessage.getTrackingId());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper ignored = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        LocalStackContainer localStackContainer = IntegrationTestHelper.getLocalStackContainerSqsKinesis();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      SqsClient sqsClient = IntegrationTestHelper.getSqsClient(localStackContainer);
      String streamArn = IntegrationTestHelper.createStream(localStackContainer);
      System.setProperty("unidentified-assets.allowed", "true");
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, localStackContainer, Map.of("streamArn", streamArn), List.of(
          FlowType.VCM));
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        ObjectMapper objectMapper = configurableApplicationContext.getBean(ObjectMapper.class);
        JmsUtil.publishMtMessageWithAssetHardwareId(objectMapper.writeValueAsString(createMtMessageWithAssetHardwareId()));
        verifyPublishedVcmMtMessage(JmsUtil.consumeAndParseAssetMtScheduledMtMessage(JmsUtil.VCM_MT_MESSAGE_OUT_QUEUE, Duration.ofSeconds(10)).orElseThrow());

        sqsClient.sendMessage(
            SendMessageRequest.builder()
                .queueUrl(IntegrationTestHelper.SQS_MT_MESSAGE_QUEUE_WITH_ASSET_HARDWARE_ID)
                .messageBody(objectMapper.writeValueAsString(createMtMessageWithAssetHardwareId()))
                .build());
        verifyPublishedVcmMtMessage(JmsUtil.consumeAndParseAssetMtScheduledMtMessage(JmsUtil.VCM_MT_MESSAGE_OUT_QUEUE, Duration.ofSeconds(10)).orElseThrow());
      }
    }
  }
}
