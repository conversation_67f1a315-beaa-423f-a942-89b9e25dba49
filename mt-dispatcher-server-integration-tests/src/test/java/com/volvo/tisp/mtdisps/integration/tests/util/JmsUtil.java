package com.volvo.tisp.mtdisps.integration.tests.util;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.time.Duration;
import java.util.Optional;
import java.util.function.Supplier;

import jakarta.jms.BytesMessage;
import jakarta.jms.Connection;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.DeliveryMode;
import jakarta.jms.Destination;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.MessageConsumer;
import jakarta.jms.MessageProducer;
import jakarta.jms.Session;
import jakarta.jms.TextMessage;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;

import javax.xml.transform.stream.StreamSource;

import org.apache.activemq.artemis.jms.client.ActiveMQConnectionFactory;
import org.apache.activemq.artemis.jms.client.ActiveMQTextMessage;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.mtdisps.impl.consumer.mtmessage.ExternalMtMessageJmsController;
import com.volvo.tisp.mtdisps.impl.consumer.mtmessage.MtMessageJmsController;
import com.volvo.tisp.mtdisps.impl.consumer.mtstatus.TgwMtStatusJmsController;
import com.volvo.tisp.mtdisps.impl.consumer.mtstatus.VcmMtStatusJmsController;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtStatusProtobuf;
import com.volvo.tisp.vc.mt.message.client.json.v1.InternalMtStatus;
import com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;
import com.wirelesscar.vqv.v1.api.UpdateNotificationMessageType;

public final class JmsUtil {
  public static final String EXTERNAL_MT_STATUS_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT.EXTERNAL-MT-STATUS.OUT";
  public static final String INTERNAL_MT_STATUS_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT.INTERNAL-MT-STATUS.OUT";
  public static final String TGW_MT_MESSAGE_IN_QUEUE = "LOCAL.LOCAL.LOCAL.TGWMTS." + MtMessageJmsController.MT_MESSAGE;
  public static final String TGW_MT_STATUS_IN_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT." + TgwMtStatusJmsController.TGW_MT_STATUS;
  public static final String VCM_MT_MESSAGE_OUT_QUEUE = "LOCAL.LOCAL.LOCAL.AMTSS.MT-MESSAGES";
  public static final String VCM_MT_STATUS_IN_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT." + VcmMtStatusJmsController.VCM_MT_STATUS;
  static final String AMQ_URL = "tcp://localhost:43717";
  private static final String EXTERNAL_MT_MESSAGE_IN_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT." + ExternalMtMessageJmsController.EXTERNAL_MT_MESSAGES;
  private static final String MT_MESSAGE_IN_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT." + MtMessageJmsController.MT_MESSAGE;
  private static final Optional<String> REPLY_TO = Optional.of("LOCAL.LOCAL.LOCAL.COMPSHRT.REPLY-TO");
  private static final String VA_IN_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT.VA.OPERATIONAL-STATUS.IN";
  private static final String VQV_IN_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT.VQV.IN";

  private JmsUtil() {
    throw new IllegalStateException();
  }

  public static Optional<AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage> consumeAndParseAssetMtScheduledMtMessage(String fullQueueName,
      Duration timeout) throws Exception {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    Optional<Message> optionalMessage = consumeOneMessage(connectionFactory, fullQueueName, timeout);

    if (optionalMessage.isEmpty()) {
      return Optional.empty();
    }
    return Optional.of(parseJsonAssetScheduledMtMessage(optionalMessage.get()));
  }

  public static Optional<com.volvo.tisp.external.mt.message.client.json.v1.MtStatus> consumeAndParseExternalMtStatus(String fullQueueName, Duration timeout,
      ObjectMapper objectMapper) throws Exception {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    Optional<Message> optionalMessage = consumeOneMessage(connectionFactory, fullQueueName, timeout);

    if (optionalMessage.isEmpty()) {
      return Optional.empty();
    }
    return Optional.of(parseJsonExternalMtStatus(optionalMessage.get(), objectMapper));
  }

  public static <T extends InternalMtStatus> Optional<T> consumeAndParseInternalMtStatus(String fullQueueName, Duration timeout, ObjectMapper objectMapper,
      Class<T> clazz) throws Exception {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    Optional<Message> optionalMessage = consumeOneMessage(connectionFactory, fullQueueName, timeout);

    if (optionalMessage.isEmpty()) {
      return Optional.empty();
    }
    return Optional.of(parseJsonInternalMtStatus(optionalMessage.get(), objectMapper, clazz));
  }

  public static <T> Optional<T> consumeAndUnmarshalMessage(String fullQueueName, Duration timeout, Class<T> clazz) throws Exception {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    Optional<Message> optionalMessage = consumeOneMessage(connectionFactory, fullQueueName, timeout);

    if (optionalMessage.isEmpty()) {
      return Optional.empty();
    }
    return Optional.of(unmarshal(optionalMessage.get(), clazz));
  }

  public static void publishExternalMtMessage(String externalMtMessage) throws Exception {
    publishJsonMessage(
        EXTERNAL_MT_MESSAGE_IN_QUEUE,
        externalMtMessage,
        com.volvo.tisp.external.mt.message.client.json.v1.MessageTypes.EXTERNAL_MT_MESSAGE,
        com.volvo.tisp.external.mt.message.client.json.v1.MessageTypes.VERSION_1_0,
        REPLY_TO);
  }

  public static void publishMtMessage(String mtMessage) throws Exception {
    publishJsonMessage(
        MT_MESSAGE_IN_QUEUE,
        mtMessage,
        MessageTypes.MT_MESSAGE,
        MessageTypes.VERSION_1_0,
        Optional.empty());
  }

  public static void publishMtMessageWithAssetHardwareId(String mtMessage) throws Exception {
    publishJsonMessage(
        MT_MESSAGE_IN_QUEUE,
        mtMessage,
        MessageTypes.MT_MESSAGE_WITH_ASSET_HARDWARE_ID,
        MessageTypes.VERSION_1_0,
        Optional.empty());
  }

  public static void publishMtStatusMessage(MtStatusMessage mtStatusMessage) throws Exception {
    sendRequest(
        marshalToXml(mtStatusMessage),
        TGW_MT_STATUS_IN_QUEUE,
        MessageTypesJms.TCE_MTSTATUS_MESSAGE_TYPE,
        MessageTypesJms.VERSION_2_0);
  }

  public static void publishMtStatusMessage(AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus mtStatusMessage) {
    publishProtoByteMessage(
        VCM_MT_STATUS_IN_QUEUE,
        mtStatusMessage.toByteArray(),
        com.volvo.tisp.vc.amtss.client.protobuf.MessageTypes.AMTSS_ASSET_MT_SCHEDULER_MT_STATUS,
        com.volvo.tisp.vc.amtss.client.protobuf.MessageTypes.VERSION_1_0);
  }

  public static void publishOperationalNotification(Supplier supplierFunction, String messageType, String messageVersion) throws Exception {
    sendRequest(
        marshalToXml(supplierFunction.get()),
        VA_IN_QUEUE,
        messageType,
        messageVersion);
  }

  public static void publishVqvNotification(String body) throws Exception {
    sendRequest(
        body,
        VQV_IN_QUEUE,
        UpdateNotificationMessageType.VIEW_NOTIFICATION_MESSAGE_TYPE,
        UpdateNotificationMessageType.VIEW_NOTIFICATION_MESSAGE_TYPE_VERSION);
  }

  private static Optional<Message> consumeOneMessage(ConnectionFactory connectionFactory, String fullQueueName, Duration timeout) throws JMSException {
    try (Connection connection = connectionFactory.createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        Destination destination = session.createQueue(fullQueueName);

        try (MessageConsumer messageConsumer = session.createConsumer(destination)) {
          Message message = messageConsumer.receive(timeout.toMillis());

          return Optional.ofNullable(message);
        }
      }
    }
  }

  private static ActiveMQConnectionFactory createActiveMQConnectionFactory() {
    return new ActiveMQConnectionFactory(AMQ_URL);
  }

  private static <T> String marshalToXml(T object) throws JAXBException, IOException {
    JAXBContext jaxbContext = JAXBContext.newInstance(object.getClass());
    Marshaller marshaller = jaxbContext.createMarshaller();

    try (StringWriter stringWriter = new StringWriter()) {
      marshaller.marshal(object, stringWriter);
      return stringWriter.toString();
    }
  }

  private static AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage parseJsonAssetScheduledMtMessage(Message message)
      throws JMSException, InvalidProtocolBufferException {
    BytesMessage bytesMessage = (BytesMessage) message;
    byte[] bytes = new byte[(int) bytesMessage.getBodyLength()];
    bytesMessage.readBytes(bytes);
    bytesMessage.reset();

    return AssetMtSchedulerMtMessageProtobuf.AssetMtSchedulerMtMessage.parseFrom(bytes);
  }

  private static com.volvo.tisp.external.mt.message.client.json.v1.MtStatus parseJsonExternalMtStatus(Message message, ObjectMapper objectMapper)
      throws JMSException, JsonProcessingException {
    TextMessage textMessage = (TextMessage) message;
    return objectMapper.readValue(textMessage.getText(), com.volvo.tisp.external.mt.message.client.json.v1.MtStatus.class);
  }

  private static <T extends InternalMtStatus> T parseJsonInternalMtStatus(Message message, ObjectMapper objectMapper, Class<T> clazz)
      throws JMSException, JsonProcessingException {
    TextMessage textMessage = (TextMessage) message;
    return objectMapper.readValue(textMessage.getText(), clazz);
  }

  private static void publishJsonMessage(String fullQueueName, String mtMessage, String messageType, String version, Optional<String> replyToHeader)
      throws Exception {
    try (Connection connection = createActiveMQConnectionFactory().createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        TextMessage textMessage = session.createTextMessage(mtMessage);

        textMessage.setStringProperty("GIMMsgType", messageType);
        textMessage.setStringProperty("GIMMsgTypeVersion", version);

        if (replyToHeader.isPresent()) {
          textMessage.setJMSReplyTo(session.createQueue(replyToHeader.get()));
        }

        Destination destination = session.createQueue(fullQueueName);

        try (MessageProducer messageProducer = session.createProducer(destination)) {
          messageProducer.setDeliveryMode(DeliveryMode.NON_PERSISTENT);
          messageProducer.send(textMessage);
        }
      }
    }
  }

  private static void publishMessage(ConnectionFactory connectionFactory, String fullQueueName, String body, String messageType, String version)
      throws JMSException {
    try (Connection connection = connectionFactory.createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        TextMessage textMessage = session.createTextMessage(body);

        textMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), messageType);
        textMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), version);

        Destination destination = session.createQueue(fullQueueName);

        try (MessageProducer messageProducer = session.createProducer(destination)) {
          messageProducer.setDeliveryMode(DeliveryMode.NON_PERSISTENT);
          messageProducer.send(textMessage);
        }
      }
    }
  }

  private static void publishProtoByteMessage(String fullQueueName, byte[] bytes, String messageType, String version) {
    try (Connection connection = createActiveMQConnectionFactory().createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        BytesMessage bytesMessage = session.createBytesMessage();
        bytesMessage.writeBytes(bytes);

        bytesMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), messageType);
        bytesMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), version);

        Destination destination = session.createQueue(fullQueueName);

        try (MessageProducer messageProducer = session.createProducer(destination)) {
          messageProducer.setDeliveryMode(DeliveryMode.NON_PERSISTENT);
          messageProducer.send(bytesMessage);
        }
      }
    } catch (JMSException e) {
      throw new RuntimeException(e);
    }
  }

  private static void sendRequest(String body, String fullQueueName, String messageType, String messageVersion) throws Exception {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    publishMessage(connectionFactory, fullQueueName, body, messageType, messageVersion);
  }

  private static <T> T unmarshal(Message message, Class<T> type) throws JAXBException, JMSException {
    TextMessage textMessage = (ActiveMQTextMessage) message;
    JAXBContext jaxbContext = JAXBContext.newInstance(type);
    Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

    try (StringReader stringReader = new StringReader(textMessage.getText())) {
      return unmarshaller.unmarshal(new StreamSource(stringReader), type).getValue();
    }
  }
}
