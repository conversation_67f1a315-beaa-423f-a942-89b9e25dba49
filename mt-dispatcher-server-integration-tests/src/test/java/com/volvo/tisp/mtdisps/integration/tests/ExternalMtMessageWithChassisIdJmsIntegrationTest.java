package com.volvo.tisp.mtdisps.integration.tests;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.external.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.JmsUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.WireMockServerWrapper;

class ExternalMtMessageWithChassisIdJmsIntegrationTest extends IntegrationTestBase {
  private static com.volvo.tisp.external.mt.message.client.json.v1.MtMessage createExternalMtMessage() {
    return new com.volvo.tisp.external.mt.message.client.json.v1.MtMessage()
        .withAcceptedCost(ACCEPTED_COST_EXTERNAL)
        .withAssetIdentifier(IntegrationTestHelper.CHASSIS_ID.value())
        .withAssetIdentifierType(MtMessage.AssetIdentifierType.CHASSIS_ID)
        .withCorrelationId(CORRELATION_ID.toString())
        .withPayload(PAYLOAD)
        .withPriority(PRIO_EXTERNAL)
        .withServiceAccessToken(SERVICE_ACCESS_TOKEN)
        .withServiceId(SERVICE_ID)
        .withServiceVersion(SERVICE_VERSION)
        .withTtl(TTL_EXTERNAL)
        .withTrackingId(TRACKING_ID.toString());
  }

  private static void verifyExternalMtInMessageTransactionParameter(MessageTransactionParametersEntity messageTransactionParametersEntity) {
    Assertions.assertEquals(SERVICE_VERSION, messageTransactionParametersEntity.getServiceVersion());
    Assertions.assertEquals(SERVICE_ID, messageTransactionParametersEntity.getServiceId());
    Assertions.assertEquals(CORRELATION_ID.toString(), messageTransactionParametersEntity.getCorrelationId());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      WireMock.configureFor(wireMockServerWrapper.wireMockServer().port());
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, null, Map.of(), List.of(FlowType.TGW, FlowType.VCM));
      IntegrationTestHelper.stubExternalMtStatusSubRepo();
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        VehicleInformationRepository vehicleInformationRepository = configurableApplicationContext.getBean(VehicleInformationRepository.class);
        MessageTransactionParametersRepository messageTransactionParametersRepository = configurableApplicationContext.getBean(
            MessageTransactionParametersRepository.class);
        //PRE-TEST MOCKING VQV FLOW
        VehicleInformationEntity vehicleInformationEntity = vehicleInformationRepository.save(IntegrationTestHelper.createVehicleInformationEntity(
            TelematicUnitType.TGW));
        IntegrationTestHelper.awaitVPIExistsInDB(vehicleInformationRepository, vehicleInformationEntity.getVpi());
        IntegrationTestHelper.awaitChassisIdExistsInDB(vehicleInformationRepository, vehicleInformationEntity.getChassisId());
        ObjectMapper objectMapper = configurableApplicationContext.getBean(ObjectMapper.class);

        //EXTERNAL-MT-MESSAGE TGW Publish flow
        JmsUtil.publishExternalMtMessage(objectMapper.writeValueAsString(createExternalMtMessage()));
        verifyPublishedTgwMtMessage(
            JmsUtil.consumeAndUnmarshalMessage(JmsUtil.TGW_MT_MESSAGE_IN_QUEUE, Duration.ofSeconds(10), com.wirelesscar.tce.api.v2.MtMessage.class)
                .orElseThrow(),
            true);
        verifyExternalMtInMessageTransactionParameter(messageTransactionParametersRepository.findById(CORRELATION_ID.toString()).orElseThrow());

        //External MT-STATUS FLOW
        JmsUtil.publishMtStatusMessage(createTGWMtStatusMessage());
        verifyPublishedExternalMtStatus(
            JmsUtil.consumeAndParseExternalMtStatus(JmsUtil.EXTERNAL_MT_STATUS_QUEUE, Duration.ofSeconds(10), objectMapper).orElseThrow());
        Awaitility.await()
            .pollDelay(200, TimeUnit.MILLISECONDS)
            .atMost(3, TimeUnit.SECONDS)
            .untilAsserted(() -> verifyMessageTransactionUpdatedExpiry(messageTransactionParametersRepository.findById(CORRELATION_ID.toString())));

        //CLEANUP
        vehicleInformationRepository.deleteByVpi(Objects.requireNonNull(vehicleInformationEntity.getVpi()));
      }
    }
  }
}
