package com.volvo.tisp.mtdisps.integration.tests.util;

import java.io.IOException;
import java.io.StringReader;
import java.nio.file.Files;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.interfaces.ECPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;

import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;

import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.jwk.Curve;
import com.nimbusds.jose.jwk.ECKey;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.KeyUse;

public final class JwksGeneratorUtil {
  private JwksGeneratorUtil() {
    throw new IllegalStateException("utility class");
  }

  public static String generateJwks() throws IOException, NoSuchAlgorithmException, InvalidKeySpecException {
    ECPublicKey ecPublicKey = (ECPublicKey) readPublicKeyFromPem();

    ECKey ecKey = new ECKey.Builder(
        Curve.P_384, ecPublicKey)
        .keyID(ServiceAccessTokenUtil.KID)
        .algorithm(JWSAlgorithm.ES384)
        .keyUse(KeyUse.SIGNATURE)
        .build();

    return new JWKSet(ecKey).toJSONObject(true).toString();
  }

  private static PublicKey readPublicKeyFromPem() throws IOException, NoSuchAlgorithmException, InvalidKeySpecException {
    try (PemReader pemReader = new PemReader(new StringReader(Files.readString(ServiceAccessTokenUtil.ES384_TOKEN_ISSUER_PUBLIC_KEY_PATH)))) {
      PemObject pemObject = pemReader.readPemObject();
      X509EncodedKeySpec keySpec = new X509EncodedKeySpec(pemObject.getContent());
      KeyFactory keyFactory = KeyFactory.getInstance("EC");
      return keyFactory.generatePublic(keySpec);
    }
  }
}
