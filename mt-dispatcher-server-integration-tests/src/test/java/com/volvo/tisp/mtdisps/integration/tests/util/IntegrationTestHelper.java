package com.volvo.tisp.mtdisps.integration.tests.util;

import java.io.IOException;
import java.math.BigInteger;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;

import org.apache.activemq.artemis.core.config.Configuration;
import org.apache.activemq.artemis.core.config.impl.ConfigurationImpl;
import org.apache.activemq.artemis.core.server.embedded.EmbeddedActiveMQ;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;
import org.testcontainers.utility.DockerImageName;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.Slf4jNotifier;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.google.common.collect.ArrayListMultimap;
import com.volvo.tisp.external.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.database.entity.VehicleInformationEntity;
import com.volvo.tisp.mtdisps.impl.model.AssetHardwareId;
import com.volvo.tisp.mtdisps.impl.model.ChassisId;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.PartNumber;
import com.volvo.tisp.mtdisps.impl.model.SerialNumber;
import com.volvo.tisp.subscriptionrepository.client.Destination;
import com.volvo.tisp.subscriptionrepository.client.SubscriptionStubber;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.va.v1.VehicleNonoperationalNotification;
import com.wirelesscar.va.v1.VehicleOperationalNotification;

import io.opentelemetry.api.GlobalOpenTelemetry;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kinesis.KinesisAsyncClient;
import software.amazon.awssdk.services.kinesis.model.CreateStreamRequest;
import software.amazon.awssdk.services.kinesis.model.DescribeStreamSummaryRequest;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest;

public final class IntegrationTestHelper {
  public static final ChassisId CHASSIS_ID = new ChassisId("SCAR-202450");
  public static final PartNumber PART_NUMBER = new PartNumber("10001000");
  public static final SerialNumber SERIAL_NUMBER = new SerialNumber("20002000");
  public static final AssetHardwareId ASSET_HARDWARE_ID = new AssetHardwareId(PART_NUMBER, SERIAL_NUMBER);
  public static final String SQS_MT_MESSAGE_QUEUE_WITH_ASSET_HARDWARE_ID = "incoming-mt-message-with-asset-hardware-id-queue";
  public static final String SQS_MT_MESSAGE_QUEUE_WITH_VPI = "mtdisps-in-mt-message-queue";
  public static final Vpi VPI = Vpi.ofString("1FAC35BF1EDB1DFF91AAC0BEBC56F22B");
  private static final String INTEGRATION_LOG_STREAM = "integration-log-stream";
  private static final String LOCALSTACK_SQS_HOST = "http://localhost:4566/000000000000/";

  private IntegrationTestHelper() {
    throw new IllegalStateException();
  }

  public static void awaitAssetIdExistsInDB(VehicleInformationRepository vehicleInformationRepository, String assetId) {
    Awaitility.await()
        .atMost(Duration.ofSeconds(3))
        .until(() -> vehicleInformationRepository.findByPartNumberAndSerialNumberAndOperationalIsTrue(AssetHardwareId.fromString(assetId).partNumber().value(),
            AssetHardwareId.fromString(assetId).serialNumber().value()).isPresent());
  }

  public static void awaitChassisIdExistsInDB(VehicleInformationRepository vehicleInformationRepository, String chassisId) {
    Awaitility.await().atMost(Duration.ofSeconds(3)).until(() -> vehicleInformationRepository.findByChassisIdAndOperationalIsTrue(chassisId).isPresent());
  }

  public static void awaitVPIExistsInDB(VehicleInformationRepository vehicleInformationRepository, String vpi) {
    Awaitility.await().atMost(Duration.ofSeconds(3)).until(() -> vehicleInformationRepository.findByVpi(vpi).isPresent());
  }

  public static EmbeddedActiveMqWrapper createAndStartEmbeddedActiveMqWrapper() throws Exception {
    EmbeddedActiveMQ embeddedActiveMQ = new EmbeddedActiveMQ();
    embeddedActiveMQ.setConfiguration(createActiveMQConfiguration());
    embeddedActiveMQ.start();
    return new EmbeddedActiveMqWrapper(embeddedActiveMQ);
  }

  public static WireMockServerWrapper createAndStartWireMockServer() {
    WireMockServer wireMockServer = createWireMockServer();
    wireMockServer.start();

    return new WireMockServerWrapper(wireMockServer);
  }

  public static void createQueue(String queueName, SqsClient sqsClient) {
    sqsClient.createQueue(
        CreateQueueRequest.builder()
            .queueName(queueName)
            .build());
  }

  public static String createStream(LocalStackContainer localStackContainer)
      throws ExecutionException, InterruptedException {
    getKinesisAsyncClient(localStackContainer)
        .createStream(CreateStreamRequest.builder()
            .streamName(INTEGRATION_LOG_STREAM)
            .shardCount(1)
            .build())
        .get();

    return getKinesisAsyncClient(localStackContainer).describeStreamSummary(DescribeStreamSummaryRequest.builder().streamName(INTEGRATION_LOG_STREAM).build())
        .get()
        .streamDescriptionSummary()
        .streamARN();
  }

  public static VehicleInformationEntity createVehicleInformationEntity(TelematicUnitType telematicType) {
    VehicleInformationEntity vehicleInformationEntity = new VehicleInformationEntity();
    vehicleInformationEntity.setChassisId(CHASSIS_ID.value());
    vehicleInformationEntity.setOperational(true);
    vehicleInformationEntity.setPartNumber(PART_NUMBER.value());
    vehicleInformationEntity.setSerialNumber(SERIAL_NUMBER.value());
    vehicleInformationEntity.setSoftCar(false);
    vehicleInformationEntity.setTelematicUnitSubType(telematicType.name());
    vehicleInformationEntity.setTelematicUnitType(telematicType);
    vehicleInformationEntity.setVersion(1);
    vehicleInformationEntity.setVpi(VPI.toString());
    return vehicleInformationEntity;
  }

  public static Supplier<VehicleNonoperationalNotification> createVehicleNonOperationalNotification() {
    return () -> {
      VehicleNonoperationalNotification vehicleNonoperationalNotification = new VehicleNonoperationalNotification();
      vehicleNonoperationalNotification.setVehiclePlatformId(VPI.toString());
      vehicleNonoperationalNotification.setVehicleVersion(BigInteger.ONE);
      return vehicleNonoperationalNotification;
    };
  }

  public static Supplier<VehicleOperationalNotification> createVehicleOperationalNotification() {
    return () -> {
      VehicleOperationalNotification vehicleOperationalNotification = new VehicleOperationalNotification();
      vehicleOperationalNotification.setVehiclePlatformId(VPI.toString());
      vehicleOperationalNotification.setVehicleVersion(BigInteger.TWO);
      return vehicleOperationalNotification;
    };
  }

  public static String getInputString(Path path) throws IOException {
    return Files.readString(path, StandardCharsets.UTF_8);
  }

  public static KinesisAsyncClient getKinesisAsyncClient(LocalStackContainer kinesisLocalStackContainer) {
    return KinesisAsyncClient.builder()
        .region(Region.of(kinesisLocalStackContainer.getRegion()))
        .endpointOverride(kinesisLocalStackContainer.getEndpointOverride(LocalStackContainer.Service.KINESIS))
        .build();
  }

  public static LocalStackContainer getLocalStackContainerSqsKinesis() {
    System.setProperty("aws.accessKeyId", "FAKE");
    System.setProperty("aws.secretAccessKey", "FAKE");
    System.setProperty("aws.region", Region.US_EAST_1.toString());

    LocalStackContainer localStackContainer = new LocalStackContainer(
        DockerImageName.parse("artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/localstack/localstack:latest")
            .asCompatibleSubstituteFor("localstack/localstack:latest")).withServices(LocalStackContainer.Service.SQS, LocalStackContainer.Service.KINESIS);
    localStackContainer.start();

    SqsClient sqsClient = getSqsClient(localStackContainer);
    createQueue(SQS_MT_MESSAGE_QUEUE_WITH_ASSET_HARDWARE_ID, sqsClient);
    createQueue(SQS_MT_MESSAGE_QUEUE_WITH_VPI, sqsClient);
    return localStackContainer;
  }

  public static SqsClient getSqsClient(LocalStackContainer localStackContainer) {
    URI endpointOverride = localStackContainer.getEndpointOverride(LocalStackContainer.Service.SQS);
    return SqsClient.builder()
        .region(Region.of(localStackContainer.getRegion()))
        .endpointOverride(endpointOverride)
        .build();
  }

  public static void mockAppConfig(MongoDBContainer mongoDBContainer, WireMockServerWrapper wireMockServerWrapper) {
    mockAppConfig(mongoDBContainer, wireMockServerWrapper, null, Map.of(), List.of());
  }

  public static void mockAppConfig(MongoDBContainer mongoDBContainer, WireMockServerWrapper wireMockServerWrapper, LocalStackContainer localStackContainer,
      Map<String, String> configMap, Collection<FlowType> enabledFlows) {
    final int wireMockPortNumber = wireMockServerWrapper.wireMockServer().port();
    WireMock.configureFor(wireMockPortNumber);

    System.setProperty("spring.artemis.broker-url", JmsUtil.AMQ_URL);

    System.setProperty("management.influx.metrics.export.uri", "http://localhost:" + wireMockPortNumber);
    System.setProperty("management.influx.metrics.export.retention-policy", "30days");

    System.setProperty("spring.data.mongodb.uri", mongoDBContainer.getReplicaSetUrl());
    System.setProperty("servicediscovery.active-by-default", "true");

    if (localStackContainer != null) {
      System.setProperty("sqs.endpoint-override", String.valueOf(localStackContainer.getEndpointOverride(LocalStackContainer.Service.SQS)));
    }

    System.setProperty("token-issuer.public-key.path", "classpath:tokenIssuerPublicKey.pem");
    System.setProperty("kinesis.data.stream.arn", configMap.getOrDefault("streamArn", "streamArn"));
    System.setProperty("spring.cloud.aws.region.static", "us-east-1");
    System.setProperty("sqs.region", "us-east-1");

    System.setProperty("servicediscovery.subr", "http://localhost:" + wireMockPortNumber + "/");
    System.setProperty("servicediscovery.vqv", "http://localhost:" + wireMockPortNumber + "/");

    System.setProperty("sqs.incoming-mt-message.queue-url", LOCALSTACK_SQS_HOST + SQS_MT_MESSAGE_QUEUE_WITH_VPI);
    System.setProperty("sqs.incoming-mt-message-with-asset-hardware-id.queue-url", LOCALSTACK_SQS_HOST + SQS_MT_MESSAGE_QUEUE_WITH_ASSET_HARDWARE_ID);

    if (enabledFlows.contains(FlowType.VCM)) {
      System.setProperty("spring.profiles.include", "vcm");
      System.setProperty("jms.amtss.mt.message.queue.name", "LOCAL.LOCAL.LOCAL.AMTSS.MT-MESSAGES");
    }

    if (enabledFlows.contains(FlowType.TGW)) {
      System.setProperty("spring.profiles.include", "tgw");
      System.setProperty("jms.tgwmts.mt.message.queue.name", "LOCAL.LOCAL.LOCAL.TGWMTS.MT-MESSAGES");
    }
  }

  public static ConfigurableApplicationContext runSpringApplication(Class<?> configurationClass) {
    GlobalOpenTelemetry.resetForTest();
    return SpringApplication.run(new Class<?>[] {configurationClass}, new String[0]);
  }

  public static MongoDBContainer startMongoContainer() {
    MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:6.0");
    mongoDBContainer.withReuse(true).start();
    return mongoDBContainer;
  }

  public static void stubExternalMtStatusSubRepo() {
    Destination destinationExternal = new Destination(MessageTypes.EXTERNAL_MT_STATUS, MessageTypes.VERSION_1_0,
        "activemq:queue:" + JmsUtil.EXTERNAL_MT_STATUS_QUEUE, "destinationExternal");

    ArrayListMultimap<String, Object> optionsMap = ArrayListMultimap.create();

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(MessageTypes.EXTERNAL_MT_STATUS)
        .withOptions(optionsMap)
        .thenMessageShouldBeDeliveredTo(destinationExternal);
  }

  public static void stubMtStatusSubRepo() {
    Destination destinationForMtStatus = new Destination(com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.MT_STATUS,
        com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.VERSION_1_0, "activemq:queue:" + JmsUtil.INTERNAL_MT_STATUS_QUEUE,
        "destinationForMtStatus");

    ArrayListMultimap<String, Object> optionsMap = ArrayListMultimap.create();

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.MT_STATUS)
        .withOptions(optionsMap)
        .thenMessageShouldBeDeliveredTo(destinationForMtStatus);
  }

  public static void stubMtStatusWithAssetHardwareIdSubRepo() {
    Destination destinationForMtStatusWithAssetHardwareId = new Destination(
        com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.MT_STATUS_WITH_ASSET_HARDWARE_ID,
        com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.VERSION_1_0, "activemq:queue:" + JmsUtil.INTERNAL_MT_STATUS_QUEUE,
        "destinationForMtStatusWithAssetHardwareId");

    ArrayListMultimap<String, Object> optionsMap = ArrayListMultimap.create();

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes.MT_STATUS_WITH_ASSET_HARDWARE_ID)
        .withOptions(optionsMap)
        .thenMessageShouldBeDeliveredTo(destinationForMtStatusWithAssetHardwareId);
  }

  public static void stubVqv(WireMockServerWrapper wireMockServerWrapper) {
    WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer();
    wireMockServer.stubFor(WireMock.post("/query").willReturn(createVqvResponse()));
    wireMockServer.stubFor(WireMock.post("/filter").willReturn(createVqvResponse()));
    wireMockServer.stubFor(WireMock.post("/view").willReturn(createVqvResponse()));
  }

  private static Configuration createActiveMQConfiguration() throws Exception {
    return new ConfigurationImpl()
        .addAcceptorConfiguration("tcp", JmsUtil.AMQ_URL)
        .setPersistenceEnabled(false)
        .setSecurityEnabled(false);
  }

  private static ResponseDefinitionBuilder createVqvResponse() {
    return WireMock
        .aResponse()
        .withStatus(200)
        .withBody("{}")
        .withHeader("Content-Type", "application/json");
  }

  private static WireMockServer createWireMockServer() {
    WireMockConfiguration wireMockConfiguration = new WireMockConfiguration().dynamicPort().notifier(new Slf4jNotifier(false));
    return new WireMockServer(wireMockConfiguration);
  }
}
