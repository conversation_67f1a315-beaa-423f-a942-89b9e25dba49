package com.volvo.tisp.mtdisps.integration.tests;

import static com.volvo.tisp.mtdisps.integration.tests.IntegrationTestBase.CORRELATION_ID;
import static com.volvo.tisp.mtdisps.integration.tests.IntegrationTestBase.DESCRIPTION;
import static com.volvo.tisp.mtdisps.integration.tests.IntegrationTestBase.TRACKING_ID;
import static com.volvo.tisp.mtdisps.integration.tests.IntegrationTestBase.createAssetMtSchedulerMtStatus;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.mtdisps.database.api.MessageTransactionParametersRepository;
import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;
import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.impl.model.MtMessageType;
import com.volvo.tisp.mtdisps.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.JmsUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus;
import com.volvo.tisp.vc.mt.message.client.json.v1.Status;

class MtStatusIntegrationTest {
  private static MessageTransactionParametersEntity createMessageTransactionParameters(Vpi vpi, Instant instant) {
    MessageTransactionParametersEntity messageTransactionParametersEntity = new MessageTransactionParametersEntity();
    messageTransactionParametersEntity.setCorrelationId(CORRELATION_ID.toString());
    messageTransactionParametersEntity.setExpireAt(instant);
    messageTransactionParametersEntity.setAssetHardwareId(IntegrationTestHelper.ASSET_HARDWARE_ID.toString());
    messageTransactionParametersEntity.setServiceFunction("MY_SERVICE");
    messageTransactionParametersEntity.setVpi(vpi.toString());
    messageTransactionParametersEntity.setMtMessageType(MtMessageType.INTERNAL_MT_MESSAGE.name());

    return messageTransactionParametersEntity;
  }

  private static void verifyMessageTransactionUpdatedExpiry(Optional<MessageTransactionParametersEntity> byId) {
    byId.ifPresent(messageTransactionParametersEntity -> Assertions.assertTrue(
        messageTransactionParametersEntity.getExpireAt().compareTo(Instant.now(Clock.systemUTC())) <= 0));
  }

  private static void verifyPublishedMtStatus(com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus actual) {
    Assertions.assertEquals(CORRELATION_ID.toString(), actual.getCorrelationId());
    Assertions.assertEquals(DESCRIPTION, actual.getDescription());
    Assertions.assertEquals(Status.DELIVERED, actual.getStatus());
    Assertions.assertEquals(TRACKING_ID.toString(), actual.getTrackingId());
    Assertions.assertEquals(IntegrationTestHelper.VPI.toString(), actual.getVehiclePlatformId());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        LocalStackContainer sqsLocalStackContainer = IntegrationTestHelper.getLocalStackContainerSqsKinesis();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, sqsLocalStackContainer, Map.of(), List.of(FlowType.VCM));
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);
      IntegrationTestHelper.stubMtStatusSubRepo();

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        ObjectMapper objectMapper = configurableApplicationContext.getBean(ObjectMapper.class);
        MessageTransactionParametersRepository messageTransactionParametersRepository = configurableApplicationContext.getBean(
            MessageTransactionParametersRepository.class);

        Instant instant = Instant.now(Clock.systemUTC()).plus(10, TimeUnit.MINUTES.toChronoUnit());
        messageTransactionParametersRepository.save(createMessageTransactionParameters(IntegrationTestHelper.VPI, instant));

        JmsUtil.publishMtStatusMessage(createAssetMtSchedulerMtStatus());

        verifyPublishedMtStatus(
            JmsUtil.consumeAndParseInternalMtStatus(JmsUtil.INTERNAL_MT_STATUS_QUEUE, Duration.ofSeconds(20), objectMapper, MtStatus.class).orElseThrow());
        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
          verifyMessageTransactionUpdatedExpiry(messageTransactionParametersRepository.findById(CORRELATION_ID.toString()));
        });
      }
    }
  }
}
