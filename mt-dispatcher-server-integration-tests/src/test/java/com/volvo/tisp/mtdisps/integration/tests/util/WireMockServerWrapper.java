package com.volvo.tisp.mtdisps.integration.tests.util;

import java.io.Closeable;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record WireMockServerWrapper(WireMockServer wireMockServer) implements Closeable {
  public WireMockServerWrapper {
    Validate.notNull(wireMockServer, "wireMockServer");

  }

  @Override
  public void close() {
    wireMockServer.stop();
  }
}