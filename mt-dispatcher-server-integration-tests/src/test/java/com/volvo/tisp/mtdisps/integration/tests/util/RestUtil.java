package com.volvo.tisp.mtdisps.integration.tests.util;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public final class RestUtil {
  private RestUtil() {
    throw new IllegalArgumentException();
  }

  public static void verifyLoadGenerateHttpResponse(String urlString, int totalVehicle, int vcmPercentage) throws IOException {
    Map<String, String> requestParams = new HashMap<>();
    requestParams.put("numberOfVehicles", String.valueOf(totalVehicle));
    requestParams.put("vcmPercentage", String.valueOf(vcmPercentage));
    requestParams.put("outputFilePath", "vehicle.csv");

    ResponseEntity<String> responseEntity = RestUtil.createRestTemplate().exchange(urlString, HttpMethod.GET, null, String.class, requestParams);
    Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    Assertions.assertTrue(responseEntity.getBody().contains(totalVehicle + " vehicleInformation have been generated"));

    //clean up
    Files.delete(Path.of("vehicle.csv"));
  }

  public static void verifyOkHttpResponse(String urlString) {
    ResponseEntity<String> responseEntity = RestUtil.createRestTemplate().exchange(urlString, HttpMethod.GET, null, String.class);
    Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode(), responseEntity::getBody);
  }

  private static RestTemplate createRestTemplate() {
    return new RestTemplate();
  }
}
