package com.volvo.tisp.mtdisps.integration.tests;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.mtdisps.database.api.VehicleInformationRepository;
import com.volvo.tisp.mtdisps.database.entity.TelematicUnitType;
import com.volvo.tisp.mtdisps.impl.conf.AppConfig;
import com.volvo.tisp.mtdisps.impl.model.FlowType;
import com.volvo.tisp.mtdisps.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.JmsUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.ServiceAccessTokenUtil;
import com.volvo.tisp.mtdisps.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;

class IdpIntegrationTest extends IntegrationTestBase {
  private static final String IDPM2M_JWKS_URI_PROPERTY = "idpm2m.jwks-uri";
  private static final String IDPM2M_STUB_PATH = "/idp/m2m";

  private static MtMessage createMtMessage(String serviceAccessToken) {
    return new MtMessage()
        .withAcceptedCost(ACCEPTED_COST)
        .withCorrelationId(CORRELATION_ID.toString())
        .withServiceFunction(SERVICE_FUNCTION)
        .withOverride(OVERRIDE)
        .withPayload(PAYLOAD)
        .withPriority(PRIO)
        .withServiceAccessToken(serviceAccessToken)
        .withServiceId(SERVICE_ID)
        .withServiceVersion(SERVICE_VERSION)
        .withTtl(TTL)
        .withVehiclePlatformId(IntegrationTestHelper.VPI.toString())
        .withTrackingId(TRACKING_ID.toString());
  }

  private static void publishMtMessage() throws Exception {
    JmsUtil.publishMtMessage(new ObjectMapper().writeValueAsString(
        createMtMessage(ServiceAccessTokenUtil.createEs384ValidServiceAccessToken(Duration.ofDays(1), "mh.w svc.42", "http://idpm2m-v1"))));
  }

  private static void stubIdpJwksSourceEndpoints(WireMockServerWrapper wireMockServerWrapper) throws IOException {
    WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer();

    String idpSourceUriV1 = String.format(Locale.ROOT, "http://localhost:%d%s", wireMockServer.port(), IDPM2M_STUB_PATH);

    System.setProperty(IDPM2M_JWKS_URI_PROPERTY, idpSourceUriV1);

    String jsonString = Files.readString(Path.of(ServiceAccessTokenUtil.IDP_JWKS_URI), StandardCharsets.UTF_8);

    ResponseDefinitionBuilder responseDefinitionBuilder = WireMock
        .aResponse()
        .withStatus(200)
        .withBody(jsonString)
        .withHeader("Content-Type", "application/json");

    wireMockServer.stubFor(WireMock.get(IDPM2M_STUB_PATH).willReturn(responseDefinitionBuilder));
  }

  private static void verifyDispatchedMtMessage() throws Exception {
    verifyPublishedTgwMtMessage(
        JmsUtil.consumeAndUnmarshalMessage(JmsUtil.TGW_MT_MESSAGE_IN_QUEUE, Duration.ofSeconds(5), com.wirelesscar.tce.api.v2.MtMessage.class).orElseThrow(),
        false);
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        MongoDBContainer mongoDBContainer = IntegrationTestHelper.startMongoContainer()) {
      IntegrationTestHelper.mockAppConfig(mongoDBContainer, wireMockServerWrapper, null, Map.of(), List.of(FlowType.TGW));
      IntegrationTestHelper.stubVqv(wireMockServerWrapper);

      stubIdpJwksSourceEndpoints(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        VehicleInformationRepository vehicleInformationRepository = configurableApplicationContext.getBean(VehicleInformationRepository.class);
        vehicleInformationRepository.save(IntegrationTestHelper.createVehicleInformationEntity(TelematicUnitType.TGW));

        publishMtMessage();
        verifyDispatchedMtMessage();
      } finally {
        System.clearProperty(IDPM2M_JWKS_URI_PROPERTY);
      }
    }
  }
}
