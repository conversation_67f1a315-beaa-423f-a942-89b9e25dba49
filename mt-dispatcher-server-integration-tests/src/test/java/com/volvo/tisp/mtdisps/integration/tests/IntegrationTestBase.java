package com.volvo.tisp.mtdisps.integration.tests;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Optional;
import java.util.StringJoiner;

import org.junit.jupiter.api.Assertions;

import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.mtdisps.database.entity.MessageTransactionParametersEntity;
import com.volvo.tisp.mtdisps.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.mtdisps.integration.tests.util.ServiceAccessTokenUtil;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtMessageProtobuf;
import com.volvo.tisp.vc.amtss.client.protobuf.AssetMtSchedulerMtStatusProtobuf;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus;
import com.volvo.tisp.vc.mt.message.client.json.v1.Status;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.api.v2.SrpLevel;
import com.wirelesscar.tce.api.v2.SrpOption;

public class IntegrationTestBase {
  public static final MtMessage.AcceptedCost ACCEPTED_COST = MtMessage.AcceptedCost.LOW;
  public static final com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.AcceptedCost ACCEPTED_COST_EXTERNAL = com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.AcceptedCost.LOW;
  public static final CorrelationId CORRELATION_ID = CorrelationId.ofString("f739a254-0560-11ee-be56-0242ac120002");
  public static final String DESCRIPTION = "delivered";
  public static final String HANDLE = "some_handle";
  public static final boolean OVERRIDE = true;
  public static final String PAYLOAD = Base64.getEncoder().encodeToString(new byte[] {1, -2, 3, -4, 5});
  public static final MtMessage.Priority PRIO = MtMessage.Priority.LOW;
  public static final AssetMtSchedulerMtMessageProtobuf.Priority PRIORITY = AssetMtSchedulerMtMessageProtobuf.Priority.PRIORITY_LOW;
  public static final com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.Priority PRIO_EXTERNAL = com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.Priority.LOW;
  public static final String SERVICE_ACCESS_TOKEN = ServiceAccessTokenUtil.createValidServiceAccessToken(Duration.of(1, ChronoUnit.DAYS));
  public static final String SERVICE_FUNCTION = "some_service_function";
  public static final int SERVICE_ID = 42;
  public static final int SERVICE_VERSION = 1;
  public static final String QUEUE_ID = new StringJoiner("-").add(SERVICE_ID + "-" + SERVICE_VERSION).add(SERVICE_FUNCTION).toString();
  public static final ReplyTo TGW_REPLY_TO = ReplyTo.ofString("LOCAL.LOCAL.LOCAL.COMPSHRT.TGW-MT-STATUS");
  public static final TrackingIdentifier TRACKING_ID = TrackingIdentifier.fromString("3DA910BCFCD54FED87BE73A5A2171C91");
  public static final MtMessage.Ttl TTL = MtMessage.Ttl.TEN_MINUTES;
  public static final String HINT = new StringJoiner("-").add(TTL.name()).add(PRIO.name()).toString();
  public static final com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.Ttl TTL_EXTERNAL = com.volvo.tisp.external.mt.message.client.json.v1.MtMessage.Ttl.TEN_MINUTES;

  public static AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus createAssetMtSchedulerMtStatus() {
    return AssetMtSchedulerMtStatusProtobuf.AssetMtSchedulerMtStatus.newBuilder()
        .setDescription(DESCRIPTION)
        .setStatus(AssetMtSchedulerMtStatusProtobuf.Status.DELIVERED)
        .setCorrelationId(CORRELATION_ID.toString())
        .setTrackingId(TRACKING_ID.toString())
        .build();
  }

  public static MtStatusMessage createTGWMtStatusMessage() {
    MtStatusMessage mtStatusMessage = new MtStatusMessage();
    mtStatusMessage.setCorrelationId(CORRELATION_ID.toString());
    mtStatusMessage.setHandle(HANDLE);
    mtStatusMessage.setStatus("delivered");
    mtStatusMessage.setVehiclePlatformId(IntegrationTestHelper.VPI.toString());

    return mtStatusMessage;
  }

  public static void verifyMessageTransactionUpdatedExpiry(Optional<MessageTransactionParametersEntity> byId) {
    byId.ifPresent(
        messageTransactionParametersEntity -> Assertions.assertTrue(messageTransactionParametersEntity.getExpireAt().isBefore(Instant.now(Clock.systemUTC()))));
  }

  public static void verifyPublishedExternalMtStatus(com.volvo.tisp.external.mt.message.client.json.v1.MtStatus actual) {
    Assertions.assertEquals(CORRELATION_ID.toString(), actual.getCorrelationId());
    Assertions.assertEquals(com.volvo.tisp.external.mt.message.client.json.v1.MtStatus.Status.DELIVERED, actual.getStatus());
    Assertions.assertNotNull(actual.getTrackingId());
  }

  public static void verifyPublishedMtStatus(MtStatus actual) {
    Assertions.assertEquals(CORRELATION_ID.toString(), actual.getCorrelationId());
    Assertions.assertEquals(Status.DELIVERED, actual.getStatus());
    Assertions.assertNotNull(actual.getTrackingId());
    Assertions.assertEquals(IntegrationTestHelper.VPI.toString(), actual.getVehiclePlatformId());
  }

  public static void verifyPublishedTgwMtMessage(com.wirelesscar.tce.api.v2.MtMessage publishedMtMessage, boolean isExternalMtMessage) {
    Assertions.assertEquals(IntegrationTestHelper.VPI.toString(), publishedMtMessage.getVehiclePlatformId());
    Assertions.assertArrayEquals(Base64.getDecoder().decode(PAYLOAD), publishedMtMessage.getPayload());
    verifySchedulerOption(publishedMtMessage.getSchedulerOption(), isExternalMtMessage);
    verifySrpOption(publishedMtMessage.getSrpOption());
    verifyMtStatusReplyOption(publishedMtMessage.getMtStatusReplyOption());
  }

  private static void verifyMtStatusReplyOption(MtStatusReplyOption actual) {
    Assertions.assertEquals(TGW_REPLY_TO.toString(), actual.getReplyDestination());
    Assertions.assertEquals(CORRELATION_ID.toString(), actual.getCorrelationId());
  }

  private static void verifySchedulerOption(SchedulerOption actual, boolean isExternalMtMessage) {
    if (isExternalMtMessage) {
      Assertions.assertEquals(SERVICE_ID + "-" + SERVICE_VERSION + "-EXTERNAL", actual.getQueueId());
      Assertions.assertEquals("NORMAL", actual.getEnqueueingType());
    } else {
      Assertions.assertEquals(QUEUE_ID, actual.getQueueId());
      Assertions.assertEquals("OVERRIDE", actual.getEnqueueingType());
    }
    Assertions.assertEquals(HINT, actual.getHint());
    Assertions.assertEquals(4, actual.getPriority());
  }

  private static void verifySrpOption(SrpOption actual) {
    Assertions.assertEquals(4, actual.getPriority());
    Assertions.assertEquals(SERVICE_ID, actual.getDstService());
    Assertions.assertEquals(SERVICE_VERSION, actual.getDstVersion());
    Assertions.assertEquals(SrpLevel.SRP_11, actual.getSrpLevel());
  }
}
