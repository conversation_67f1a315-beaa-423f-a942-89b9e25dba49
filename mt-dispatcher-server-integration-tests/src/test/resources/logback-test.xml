<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%date{HH:mm:ss.SSS} [%thread] [%.-1level] %logger{48} - %message%n</pattern>
    </encoder>
  </appender>
  <appender name="NULL" class="ch.qos.logback.core.helpers.NOPAppender"/>

  <logger name="org.apache" level="ERROR"/>
  <logger name="org.springframework" level="ERROR"/>
  <logger name="tc" level="ERROR"/>
  <logger name="io.awspring" level="OFF"/>
  <logger name="software.amazon" level="OFF"/>

  <root level="INFO">
    <appender-ref ref="STDOUT"/>
  </root>
</configuration>
