<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>mo-router-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>mo-router-server-app</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>mo-router-server-impl</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <mainClass>com.volvo.tisp.moros.impl.conf.AppConfig</mainClass>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
