version: '3.8'
services:
  influxdb:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/influxdb:2.7.12@sha256:b357bde62be5ae1ce1a0dea4f04e7194a62ae82fb4ccc2f4993df3f5209a40d0
    ports:
      - 8086:8086
  mongodb_container:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/mongo:8.0@sha256:a6bda40d00e56682aeaa1bfc88e024b7dd755782c575c02760104fe02010f94f
    environment:
      MONGO_INITDB_DATABASE: testmongo
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: rootpassword
    ports:
      - 27017:27017
  sqs:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/localstack/localstack:4.7.0@sha256:12253acd9676770e9bd31cbfcf17c5ca6fd7fb5c0c62f3c46dd701f20304260c
    environment:
      - SERVICES=sqs
    volumes:
      - ./localstack_bootstrap/sqs_bootstrap.sh:/etc/localstack/init/ready.d/init-aws.sh
    ports:
      - 4566:4566
  amq:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/rmohr/activemq:5.15.9@sha256:b5669f141ac5c455b1010b90d348fe78289bba7e2db5659484d99736b696593c
    ports:
      - 8161:8161
      - 61616:61616
