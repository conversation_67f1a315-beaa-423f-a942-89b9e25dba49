package com.volvo.tisp.vehiclepingservice.swap.common;

import org.jdom2.Element;

/**
 * Title: ASN Null class.
 *
 * <p>Description: Part of ASN
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ASNNull extends ASNConstrainedValue {

  private static final long serialVersionUID = 1L;

  public ASNNull() {
    setAsnObjectName("Null");
  }

  public ASNValue createObject() throws ASNException {
    return new ASNNull();
  }

  public void decode(PERStream stream) throws ASNException {
  }

  public void decode(Element oElement) throws ASNException {
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    oElem.addContent("Null");
    oElement.addContent(oElem);
  }

  public void encode(PERStream stream) throws ASNException {
  }
}
