//
// BeefyMessage.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vehiclepingservice.swap.v1.ping;

import java.io.UnsupportedEncodingException;

import org.jdom2.Element;

import com.volvo.tisp.vehiclepingservice.swap.common.ASNBoolean;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNException;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNInteger;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNOctetString;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNSequence;
import com.volvo.tisp.vehiclepingservice.swap.common.PERStream;

/**
 * BeefyMessage.
 */
public class BeefyMessage extends ASNSequence {
  private static final long serialVersionUID = 1L;
  ASNBoolean m_copyPayload = new ASNBoolean();
  ASNBoolean m_exists__id = new ASNBoolean();
  ASNInteger m_id = new ASNInteger();
  ASNOctetString m_payload = new ASNOctetString();
  ASNBoolean m_responseExpected = new ASNBoolean();
  ASNInteger m_responseSize = new ASNInteger();

  public BeefyMessage() {
    setAsnObjectName("BeefyMessage");
    m_exists__id.setAsnObjectName("exists__id");
    m_responseExpected.setAsnObjectName("responseExpected");
    m_copyPayload.setAsnObjectName("copyPayload");
    m_responseSize.setAsnObjectName("responseSize");
    m_responseSize.setFixedConstraint(0L, 65335L);
    m_id.setAsnObjectName("id");
    m_id.setFixedConstraint(0L, 4294967295L);
    m_payload.setAsnObjectName("payload");
    m_payload.setFixedConstraint(0L, 65335L);
  }

  public void decode(PERStream stream) throws ASNException {
    super.decode(stream);
    m_exists__id.decode(stream);
    m_responseExpected.decode(stream);
    m_copyPayload.decode(stream);
    m_responseSize.decode(stream);
    if (m_exists__id.get()) {
      m_id.decode(stream);
    }
    m_payload.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    super.encode(stream);
    m_exists__id.encode(stream);
    m_responseExpected.encode(stream);
    m_copyPayload.encode(stream);
    m_responseSize.encode(stream);
    if (m_exists__id.get()) {
      m_id.encode(stream);
    }
    m_payload.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_exists__id.encode(oElem);
    m_responseExpected.encode(oElem);
    m_copyPayload.encode(oElem);
    m_responseSize.encode(oElem);
    if (m_exists__id.get())
      m_id.encode(oElem);
    m_payload.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_exists__id.encodedSize();
    length += m_responseExpected.encodedSize();
    length += m_copyPayload.encodedSize();
    length += m_responseSize.encodedSize();
    if (m_exists__id.get())
      length += m_id.encodedSize();
    length += m_payload.encodedSize();
    return length;
  }

  public boolean getCopyPayload() {
    return m_copyPayload.get();
  }

  public boolean getExists__id() {
    return m_exists__id.get();
  }

  public long getId() {
    return m_id.get();
  }

  public byte[] getPayload() {
    return m_payload.getAsByteArray();
  }

  public String getPayload(String charSetName) throws UnsupportedEncodingException {
    return m_payload.get(charSetName);
  }

  public String getPayloadAsString() {
    return m_payload.getAsString();
  }

  public boolean getResponseExpected() {
    return m_responseExpected.get();
  }

  public long getResponseSize() {
    return m_responseSize.get();
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_exists__id.readExternal(in);
    m_responseExpected.readExternal(in);
    m_copyPayload.readExternal(in);
    m_responseSize.readExternal(in);
    if (m_exists__id.get()) {
      m_id.readExternal(in);
    }
    m_payload.readExternal(in);
  }

  public void setCopyPayload(boolean value) {
    m_copyPayload.set(value);
  }

  public void setExists__id(boolean value) {
    m_exists__id.set(value);
  }

  public void setId(long value) {
    m_id.set(value);
    m_exists__id.set(true);
  }

  public void setPayload(byte[] value) {
    m_payload.set(value);
  }

  public void setPayload(String value) {
    m_payload.set(value);
  }

  public void setPayload(String value, String charSetName) throws UnsupportedEncodingException {
    m_payload.set(value, charSetName);
  }

  public void setResponseExpected(boolean value) {
    m_responseExpected.set(value);
  }

  public void setResponseSize(long value) {
    m_responseSize.set(value);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_exists__id.writeExternal(out);
    m_responseExpected.writeExternal(out);
    m_copyPayload.writeExternal(out);
    m_responseSize.writeExternal(out);
    if (m_exists__id.get()) {
      m_id.writeExternal(out);
    }
    m_payload.writeExternal(out);
  }
}

// End of BeefyMessage.java
