package com.volvo.tisp.vehiclepingservice.swap.common;

public class ASNN<PERSON>ricString extends AbstractKnownMultiplierString {
  public ASNNumericString() {
    this("NumericString");
  }

  public ASNNumericString(String name) {
    super(name);
  }

  private static String getNumericString(byte[] buf) throws ASNException {
    StringBuffer sb = new StringBuffer();

    for (int i = 0; i < buf.length; ++i) {
      sb.append(getNumericStringChar(buf[i]));
    }

    return sb.toString();
  }

  private static byte[] getNumericStringBytes(String string) throws ASNException {
    if (string != null && string.length() != 0) {
      byte[] buf = new byte[string.length()];

      for (int i = 0; i < string.length(); ++i) {
        buf[i] = getNumericStringIndex(string.charAt(i));
      }

      return buf;
    } else {
      return new byte[0];
    }
  }

  private static char getNumericString<PERSON>har(int c) throws ASNEx<PERSON> {
    switch (c) {
      case 0:
        return ' ';
      default:
        if (c >= 1 && c <= 10) {
          return (char) (c + 47);
        } else {
          throw new ASNException(
              "Character not in NumericString alphabet: 0x"
                  + Integer.toHexString(c)
                  + " ("
                  + c
                  + ")");
        }
    }
  }

  private static byte getNumericStringIndex(char c) throws ASNException {
    switch (c) {
      case ' ':
        return 0;
      default:
        if (c >= 48 && c <= 57) {
          return (byte) (c - 47);
        } else {
          throw new ASNException(
              "Character not in NumericString alphabet: 0x"
                  + Integer.toHexString(c)
                  + " ('"
                  + c
                  + "')");
        }
    }
  }

  public ASNValue createObject() throws ASNException {
    return new ASNNumericString();
  }

  protected void assertValidValue() throws ASNException {
    for (int i = 0; i < this.value.length(); ++i) {
      getNumericStringIndex(this.value.charAt(i));
    }
  }

  protected void decodeValue(PERStream stream, int size) throws ASNException {
    byte[] numBuf = new byte[size];

    for (int i = 0; i < size; ++i) {
      numBuf[i] = stream.decodeBits(4);
    }

    this.value = getNumericString(numBuf);
  }

  protected void encodeValue(PERStream stream) throws ASNException {
    byte[] numBuf = getNumericStringBytes(this.get());

    for (int i = 0; i < numBuf.length; ++i) {
      stream.encodeBits(numBuf[i], 4);
    }
  }

  protected int getEncodedValueSizeInBits() {
    return this.value.length() * 4;
  }
}
