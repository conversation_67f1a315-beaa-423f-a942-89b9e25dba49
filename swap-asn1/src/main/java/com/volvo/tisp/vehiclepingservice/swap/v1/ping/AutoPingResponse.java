//
// AutoPingResponse.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vehiclepingservice.swap.v1.ping;

import org.jdom2.Element;

import com.volvo.tisp.vehiclepingservice.swap.common.ASNBoolean;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNException;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNInteger;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNSequence;
import com.volvo.tisp.vehiclepingservice.swap.common.PERStream;

/**
 * AutoPingResponse.
 */
public class AutoPingResponse extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNBoolean m_exists__id = new ASNBoolean();
  ASNInteger m_id = new ASNInteger();

  public AutoPingResponse() {
    setAsnObjectName("AutoPingResponse");
    m_exists__id.setAsnObjectName("exists__id");
    m_id.setAsnObjectName("id");
    m_id.setFixedConstraint(0L, 4294967295L);
  }

  public void decode(PERStream stream) throws ASNException {
    super.decode(stream);
    m_exists__id.decode(stream);
    if (m_exists__id.get()) {
      m_id.decode(stream);
    }
  }

  public void encode(PERStream stream) throws ASNException {
    super.encode(stream);
    m_exists__id.encode(stream);
    if (m_exists__id.get()) {
      m_id.encode(stream);
    }
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_exists__id.encode(oElem);
    if (m_exists__id.get())
      m_id.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_exists__id.encodedSize();
    if (m_exists__id.get())
      length += m_id.encodedSize();
    return length;
  }

  public boolean getExists__id() {
    return m_exists__id.get();
  }

  public long getId() {
    return m_id.get();
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_exists__id.readExternal(in);
    if (m_exists__id.get()) {
      m_id.readExternal(in);
    }
  }

  public void setExists__id(boolean value) {
    m_exists__id.set(value);
  }

  public void setId(long value) {
    m_id.set(value);
    m_exists__id.set(true);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_exists__id.writeExternal(out);
    if (m_exists__id.get()) {
      m_id.writeExternal(out);
    }
  }
}