package com.volvo.tisp.vehiclepingservice.swap.common;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.output.Format;
import org.jdom2.output.XMLOutputter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.lang.SuppressForbidden;

/**
 * Title: ASN Value base class.
 *
 * <p>Description: Part of ASN. The base class of all ASN values
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR>
 * @version 1.0
 */
@SuppressForbidden
public class ASNValue implements java.io.Externalizable {
  private static final Logger logger = LoggerFactory.getLogger(ASNValue.class);

  /**
   *
   */
  private static final long serialVersionUID = 1L;

  private String sAsnObjectName = "Unknown";

  public ASNValue() {
  }

  ASNValue(String name) {
    this.sAsnObjectName = name;
  }

  // Misc functions
  static int countBits(long range) {
    int nBits = 0;
    while (range > (1L << nBits)) {
      nBits++;
    }
    return nBits;
  }

  static int countBits(long lower, long upper) {
    return countBits(upper - lower + 1);
  }

  static int countBits(int lower, int upper) {
    return countBits(upper - lower + 1);
  }

  public void decode(PERStream stream) throws ASNException {
  }

  public void decode(Element oElement) throws ASNException {
    throw new ASNException(
        "ASNValue.decode method should not be called. Too broad to do anything.");
  }

  public void encode(PERStream stream) throws ASNException {
  }

  public void encode(Element oElement) throws ASNException {
  }

  public Element encode() throws ASNException {
    Element dummy = new Element("dummy");

    encode(dummy);
    Element theElement = (Element) dummy.getChildren().get(0);

    theElement.detach();
    return theElement;
  }

  public long encodedSize() {
    return 0;
  }

  public String getAsnObjectName() {
    return sAsnObjectName;
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    // name = ( String)in.readObject();
  }

  public void setAsnObjectName(String name) {
    this.sAsnObjectName = name;
  }

  public String toString() {
    String sXML = "";
    try {
      Document oDoc = new Document(encode());
      Format format = Format.getPrettyFormat();
      format.setEncoding("ISO-8859-1");
      format.setOmitDeclaration(true);
      XMLOutputter op = new XMLOutputter(format);

      // encode( oDoc.getRootElement() );
      sXML = op.outputString(oDoc);
    } catch (ASNException ex) {
      logger.error("ASNException", ex);
    } catch (Exception e) {
      logger.error("Exception", e);
    }
    return sXML;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    // out.writeObject( name );
  }

  protected Class<?> getArgType(Method method) {
    Class<?> c = null;

    c = method.getParameterTypes()[0];

    return c;
  }

  protected Field getField(Element element) throws ASNException {
    Field field = null;

    String elementName = element.getName();
    String fieldName = "m_" + elementName;
    try {
      field = getClass().getDeclaredField(fieldName);
    } catch (NoSuchFieldException nsfe) {
      try {
        field = getClass().getDeclaredField(elementName);
      } catch (NoSuchFieldException e) {
        throw new ASNException(
            "No field called '" + fieldName + "' exists in ASN object '" + getAsnObjectName());
      }
    }

    return field;
  }

  protected Method getSetMethod(String attribute) {
    Method[] setMethods = getSetMethods();
    Method matchingMethod = null;

    String methodName = "set" + Character.toUpperCase(attribute.charAt(0)) + attribute.substring(1);
    int i = 0;
    boolean found = false;
    while (!found && (i < setMethods.length)) {
      if (setMethods[i].getName().equals(methodName)) {
        matchingMethod = setMethods[i];
      }

      i++;
    }

    return matchingMethod;
  }

  protected Method[] getSetMethods() {
    Method[] methods = getClass().getMethods();
    List<Method> setMethodList = new ArrayList<Method>();

    for (int i = 0; i < methods.length; i++) {
      // LOGGER.debug("method: " + methods[i].getName());
      if (methods[i].getName().startsWith("set")) {
        setMethodList.add(methods[i]);
      }
    }

    return setMethodList.toArray(new Method[0]);
  }

  ASNValue createObject() throws ASNException {
    return new ASNValue();
  }
}
