//
// AutoPingSetup.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vehiclepingservice.swap.v1.ping;

import org.jdom2.Element;

import com.volvo.tisp.vehiclepingservice.swap.common.ASNBoolean;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNException;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNInteger;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNSequence;
import com.volvo.tisp.vehiclepingservice.swap.common.PERStream;

/**
 * AutoPingSetup.
 */
public class AutoPingSetup extends ASNSequence {
  private static final long serialVersionUID = 1L;
  ASNInteger m_count = new ASNInteger();
  ASNBoolean m_exists__id = new ASNBoolean();
  ASNInteger m_id = new ASNInteger();
  ASNInteger m_interval = new ASNInteger();
  ASNBoolean m_pingResponseExpected = new ASNBoolean();

  public AutoPingSetup() {
    setAsnObjectName("AutoPingSetup");
    m_exists__id.setAsnObjectName("exists__id");
    m_interval.setAsnObjectName("interval");
    m_interval.setFixedConstraint(0L, 65335L);
    m_count.setAsnObjectName("count");
    m_count.setFixedConstraint(0L, 65335L);
    m_pingResponseExpected.setAsnObjectName("pingResponseExpected");
    m_id.setAsnObjectName("id");
    m_id.setFixedConstraint(0L, 4294967295L);
  }

  public void decode(PERStream stream) throws ASNException {
    super.decode(stream);
    m_exists__id.decode(stream);
    m_interval.decode(stream);
    m_count.decode(stream);
    m_pingResponseExpected.decode(stream);
    if (m_exists__id.get()) {
      m_id.decode(stream);
    }
  }

  public void encode(PERStream stream) throws ASNException {
    super.encode(stream);
    m_exists__id.encode(stream);
    m_interval.encode(stream);
    m_count.encode(stream);
    m_pingResponseExpected.encode(stream);
    if (m_exists__id.get()) {
      m_id.encode(stream);
    }
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_exists__id.encode(oElem);
    m_interval.encode(oElem);
    m_count.encode(oElem);
    m_pingResponseExpected.encode(oElem);
    if (m_exists__id.get())
      m_id.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_exists__id.encodedSize();
    length += m_interval.encodedSize();
    length += m_count.encodedSize();
    length += m_pingResponseExpected.encodedSize();
    if (m_exists__id.get())
      length += m_id.encodedSize();
    return length;
  }

  public long getCount() {
    return m_count.get();
  }

  public boolean getExists__id() {
    return m_exists__id.get();
  }

  public long getId() {
    return m_id.get();
  }

  public long getInterval() {
    return m_interval.get();
  }

  public boolean getPingResponseExpected() {
    return m_pingResponseExpected.get();
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_exists__id.readExternal(in);
    m_interval.readExternal(in);
    m_count.readExternal(in);
    m_pingResponseExpected.readExternal(in);
    if (m_exists__id.get()) {
      m_id.readExternal(in);
    }
  }

  public void setCount(long value) {
    m_count.set(value);
  }

  public void setExists__id(boolean value) {
    m_exists__id.set(value);
  }

  public void setId(long value) {
    m_id.set(value);
    m_exists__id.set(true);
  }

  public void setInterval(long value) {
    m_interval.set(value);
  }

  public void setPingResponseExpected(boolean value) {
    m_pingResponseExpected.set(value);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_exists__id.writeExternal(out);
    m_interval.writeExternal(out);
    m_count.writeExternal(out);
    m_pingResponseExpected.writeExternal(out);
    if (m_exists__id.get()) {
      m_id.writeExternal(out);
    }
  }
}

// End of AutoPingSetup.java
