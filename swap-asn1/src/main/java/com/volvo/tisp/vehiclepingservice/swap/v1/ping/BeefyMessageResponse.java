//
// BeefyMessageResponse.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vehiclepingservice.swap.v1.ping;

import java.io.UnsupportedEncodingException;

import org.jdom2.Element;

import com.volvo.tisp.vehiclepingservice.swap.common.ASNBoolean;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNException;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNInteger;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNOctetString;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNSequence;
import com.volvo.tisp.vehiclepingservice.swap.common.PERStream;

/**
 * BeefyMessageResponse.
 */
public class BeefyMessageResponse extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNBoolean m_exists__id = new ASNBoolean();
  ASNInteger m_id = new ASNInteger();
  ASNOctetString m_payload = new ASNOctetString();
  ASNOctetString m_status = new ASNOctetString();

  public BeefyMessageResponse() {
    setAsnObjectName("BeefyMessageResponse");
    m_exists__id.setAsnObjectName("exists__id");
    m_id.setAsnObjectName("id");
    m_id.setFixedConstraint(0L, 4294967295L);
    m_status.setAsnObjectName("status");
    m_status.setFixedConstraint(0L, 255L);
    m_payload.setAsnObjectName("payload");
    m_payload.setFixedConstraint(0L, 65335L);
  }

  public void decode(PERStream stream) throws ASNException {
    super.decode(stream);
    m_exists__id.decode(stream);
    if (m_exists__id.get()) {
      m_id.decode(stream);
    }
    m_status.decode(stream);
    m_payload.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    super.encode(stream);
    m_exists__id.encode(stream);
    if (m_exists__id.get()) {
      m_id.encode(stream);
    }
    m_status.encode(stream);
    m_payload.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_exists__id.encode(oElem);
    if (m_exists__id.get())
      m_id.encode(oElem);
    m_status.encode(oElem);
    m_payload.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_exists__id.encodedSize();
    if (m_exists__id.get())
      length += m_id.encodedSize();
    length += m_status.encodedSize();
    length += m_payload.encodedSize();
    return length;
  }

  public boolean getExists__id() {
    return m_exists__id.get();
  }

  public long getId() {
    return m_id.get();
  }

  public byte[] getPayload() {
    return m_payload.getAsByteArray();
  }

  public String getPayload(String charSetName) throws UnsupportedEncodingException {
    return m_payload.get(charSetName);
  }

  public String getPayloadAsString() {
    return m_payload.getAsString();
  }

  public byte[] getStatus() {
    return m_status.getAsByteArray();
  }

  public String getStatus(String charSetName) throws UnsupportedEncodingException {
    return m_status.get(charSetName);
  }

  public String getStatusAsString() {
    return m_status.getAsString();
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_exists__id.readExternal(in);
    if (m_exists__id.get()) {
      m_id.readExternal(in);
    }
    m_status.readExternal(in);
    m_payload.readExternal(in);
  }

  public void setExists__id(boolean value) {
    m_exists__id.set(value);
  }

  public void setId(long value) {
    m_id.set(value);
    m_exists__id.set(true);
  }

  public void setPayload(byte[] value) {
    m_payload.set(value);
  }

  public void setPayload(String value) {
    m_payload.set(value);
  }

  public void setPayload(String value, String charSetName) throws UnsupportedEncodingException {
    m_payload.set(value, charSetName);
  }

  public void setStatus(byte[] value) {
    m_status.set(value);
  }

  public void setStatus(String value) {
    m_status.set(value);
  }

  public void setStatus(String value, String charSetName) throws UnsupportedEncodingException {
    m_status.set(value, charSetName);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_exists__id.writeExternal(out);
    if (m_exists__id.get()) {
      m_id.writeExternal(out);
    }
    m_status.writeExternal(out);
    m_payload.writeExternal(out);
  }
}

// End of BeefyMessageResponse.java
