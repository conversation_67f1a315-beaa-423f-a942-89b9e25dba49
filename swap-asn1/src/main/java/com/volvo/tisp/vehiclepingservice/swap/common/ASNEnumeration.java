package com.volvo.tisp.vehiclepingservice.swap.common;

import java.util.Hashtable;
import java.util.List;

import org.jdom2.Element;
import org.jdom2.Text;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.lang.SuppressForbidden;

/**
 * Title: ASN Enumeration base class.
 *
 * <p>Description: Part of ASN
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR>
 * @version 1.0
 */
@SuppressForbidden
public class ASNEnumeration extends ASNInteger {
  private static final Logger logger = LoggerFactory.getLogger(ASNEnumeration.class);

  /**
   *
   */
  private static final long serialVersionUID = 1L;

  // protected long m_value;

  public ASNEnumeration() {
  }

  public ASNValue createObject() throws ASNException {
    return new ASNEnumeration();
  }

  public void decode(PERStream stream) throws ASNException {
    if (stream.isDebug()) {
      logger.debug("S " + stream.position() + " " + getAsnObjectName() + "\tASNEnumeration");
    }

    // TODO this should probably be done on top level (ASNConstrainedValue)
    if (isExtendeble()) {
      ASNBoolean extensionBit = new ASNBoolean();
      extensionBit.setAsnObjectName(getAsnObjectName() + ".extensionBit");
      extensionBit.decode(stream);
      setExtended(extensionBit.get());
    }

    m_value = lowBoundary + stream.decodeInteger(countBits(lowBoundary, highBoundary));

    if (stream.isDebug()) {
      logger.debug(
          "E "
              + stream.position()
              + " "
              + getAsnObjectName()
              + "\tASNEnumeration ("
              + m_value
              + ")");
    }
  }

  public void decode(Element oElement) throws ASNException {
    if (oElement.getName().equals(getAsnObjectName())) {
      List content = oElement.getContent();

      if (content.size() == 1) {
        Text enumValue = (Text) content.get(0);
        setValueAsString(enumValue.getText());
      } else {
        throw new ASNException(
            "Choice element '" + oElement.getName() + "' must have exactly one child!");
      }
    } else {
      throw new ASNException(
          "Element '"
              + oElement.getName()
              + "' can not be unserialized for ASN object of type '"
              + getAsnObjectName()
              + "'");
    }
  }

  public void encode(PERStream stream) throws ASNException {
    if (isExtendeble()) {
      ASNBoolean extensionBit = new ASNBoolean();
      extensionBit.setAsnObjectName(getAsnObjectName() + ".extensionBit");
      extensionBit.set(isExtended());
      extensionBit.encode(stream);
    }

    stream.encodeInteger(m_value - lowBoundary, countBits(lowBoundary, highBoundary));
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    oElem.addContent(getValueAsString());
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    int base = isExtendeble() ? 1 : 0;
    return base + countBits(lowBoundary, highBoundary);
  }

  public long get() {
    return m_value;
  }

  public String getValueAsString() {
    Integer key = Integer.valueOf((int) m_value);
    if (getValueMap().containsKey(key))
      return getValueMap().get(key);
    else
      return "UNDEFINED (" + key.toString() + ")";
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_value = in.readLong();
  }

  public void set(long value) {
    m_value = value;
  }

  public void setValueAsString(String newValue) throws ASNException {
    if (getNameMap().containsKey(newValue))
      set(getNameMap().get(newValue));
    else
      throw new ASNException("Enum string not found");
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    out.writeLong(m_value);
  }

  protected Hashtable<String, Integer> getNameMap() {
    return null;
  }

  protected Hashtable<Integer, String> getValueMap() {
    return null;
  }
}
