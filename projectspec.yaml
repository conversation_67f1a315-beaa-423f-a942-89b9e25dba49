version: 1.0
pipeline: ec2-opus-microservice
build:
  runtime: java
  version: corretto21
test:
  environmentComputeType: BUILD_GENERAL1_LARGE
deploy:
  test:
    VehicleCommunication:
      - eu-west-1
  iot1:
    VehicleCommunication:
      - eu-west-1
      - us-east-1
  qa:
    VehicleCommunication:
      - eu-west-1
      - us-east-1
    VehicleCommunication-IZ:
      - ap-northeast-1
  prod:
    VehicleCommunication:
      - eu-west-1
