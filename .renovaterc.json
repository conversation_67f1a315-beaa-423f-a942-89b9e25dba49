{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["local>vgcs/renovate-config", "group:all"], "reviewers": ["VGCS-VehicleCommunication"], "packageRules": [{"matchDatasources": ["maven"], "groupName": "all dependencies", "autoApprove": false, "automerge": false, "matchPackageNames": ["*"]}], "description": "Scheduled before 03:00AM on day-of-month 3 and 18.", "schedule": ["*  0-3 3,18 * *"]}